import 'package:flutter/material.dart';

import 'widget/notification_widget.dart';

class NotifyTaskManager {
  static final List<NotifyRequest> _queue = [];
  static bool _isShowing = false;

  static void show(NotifyRequest request) {
    _queue.add(request);
    _tryShowNext();
  }

  static void showMultiple(List<NotifyRequest?> requests) {
    var list = requests.whereType<NotifyRequest>();
    debugPrint("showMultiple: $list");
    _queue.addAll(list);
    _tryShowNext();
  }

  static void _tryShowNext() {
    if (_isShowing || _queue.isEmpty) return;

    _isShowing = true;
    final request = _queue.removeAt(0);

    late OverlayEntry entry;
    entry = OverlayEntry(
      builder: (_) => NotificationWidget(
        onTap: request.onTap,
        displayTime: request.displayTime,
        onClose: () async {
          // 安全移除并播放下一条
          try {
            if (entry.mounted) {
              entry.remove();
              request.onDismiss?.call();
            }
          } catch (e) {
            debugPrint("entry error: $e");
          }
          _isShowing = false;
          // ✅ 延迟一会再显示下一条
          await Future.delayed(const Duration(milliseconds: 600));
          _tryShowNext(); // 播放下一条
        },
        child: request.builder(request.context),
      ),
    );

    Overlay.of(request.context).insert(entry);
  }
}

class NotifyRequest {
  final BuildContext context;
  final WidgetBuilder builder;
  final Duration displayTime;
  final VoidCallback? onDismiss; // 通知关闭触发
  final VoidCallback? onTap; // 通知点击

  NotifyRequest({
    required this.context,
    required this.builder,
    this.onDismiss,
    this.displayTime = const Duration(seconds: 3),
    this.onTap,
  });
}
