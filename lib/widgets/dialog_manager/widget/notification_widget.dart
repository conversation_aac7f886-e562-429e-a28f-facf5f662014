import 'dart:async';

import 'package:flutter/material.dart';

class NotificationWidget extends StatefulWidget {
  final Widget child;
  final Duration displayTime;
  final VoidCallback onClose;
  final VoidCallback? onTap;

  const NotificationWidget({
    Key? key,
    required this.child,
    this.displayTime = const Duration(seconds: 3),
    required this.onClose,
    this.onTap,
  }) : super(key: key);

  @override
  NotificationWidgetState createState() => NotificationWidgetState();
}

class NotificationWidgetState extends State<NotificationWidget>
    with TickerProviderStateMixin {
  late final AnimationController _entranceController; // 控制入口 slide
  late final AnimationController _returnController; // 控制回弹 / 关闭动画
  Animation<double>? _returnAnimation;
  final GlobalKey _childKey = GlobalKey();

  double _dragOffset = 0.0; // 当前拖拽偏移（正为向下，负为向上）
  double _maxDrag = 0.0; // 通知高度（用于决定超过一半则关闭）
  Timer? _timer;
  bool _holding = false; // 手指是否按住
  bool _isClosing = false; // 正在关闭（避免重复触发）

  @override
  void initState() {
    super.initState();
    _entranceController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 280),
    );
    _returnController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 220),
    );

    // 在帧回调里测量高度并播放入口动画 & 启动定时器
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _maxDrag = _childKey.currentContext?.size?.height ?? 80.0;
      _entranceController.forward();
      _startTimer();
    });
  }

  void _startTimer() {
    _timer?.cancel();
    if (widget.displayTime <= Duration.zero) return;
    _timer = Timer(widget.displayTime, () {
      if (!_holding && !_isClosing) {
        _closeWithAnimation();
      }
    });
  }

  void _pauseTimer() {
    _timer?.cancel();
    _holding = true;
  }

  void _resumeTimer() {
    if (!_holding) return;
    _holding = false;
    _startTimer();
  }

  // 平滑回弹到 0
  void _animateBack() {
    _returnController.stop();
    _returnAnimation?.removeListener(_onReturnTick);
    _returnAnimation = Tween<double>(begin: _dragOffset, end: 0.0).animate(
        CurvedAnimation(parent: _returnController, curve: Curves.easeOut));
    _returnAnimation!.addListener(_onReturnTick);
    _returnController
      ..reset()
      ..forward().whenComplete(() {
        _returnAnimation?.removeListener(_onReturnTick);
      });
  }

  // 向上滑动并关闭（动画完成后移除 Overlay）
  void _closeWithAnimation({VoidCallback? onTap}) {
    if (_isClosing) return;
    _isClosing = true;
    _timer?.cancel();

    _returnController.stop();
    _returnAnimation?.removeListener(_onReturnTick);
    // 目标向上位移：取通知高度 + 20 的安全值（确保完全滑出）
    final double target = -(_maxDrag + 20.0);
    _returnAnimation = Tween<double>(begin: _dragOffset, end: target).animate(
        CurvedAnimation(parent: _returnController, curve: Curves.easeIn));
    _returnAnimation!.addListener(_onReturnTick);
    _returnController
      ..reset()
      ..forward().whenComplete(() {
        // 动画结束后移除 Overlay
        _returnAnimation?.removeListener(_onReturnTick);
        if (mounted) {
          widget.onClose();
          onTap?.call();
        }
        ;
      });
  }

  void _onReturnTick() {
    if (!mounted) return;
    setState(() {
      _dragOffset = _returnAnimation!.value;
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _entranceController.dispose();
    _returnController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: true,
      bottom: false,
      child: Align(
        alignment: Alignment.topCenter,
        child: SlideTransition(
          position: Tween<Offset>(begin: const Offset(0, -1), end: Offset.zero)
              .animate(CurvedAnimation(
                  parent: _entranceController, curve: Curves.easeOut)),
          child: Transform.translate(
            offset: Offset(0, _dragOffset),
            child: _buildGestureChild(),
          ),
        ),
      ),
    );
  }

  Widget _buildGestureChild() {
    // 只包裹通知本身区域，避免覆盖整屏（因此外部区域仍可点击）
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // 点击立即关闭（带动画）
        _closeWithAnimation(onTap: widget.onTap);
      },
      onPanDown: (_) {
        // 按下时暂停定时器
        _pauseTimer();
      },
      onPanUpdate: (details) {
        // 更新偏移：上滑（负值）直接响应；下拉（正值）采用阻尼
        setState(() {
          final dy = details.delta.dy;
          final newOffset = _dragOffset + dy;
          if (newOffset <= 0) {
            // 向上：直接跟随
            _dragOffset = newOffset;
          } else {
            // 向下：阻尼处理，限制最大下拉距离
            _dragOffset += dy * 0.06;
            const maxDown = 30.0;
            if (_dragOffset > maxDown) _dragOffset = maxDown;
          }
        });
      },
      onPanEnd: (details) {
        // 松手时判断是否超过一半，超过则关闭，否则回弹
        if (_dragOffset < 0 && _dragOffset.abs() > _maxDrag / 2) {
          _closeWithAnimation();
        } else {
          _animateBack();
          _resumeTimer();
        }
      },
      onPanCancel: () {
        _animateBack();
        _resumeTimer();
      },
      child: Material(
        color: Colors.transparent,
        child: Container(
          key: _childKey,
          // 推荐：将 child 包在 SafeArea (顶部) 内，或 child 自己包含 SafeArea
          child: widget.child,
        ),
      ),
    );
  }
}
