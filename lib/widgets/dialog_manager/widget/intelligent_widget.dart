import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

import '../../../app/provider/config/config_provider.dart';
import '../../../app/repository/modals/config/icon_config.dart';
import '../dialog_task_manager.dart';

/// 智能配置弹窗
/// iconConfig 智能弹窗数据
/// recordExposure 记录弹窗曝光
/// recordPopupNum 记录弹窗次数
/// recordHits 记录弹窗点击量
/// recordCloseHits 记录弹窗关闭点击量
/// recordUserClosePopUpIds 记录用户主动关闭的弹窗
class SmartDialogWidget extends ConsumerWidget {
  const SmartDialogWidget({
    super.key,
    required this.iconConfig,
    required this.recordExposure,
    required this.recordPopupNum,
    required this.recordHits,
    required this.recordCloseHits,
  });

  final void Function(int baseId) recordExposure;
  final void Function(int popUpScene, int popUpType) recordPopupNum;
  final void Function(int baseId) recordHits;
  final void Function(int baseId) recordCloseHits;

  final dynamic iconConfig;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    try {
      var baseId = iconConfig["id"];
      var popUpType = iconConfig["popUpType"];
      var popUpScene = iconConfig["popUpScene"];
      var popUpStyle = iconConfig["popUpStyle"];
      debugPrint("IntelligentDialog-popUpStyle: $popUpStyle");
      var popUpStyleJso = jsonDecode(popUpStyle);
      var picData = popUpStyleJso["picData"] as List?;
      debugPrint("IntelligentDialog-picData: $picData");
      // 显示的图片
      var picUrl = picData?.first["picUrl"] ?? "";
      // 点击参数
      var typeData = picData?.first["jumpUrl"];
      var jumpUrl = typeData["url"] ?? typeData["jumpUrl"];

      // 弹窗曝光
      if (baseId != null) {
        recordExposure(baseId);
      }
      if (popUpScene != null && popUpType != null) {
        recordPopupNum(popUpScene, popUpType);
      }

      return SizedBox(
        width: 286.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    SmartDialog.dismiss();
                    if (baseId != null) {
                      recordCloseHits(baseId);
                    }
                  },
                  child: Image.asset(
                    close,
                    width: 23.w,
                    height: 23.w,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 33.h,
            ),
            InkWell(
              onTap: () {
                SmartDialog.dismiss();

                // 清空弹窗队列
                DialogTaskManager().clean();
                if (baseId != null) {
                  recordHits(baseId);
                }

                // 配置跳转
                ref.read(configItemClickProvider.notifier).configItemClick(
                      IconConfig()
                        ..jumpUrl = jumpUrl
                        ..typeData = jsonEncode(typeData),
                    );
              },
              child: Image.network(
                picUrl,
                width: 295.w,
                errorBuilder: (context, o, s) {
                  return Container();
                },
              ),
            ),
            SizedBox(
              height: 46.h,
            ),
          ],
        ),
      );
    } catch (e) {
      return SizedBox(
        width: 286.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    SmartDialog.dismiss();
                  },
                  child: Image.asset(
                    close,
                    width: 23.w,
                    height: 23.w,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 33.h,
            ),
            SizedBox(height: 320.h),
          ],
        ),
      );
    }
  }
}

/// 智能配置飘窗
/// iconConfig 智能飘窗数据
class SmartNotificationWidget extends ConsumerWidget {
  const SmartNotificationWidget({super.key, required this.iconConfig});

  final dynamic iconConfig;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    try {
      var popUpStyle = iconConfig["popUpStyle"];
      debugPrint("IntelligentDialog-popUpStyle: $popUpStyle");
      var popUpStyleJso = jsonDecode(popUpStyle);
      var picData = popUpStyleJso["picData"] as List?;
      debugPrint("IntelligentDialog-picData: $picData");
      // 显示的图片
      var picUrl = picData?.first["picUrl"] ?? "";

      return Container(
        margin: const EdgeInsets.only(top: 10, left: 16, right: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Colors.black54,
              blurRadius: 6,
              offset: Offset(1, 1),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Image.network(picUrl),
        ),
      );
    } catch (e) {
      return const SizedBox();
    }
  }
}
