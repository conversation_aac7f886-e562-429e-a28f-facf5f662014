import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

// 弹窗优先级
enum DialogPriority {
  lowest(0),
  veryLow(1),
  low(2),
  belowNormal(3),
  normal(4),
  aboveNormal(5),
  high(6),
  veryHigh(7),
  critical(8),
  highest(9);

  final int value;

  const DialogPriority(this.value);
}

/// 任务
class DialogTask {
  final DialogPriority priority;
  final WidgetBuilder builder;
  final Duration delay;
  final bool clickMaskDismiss; // 点击mask是否关闭
  final VoidCallback? onDismiss; // 弹窗关闭触发
  final Completer<void> completer = Completer<void>();

  DialogTask({
    required this.builder,
    this.priority = DialogPriority.normal,
    this.delay = Duration.zero,
    this.clickMaskDismiss = true,
    this.onDismiss,
  });
}

/// 弹窗统一调度器
class DialogTaskManager {
  static final DialogTaskManager _instance = DialogTaskManager._internal();

  factory DialogTaskManager() => _instance;

  DialogTaskManager._internal();

  final SimplePriorityQueue<DialogTask> _dialogQueue = SimplePriorityQueue(
      (a, b) => b.priority.value.compareTo(a.priority.value));

  bool _isDialogShowing = false;

  /// 添加任务
  Future<void> showTask(DialogTask task) {
    return _enqueueDialog(task: task);
  }

  /// 添加多个任务
  Future<void> showMultipleTask(List<DialogTask?> taskList) {
    return _enqueueDialog(taskList: taskList);
  }

  /// 弹窗串行队列
  Future<void> _enqueueDialog({DialogTask? task, List<DialogTask?>? taskList}) {
    assert(task != null || taskList != null, '必须传入 task 或 taskList 其中之一');
    var t = task;
    if (taskList != null) {
      var newList = taskList.whereType<DialogTask>().toList();
      t = newList.first;
      _dialogQueue.addAll(newList);
    } else {
      t = task;
      _dialogQueue.add(t!);
    }
    _tryShowNextDialog();
    return t.completer.future;
  }

  /// 处理队列
  void _tryShowNextDialog() async {
    if (_isDialogShowing || _dialogQueue.isEmpty) return;

    final task = _dialogQueue.removeFirst();
    if (task.delay > Duration.zero) {
      await Future.delayed(task.delay);
    }

    _isDialogShowing = true;
    await SmartDialog.show(
      backType: SmartBackType.block,
      clickMaskDismiss: task.clickMaskDismiss,
      onDismiss: task.onDismiss,
      builder: (context) => task.builder(context),
    );

    task.completer.complete();
    _isDialogShowing = false;

    await Future.delayed(const Duration(milliseconds: 600));
    _tryShowNextDialog();
  }

  /// 清除弹窗队列
  void clean() {
    _dialogQueue.clear();
  }
}

class SimplePriorityQueue<T> {
  final int Function(T a, T b) compare;
  final List<T> _items = [];

  SimplePriorityQueue(this.compare);

  void add(T item) {
    _items.add(item);
    _items.sort(compare);
  }

  void addAll(List<T> items) {
    _items.addAll(items);
    _items.sort(compare);
  }

  /// 移除并返回优先级最高的元素
  T removeFirst() {
    return _items.removeAt(0);
  }

  void clear() {
    _items.clear();
  }

  /// 队列是否为空
  bool get isEmpty => _items.isEmpty;

  /// 队列大小
  int get length => _items.length;
}
