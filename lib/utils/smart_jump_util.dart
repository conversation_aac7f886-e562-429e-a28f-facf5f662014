import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/provider/conversion/link_conversion_provider.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_promotion.dart';
import 'package:msmds_platform/app/repository/service/chain_transfer_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/utils/toast_util.dart';
import 'package:msmds_platform/plugin/wechat/src/fluwx.dart';
import 'package:msmds_platform/plugin/wechat/src/foundation/arguments.dart';
import 'package:msmds_platform/plugin/wechat/src/wechat_enums.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../app/dialog/tb_auth_dialog.dart';
import '../app/navigation/coosea.dart';
import '../app/navigation/router.dart';
import '../plugin/alibc/alibc_ohos_plugin.dart';

/// 类型别名，兼容原有的跳转数据结构
typedef FunPromotionJump = Jump;

/// 跳转类型枚举 - 对应RN中的type
enum JumpType {
  internal(1), // 内部跳转
  webView(2), // 跳H5页面
  taobaoSdk(3), // SDK跳转淘宝
  jdSdk(4), // SDK跳转京东
  wechatShare(5), // 微信分享-动态配置参数
  miniProgram(6), // 跳小程序
  internalWithParams(7), // 内部跳转-动态配置参数
  broadcast(8), // 内部广播
  reserved9(9), // 保留
  taobaoActivity(10), // 淘宝跳转链接添加转链
  jdActivity(11), // 京东跳转链接添加转链
  eleActivity(12), // 饿了么买菜专用
  feedback(13), // 用户反馈
  lifeCoupon(14), // 必应鸟
  discountRadar(15), // 优惠雷达
  urlLauncher(16), // Linking跳转
  reserved17(17), // 保留
  vipShop(18), // 唯品会
  gameCenter(19), // 游戏中心
  mtActivity(20), // 美团活动
  wechatMini2(21), // 微信小程序2
  sunning(22), // 苏宁
  pddActivity(23), // 拼多多活动
  jdGoods(24), // 京东商品
  jdActivityGoods(25), // 京东活动商品
  mtGoods(26), // 美团商品
  reserved27(27), // 保留
  pddGoods(28), // 拼多多商品
  pddActivity2(29), // 拼多多活动2
  reserved30(30), // 保留
  reserved31(31), // 保留
  reserved32(32), // 保留
  didiChannel(33), // 滴滴转链，跳转小程序
  mtChannel(34), // 美团频道
  thirdParty(35), // 跳转活动转链(多麦，聚推客)
  pddPromo(36), // 拼多多推广
  douyin(37), // 抖音
  activityWeb(38); //  活动转链跳转小程序(多麦，聚推客)

  const JumpType(this.value);
  final int value;

  static JumpType? fromValue(int? value) {
    if (value == null) return null;
    try {
      return JumpType.values.firstWhere(
        (type) => type.value == value,
      );
    } catch (e) {
      return JumpType.urlLauncher;
    }
  }
}

/// 智能跳转工具类
/// 根据RN版本的smartJump逻辑转换为Flutter实现
class SmartJumpUtil {
  SmartJumpUtil._();

  /// 获取平台名称
  static String getPlatformName(JumpType? type) {
    switch (type) {
      case JumpType.taobaoSdk:
      case JumpType.taobaoActivity:
      case JumpType.eleActivity:
      case JumpType.discountRadar:
        return '淘宝';
      case JumpType.jdSdk:
      case JumpType.jdActivity:
      case JumpType.jdGoods:
      case JumpType.jdActivityGoods:
        return '京东';
      case JumpType.wechatShare:
      case JumpType.miniProgram:
      case JumpType.wechatMini2:
      case JumpType.didiChannel:
        return '微信';
      case JumpType.mtActivity:
      case JumpType.mtGoods:
      case JumpType.mtChannel:
        return '美团';
      case JumpType.vipShop:
        return '唯品会';
      case JumpType.sunning:
        return '苏宁';
      case JumpType.pddActivity:
      case JumpType.pddGoods:
      case JumpType.pddActivity2:
      case JumpType.pddPromo:
        return '拼多多';
      case JumpType.douyin:
        return '抖音';
      default:
        return '';
    }
  }

  /// 判断是否需要确认跳转弹窗
  static bool needsConfirmDialog(JumpType? type) {
    return type != null &&
        ![
          JumpType.internal,
          JumpType.webView,
          JumpType.internalWithParams,
          JumpType.broadcast,
          JumpType.feedback,
          JumpType.lifeCoupon,
          JumpType.urlLauncher,
          JumpType.gameCenter,
          JumpType.activityWeb,
        ].contains(type);
  }

  /// 智能跳转主方法
  static Future<void> smartJump({
    required WidgetRef ref,
    required BuildContext context,
    required FunPromotionJump jumpData,
    String? fallbackUrl,
    bool isReturnUrl = false,
    String? miniId, // 小程序 AppID，用于微信小程序跳转
  }) async {
    final jumpType = JumpType.fromValue(jumpData.type);
    final url = fallbackUrl;

    if (jumpType == null) {
      debugPrint('SmartJump: unknown jump type ${jumpData.type}');
      ToastUtil.showToast('不支持的跳转类型');
      return;
    }

    debugPrint('SmartJump: type=${jumpType.value}, url=$url');

    // 检查是否需要确认弹窗
    if (!isReturnUrl && needsConfirmDialog(jumpType)) {
      final platformName = getPlatformName(jumpType);
      if (platformName.isNotEmpty && context.mounted) {
        await _showConfirmDialog(
          context,
          platformName,
          () => _executeJump(ref, jumpData, jumpType, url, isReturnUrl, miniId),
        );
        return;
      }
    }

    // 直接执行跳转
    await _executeJump(ref, jumpData, jumpType, url, isReturnUrl, miniId);
  }

  /// 显示确认跳转弹窗
  static Future<void> _showConfirmDialog(
    BuildContext context,
    String platformName,
    VoidCallback onConfirm,
  ) async {
    if (!context.mounted) return;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: Text('即将离开买什么都省，打开$platformName'),
        actionsPadding:
            const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        actions: [
          // 移除外层 Expanded，直接使用 Row
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 20),
                    decoration: BoxDecoration(
                      color: const Color(0xFFEEEEEE),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Text(
                      '取消',
                      style: TextStyle(color: Color(0xFF5F5E5E)),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: InkWell(
                  onTap: () {
                    onConfirm();
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 20),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFE5640),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Text(
                      '允许',
                      style: TextStyle(color: Colors.white),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 执行具体跳转逻辑
  static Future<void> _executeJump(
    WidgetRef ref,
    FunPromotionJump jumpData,
    JumpType jumpType,
    String? url,
    bool isReturnUrl,
    String? miniId,
  ) async {
    debugPrint("SmartJump executing: type=${jumpType.value}, url=$url");
    debugPrint("SmartJump executing: jumpType=$jumpType, url=$url");
    if (url == null || url.isEmpty) {
      debugPrint('SmartJump: url is null or empty');
      ToastUtil.showToast('跳转链接为空');
      return;
    }

    try {
      switch (jumpType) {
        case JumpType.taobaoSdk:
        case JumpType.taobaoActivity:
        case JumpType.eleActivity:
        case JumpType.discountRadar:
          await _handleTaobaoJump(ref, jumpType, url);
          break;

        case JumpType.jdSdk:
        case JumpType.jdActivity:
        case JumpType.jdGoods:
        case JumpType.jdActivityGoods:
          await _handleJdJump(ref, jumpType, url);
          break;

        case JumpType.pddActivity:
        case JumpType.pddGoods:
        case JumpType.pddActivity2:
        case JumpType.pddPromo:
          await _handlePddJump(ref, jumpType, url);
          break;

        case JumpType.mtActivity:
        case JumpType.mtGoods:
        case JumpType.mtChannel:
          await _handleMeituanJump(ref, jumpType, url);
          break;

        case JumpType.vipShop:
          await _handleVipShopJump(ref, url);
          break;

        case JumpType.douyin:
          await _handleDouyinJump(ref, url);
          break;

        case JumpType.wechatShare:
        case JumpType.miniProgram:
        case JumpType.wechatMini2:
          await _handleWechatJump(jumpType, url, miniId);
          break;
        case JumpType.didiChannel:
          await _handleDidiJump(ref, jumpData, url, isReturnUrl);
          break;

        case JumpType.internal:
        case JumpType.webView:
        case JumpType.internalWithParams:
        case JumpType.broadcast:
        case JumpType.feedback:
        case JumpType.urlLauncher:
        case JumpType.activityWeb:
          await _handleOtherJump(jumpType, url);
          break;
        case JumpType.thirdParty:
          await _handleThirdPartyJump(ref, jumpData, url);
          break;
        default:
          ToastUtil.showToast('暂不支持该跳转类型');
          debugPrint('SmartJump: Unsupported jump type: $jumpType');
      }
    } catch (e) {
      debugPrint('SmartJump error: $e');
      ToastUtil.showToast('跳转失败，请稍后重试');
    }
  }

  /// 处理淘宝相关跳转
  static Future<void> _handleTaobaoJump(
    WidgetRef ref,
    JumpType jumpType,
    String url,
  ) async {
    switch (jumpType) {
      case JumpType.taobaoSdk:
        ref.read(tbConversionProvider.notifier).tbChangeUrlByGoodsId(url);
        break;
      case JumpType.taobaoActivity:
        await _handleTaobaoActivity(ref, url);
        break;
      case JumpType.eleActivity:
        ref.read(eleConversionProvider.notifier).changeUrlWithEleActivity(url);
        break;
      default:
        ToastUtil.showToast('淘宝跳转: $url');
    }
  }

  /// 处理淘宝活动跳转
  static Future<void> _handleTaobaoActivity(WidgetRef ref, String url) async {
    // 检查用户登录状态
    final userInfo = ref.read(authProvider);
    if (userInfo?.data == null) {
      debugPrint('_handleTaobaoActivity: 用户未登录');
      if (navigatorKey.currentContext != null) {
        Navigator.pushNamed(navigatorKey.currentContext!, CsRouter.login);
      }
      return;
    }

    if (url.isEmpty) {
      debugPrint('_handleTaobaoActivity: url为空');
      ToastUtil.showToast('跳转链接为空');
      return;
    }
    debugPrint('_handleTaobaoActivity: 开始处理淘宝活动跳转, url=$url');

    SmartDialog.showLoading(msg: "跳转中...");
    final result = await ChainTransferService.getTbActivityInfo(url);
    SmartDialog.dismiss();

    if (result.status == Status.completed) {
      try {
        final jsonData = result.data;
        debugPrint("_handleTaobaoActivity-jsonData: $jsonData");

        if (jsonData != null) {
          final AlibcFlutterPlugin aliPlugin = AlibcFlutterPlugin();
          await aliPlugin.launcherTbByUrl(jsonData);
        } else {
          ToastUtil.showToast('获取淘宝活动链接失败');
        }
      } catch (e) {
        debugPrint("_handleTaobaoActivity-启动淘宝失败: $e");
        ToastUtil.showToast('启动淘宝失败，请稍后重试');
      }
    } else {
      debugPrint(
          '_handleTaobaoActivity: API调用失败 - ${result.exception?.getMessage()}');

      if (result.exception?.getCode() == 5006) {
        // 唤起授权弹窗
        TbAuthDialog.authDialog(() {
          ref.read(tbConversionProvider.notifier).launchTbAuth();
        });
      } else {
        ToastUtil.showToast(result.exception?.getMessage() ?? '获取淘宝活动链接失败');
      }
    }
  }

  /// 处理京东相关跳转
  static Future<void> _handleJdJump(
    WidgetRef ref,
    JumpType jumpType,
    String url,
  ) async {
    switch (jumpType) {
      case JumpType.jdSdk:
      case JumpType.jdGoods:
        ref.read(jdConversionProvider.notifier).jdChangeUrl(url);
        break;
      case JumpType.jdActivity:
      case JumpType.jdActivityGoods:
        ref.read(jdConversionProvider.notifier).changeUrlWithJdActivityUrl(url);
        break;
      default:
        ToastUtil.showToast('京东跳转: $url');
    }
  }

  /// 处理拼多多相关跳转
  static Future<void> _handlePddJump(
    WidgetRef ref,
    JumpType jumpType,
    String url,
  ) async {
    debugPrint("SmartJump _handlePddJump: type=$jumpType, url=$url");
    switch (jumpType) {
      case JumpType.pddGoods:
        ref.read(pddConversionProvider.notifier).changeUrlWithPddSkuId(url);
        break;
      default:
        ToastUtil.showToast('拼多多跳转: $url');
    }
  }

  /// 处理美团相关跳转
  static Future<void> _handleMeituanJump(
      WidgetRef ref, JumpType jumpType, String url) async {
    switch (jumpType) {
      case JumpType.mtActivity: // type 20 美团活动
        // type 20 使用 getMtActivityInfo API，优先使用 commonLink，回退到 shortLink，构造美团深度链接
        ref.read(mtConversionProvider.notifier).mtActivityType20(url);
        break;
      case JumpType.mtGoods: // type 26 美团商品
        // type 26 使用 getMtActivityInfo API，只使用 commonLink，直接使用 deepLink 打开
        ref.read(mtConversionProvider.notifier).mtGoodsType26(url);
        break;
      case JumpType.mtChannel: // type 34 美团频道
        // type 34 使用 lmGenerateLink API，根据是否支持APP调整 linkType
        ref.read(mtConversionProvider.notifier).mtChannelWithGenerateLink(url);
        break;
      default:
        // 兼容其他美团相关跳转，使用原有方法
        ref.read(mtConversionProvider.notifier).changeUrlWithMtActivity(url);
        break;
    }
  }

  /// 处理唯品会跳转
  static Future<void> _handleVipShopJump(WidgetRef ref, String url) async {
    ref.read(wphConversionProvider.notifier).wphChangeUrl(url, url);
  }

  /// 处理抖音跳转
  static Future<void> _handleDouyinJump(WidgetRef ref, String url) async {
    ref.read(dyConversionProvider.notifier).dyChangeUrl(url, null);
  }

  /// 处理微信相关跳转
  static Future<void> _handleWechatJump(
    JumpType jumpType,
    String? url,
    String? miniId,
  ) async {
    switch (jumpType) {
      case JumpType.wechatShare:
        debugPrint('WeChat share: $url');
        ToastUtil.showToast('微信分享');
        break;
      case JumpType.miniProgram:
      case JumpType.wechatMini2:
        if (url == null || url.isEmpty) {
          ToastUtil.showToast('跳转路径为空');
          return;
        }
        if (miniId == null || miniId.isEmpty) {
          ToastUtil.showToast('小程序AppID为空');
          return;
        }
        await _launchMiniProgram(miniId, url);
        break;
      default:
        ToastUtil.showToast('微信跳转');
    }
  }

  /// 处理滴滴转链跳转
  static Future<void> _handleDidiJump(
    WidgetRef ref,
    FunPromotionJump jumpData,
    String? url,
    bool isReturnUrl,
  ) async {
    // 检查用户登录状态
    final userInfo = ref.read(authProvider);
    if (userInfo?.data == null) {
      debugPrint('_handleDidiJump: 用户未登录');
      ToastUtil.showToast('请先登录');
      return;
    }

    if (url == null || url.isEmpty) {
      debugPrint('_handleDidiJump: url为空');
      ToastUtil.showToast('跳转链接为空');
      return;
    }

    try {
      // 解析 jump 数据中的 promotionId 和 isMiniApp 参数
      // 这些参数通常存储在 jumpData 的额外字段中，需要通过 profile 或其他方式获取
      String? promotionId;
      bool isMiniApp = true; // 默认为小程序跳转

      // 尝试从 jumpData 中解析参数（如果有的话）
      if (jumpData.promotionId != null && jumpData.promotionId!.isNotEmpty) {
        try {
          promotionId = jumpData.promotionId;
          isMiniApp = jumpData.isMiniApp ?? true;
        } catch (e) {
          debugPrint('_handleDidiJump: 解析 profile 失败: $e');
        }
      }

      debugPrint(
          '_handleDidiJump: activityId=$url, isMiniApp=$isMiniApp, promotionId=$promotionId');

      // 调用滴滴转链API
      final result = await ChainTransferService.getDiDiActivityInfo(
        url,
        isMiniApp,
        promotionId,
      );

      if (result.status == Status.completed) {
        final responseData = result.data;
        final appSource = responseData?['data']?['appSource'];
        final link = responseData?['data']?['link'];

        debugPrint('_handleDidiJump: appSource=$appSource, link=$link');

        if (link == null || link.isEmpty) {
          ToastUtil.showToast('获取跳转链接失败');
          return;
        }

        if (isMiniApp) {
          // 小程序跳转
          if (isReturnUrl) {
            // 如果只是返回URL，不执行跳转
            return;
          }

          if (appSource != null && appSource.isNotEmpty) {
            await _launchMiniProgram(appSource, link);
          } else {
            ToastUtil.showToast('小程序AppID为空');
          }
        } else {
          // H5跳转
          if (isReturnUrl) {
            // 如果只是返回URL，不执行跳转
            return;
          }

          // 跳转到WebView页面
          debugPrint('_handleDidiJump: 跳转到H5页面: $link');
          if (navigatorKey.currentContext != null) {
            Navigator.pushNamed(
              navigatorKey.currentContext!,
              CsRouter.webPage,
              arguments: ["", link],
            );
          } else {
            ToastUtil.showToast('无法获取导航上下文');
          }
        }
      } else {
        debugPrint(
            '_handleDidiJump: API调用失败 - ${result.exception?.getMessage()}');
        ToastUtil.showToast(result.exception?.getMessage() ?? '网络请求失败');
      }
    } catch (e) {
      debugPrint('_handleDidiJump: 发生异常 - $e');
      ToastUtil.showToast('跳转失败，请稍后重试');
    }
  }

  /// 处理第三方H5活动跳转（多麦、聚推客等）
  static Future<void> _handleThirdPartyJump(
      WidgetRef ref, FunPromotionJump jumpData, String url) async {
    // 检查用户登录状态
    final userInfo = ref.read(authProvider);
    if (userInfo?.data == null) {
      debugPrint('_handleThirdPartyJump: 用户未登录');
      if (navigatorKey.currentContext != null) {
        Navigator.pushNamed(navigatorKey.currentContext!, CsRouter.login);
      }
      return;
    }

    if (url.isEmpty) {
      debugPrint('_handleThirdPartyJump: url为空');
      ToastUtil.showToast('跳转链接为空');
      return;
    }

    try {
      int? activityType;
      int? siteId;

      // 首先尝试从 jumpData.activityType 获取
      if (jumpData.activityType != null) {
        activityType = int.tryParse(jumpData.activityType!);
      }
      if (jumpData.siteId != null) {
        siteId = jumpData.siteId;
      }

      debugPrint(
          '_handleThirdPartyJump: activityType=$activityType, siteId=$siteId, url=$url');

      if (activityType == 1) {
        // 多麦
        if (siteId != null) {
          SmartDialog.showLoading(msg: "跳转中...");
          final result =
              await ChainTransferService.getDuoMaiActivityInfo(url, siteId);
          SmartDialog.dismiss();

          if (result.status == Status.completed) {
            final jsonData = result.data;
            final cpsShort = jsonData?["cpsShort"];

            if (navigatorKey.currentContext != null && cpsShort != null) {
              Navigator.pushNamed(
                navigatorKey.currentContext!,
                CsRouter.webPage,
                arguments: ["", cpsShort],
              );
            } else {
              ToastUtil.showToast('获取多麦活动链接失败');
            }
          } else {
            debugPrint(
                '_handleThirdPartyJump: 多麦API调用失败 - ${result.exception?.getMessage()}');
            ToastUtil.showToast(result.exception?.getMessage() ?? '获取多麦活动链接失败');
          }
        } else {
          ToastUtil.showToast('多麦站点ID为空');
        }
      } else if (activityType == 2) {
        // 聚推客
        debugPrint('_handleThirdPartyJump: 聚推客');
        SmartDialog.showLoading(msg: "跳转中...");
        final result = await ChainTransferService.getJuTuiKeActivityInfo(url);
        SmartDialog.dismiss();

        if (result.status == Status.completed) {
          final jsonData = result.data;
          final longH5 = jsonData?["longH5"];

          if (navigatorKey.currentContext != null && longH5 != null) {
            Navigator.pushNamed(
              navigatorKey.currentContext!,
              CsRouter.webPage,
              arguments: ["", longH5],
            );
          } else {
            ToastUtil.showToast('获取聚推客活动链接失败');
          }
        } else {
          debugPrint(
              '_handleThirdPartyJump: 聚推客API调用失败 - ${result.exception?.getMessage()}');
          ToastUtil.showToast(result.exception?.getMessage() ?? '获取聚推客活动链接失败');
        }
      } else {
        ToastUtil.showToast('不支持的活动类型: $activityType');
      }
    } catch (e) {
      SmartDialog.dismiss();
      debugPrint('_handleThirdPartyJump: 发生异常 - $e');
      ToastUtil.showToast('跳转失败，请稍后重试');
    }
  }

  /// 统一的微信小程序跳转方法
  static Future<void> _launchMiniProgram(String username, String path) async {
    try {
      final Fluwx fluwx = Fluwx();
      var isWeChatInstalled = await fluwx.isWeChatInstalled;

      if (isWeChatInstalled) {
        debugPrint('跳转到微信小程序: username=$username, path=$path');

        await fluwx.open(
          target: MiniProgram(
            username: username,
            path: path,
            miniProgramType: WXMiniProgramType.release,
          ),
        );
      } else {
        ToastUtil.showToast("微信未安装或版本过低");
      }
    } catch (e) {
      debugPrint('跳转小程序失败: $e');
      ToastUtil.showToast('跳转失败，请稍后重试');
    }
  }

  /// 处理其他类型跳转
  static Future<void> _handleOtherJump(JumpType jumpType, String url) async {
    switch (jumpType) {
      case JumpType.internal:
        debugPrint('Internal jump: $url');
        ToastUtil.showToast('内部跳转: $url');
        break;
      case JumpType.webView:
        debugPrint('WebView jump: $url');
        if (navigatorKey.currentContext == null) return;
        Navigator.pushNamed(
          navigatorKey.currentContext!,
          CsRouter.webPage,
          arguments: ["", url, false],
        );
        break;
      case JumpType.internalWithParams:
        debugPrint('Internal jump with params: $url');
        // ToastUtil.showToast('内部跳转(带参数): $url');
        break;
      case JumpType.broadcast:
        debugPrint('Broadcast: $url');
        ToastUtil.showToast('内部广播: $url');
        break;
      case JumpType.feedback:
        debugPrint('User feedback');
        ToastUtil.showToast('用户反馈');
        break;
      case JumpType.urlLauncher:
        debugPrint('URL launcher: $url');
        ToastUtil.showToast('外部链接跳转: $url');
        break;
      case JumpType.activityWeb:
        debugPrint('Activity web jump: $url');
        ToastUtil.showToast('活动网页跳转: $url');
        break;
      default:
        ToastUtil.showToast('其他跳转: $url');
    }
  }
}
