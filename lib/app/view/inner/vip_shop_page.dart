import 'dart:async';

import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

import '../../../common/widgets/appbar/leading.dart';
import '../../provider/config/config_provider.dart';
import '../../provider/config/inner_provider.dart';
import '../../repository/modals/config/icon_config.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.inner
/// @ClassName: vip_shop_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/21 15:36
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/21 15:36
/// @UpdateRemark: 更新说明
class VipShopPage extends ConsumerStatefulWidget {
  const VipShopPage({super.key});

  @override
  VipShopPageState createState() => VipShopPageState();
}

class VipShopPageState extends ConsumerState<VipShopPage> {
  IconConfig? iconConfig;

  // 返现流程
  Widget _buildSavingSetup() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.w),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: const Color(0xFFFCD8ED),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: Column(
          children: [
            SizedBox(height: 16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 67.w,
                  height: 6.h,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(0xFFFFFFFF),
                        Color(0xFFFFD9FF),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  "返现流程",
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2E3032),
                  ),
                ),
                SizedBox(width: 8.w),
                Container(
                  width: 67.w,
                  height: 6.h,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(0xFFFFD9FF),
                        Color(0xFFFFFFFF),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 10.h),
            Text(
              "订单签收后最快第8天可获得返现入账",
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF7C7C7C),
              ),
            ),
            const SetupWidget(),
          ],
        ),
      ),
    );
  }

  // 返现规则说明
  Widget _buildSavingRule() {
    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 16.h, 10.w, 16.h),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: const Color(0xFFFCD8ED),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: Column(
          children: [
            SizedBox(height: 18.h),
            Stack(
              alignment: Alignment.center,
              children: [
                Image.network(
                  "${Constant.msmdsAliCdn}/appNormal/wphRulesText.png",
                  width: 200.w,
                  height: 16.h,
                ),
                Text(
                  "返现规则说明",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF2E3032),
                  ),
                ),
              ],
            ),
            SizedBox(height: 15.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Text(
                "1、通过「买什么都省」APP前往唯品会进行下单，最高返利比例为8% (最终返利以消息推送为准，返利与商品实际价格相关，不包括运费、税费等) ，手机、奶粉、旅行频道、汽车频道暂无返现，购买唯品会金融产品、购买唯品卡暂无返现，购买投资类金条无返现；在唯品会H5或小程序下单可能无返现，建议下载唯品会app下单；\n2、用户通过「买什么都省」APP下单后，30分钟内即可查看返现订单，订单确认签收完成后最快第8天即可获得返现金额；\n3、返现订单在【我的订单】-【唯品会】中查看，返现账户在【我的】里面查看 ;\n4、发生整单退货和换货的订单整单无返现，申请部分退款和部分换货的订单，退换货部分无返现，其他商品按照原订单实付金额计算返现；\n5、使用唯品币、优惠券、唯品会卡下单付款的订单可能会导致没有返现，仅有通过「买什么都省」APP进行购买的有效订单才可获得返现;\n如有更多疑问，请联系「买什么都省」客服。",
                style: TextStyle(
                  fontSize: 12.sp,
                  height: 2,
                  color: const Color(0xFF2E3032),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        toolbarHeight: 44.h,
        elevation: 0,
        centerTitle: true,
        title: Text(
          "唯品会",
          style: TextStyle(
            fontSize: 16.sp,
          ),
        ),
        backgroundColor: Colors.white,
        leading: const Leading(),
      ),
      body: Column(
        children: [
          Expanded(
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: VipShopSwiper(
                    onIndexChanged: (config) {
                      setState(() {
                        iconConfig = config;
                      });
                    },
                  ),
                ),
                SliverToBoxAdapter(
                  child: _buildSavingSetup(),
                ),
                SliverToBoxAdapter(
                  child: _buildSavingRule(),
                ),
              ],
            ),
          ),
          Container(
            color: Colors.white,
            child: Column(
              children: [
                SizedBox(height: 10.h),
                GradientButton(
                  onPress: () {
                    if (iconConfig != null) {
                      ref
                          .read(configItemClickProvider.notifier)
                          .configItemClick(iconConfig!);
                    } else {
                      var configList =
                          ref.read(fetchVipShopSwiperConfigProvider).value;
                      if (configList != null && configList.isNotEmpty) {
                        ref
                            .read(configItemClickProvider.notifier)
                            .configItemClick(configList.first);
                      }
                    }
                  },
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  margin: EdgeInsets.symmetric(horizontal: 10.w),
                  radius: 25.r,
                  shadow: false,
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFFFB0AFF),
                      Color(0xFFFF5100),
                    ],
                  ),
                  child: Text(
                    "去唯品会下单拿返现",
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(
                  height: MediaQuery.of(context).padding.bottom == 0
                      ? 10.h
                      : MediaQuery.of(context).padding.bottom,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class VipShopSwiper extends ConsumerWidget {
  const VipShopSwiper({
    super.key,
    required this.onIndexChanged,
  });

  final Function(IconConfig config) onIndexChanged;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(fetchVipShopSwiperConfigProvider).when(
      data: (data) {
        if (data == null || data.isEmpty) {
          return Container();
        }
        return SwiperWidget(
          list: data,
          onIndexChanged: onIndexChanged,
        );
      },
      error: (o, s) {
        return Container();
      },
      loading: () {
        return Container();
      },
    );
  }
}

class SwiperWidget extends ConsumerStatefulWidget {
  const SwiperWidget({
    super.key,
    required this.list,
    required this.onIndexChanged,
  });

  final List<IconConfig> list;

  final Function(IconConfig config) onIndexChanged;

  @override
  SwiperWidgetState createState() => SwiperWidgetState();
}

class SwiperWidgetState extends ConsumerState<SwiperWidget> {
  double? _swiperHeight;

  @override
  void initState() {
    super.initState();
    _initializeSwiperHeight();
  }

  void _initializeSwiperHeight() async {
    try {
      var item = widget.list.first;
      var result = await getImageDimensions(item.pictureUrl ?? "");
      final aspectRatio = result.width / result.height;
      setState(() {
        _swiperHeight = 355.w / aspectRatio;
      });
    } catch (e) {
      setState(() {
        _swiperHeight = 100.h;
      });
    }
  }

  // 获取图片尺寸
  Future<Size> getImageDimensions(String url) {
    final ImageProvider provider = NetworkImage(url);

    final Completer<Size> completer = Completer();

    final ImageStreamListener listener = ImageStreamListener(
      (image, synchronousCall) {
        final size = Size(
          image.image.width.toDouble(),
          image.image.height.toDouble(),
        );

        completer.complete(size);
      },
      onError: (e, s) {
        completer.completeError(e, s);
      },
    );

    final ImageStream stream = provider.resolve(ImageConfiguration.empty);

    stream.addListener(listener);

    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    if (_swiperHeight != null) {
      return Container(
        width: 355.w,
        height: _swiperHeight,
        margin: EdgeInsets.only(top: 12.h, bottom: 12.h),
        child: Swiper(
          autoplay: true,
          loop: widget.list.length > 1,
          itemBuilder: (BuildContext context, int index) {
            var item = widget.list[index];
            return ClipRRect(
              borderRadius: BorderRadius.circular(6.r),
              child: InkWell(
                onTap: () {
                  ref
                      .read(configItemClickProvider.notifier)
                      .configItemClick(item);
                },
                child: Image.network(
                  item.pictureUrl ?? "",
                  fit: BoxFit.contain,
                  errorBuilder: (c, o, s) {
                    return Container();
                  },
                ),
              ),
            );
          },
          onIndexChanged: (int index) {
            var item = widget.list[index];
            widget.onIndexChanged.call(item);
          },
          itemCount: widget.list.length,
          pagination: SwiperCustomPagination(builder: (context, config) {
            if (widget.list.length <= 1) {
              return const SizedBox();
            }
            return Positioned(
              bottom: 10.h,
              right: 20.w,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 1.h),
                decoration: BoxDecoration(
                  color: const Color(0x99000000),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  "${config.activeIndex + 1}/${config.itemCount}",
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            );
          }),
        ),
      );
    }
    return Container();
  }
}

// 返现流程
class SetupWidget extends StatelessWidget {
  const SetupWidget({super.key});

  Widget _buildItem(String img, String title) {
    return Column(
      children: [
        Image.network(img, width: 43.w, height: 43.w),
        SizedBox(height: 10.h),
        Text(
          title,
          style: TextStyle(
            fontSize: 10.sp,
            color: const Color(0xFFFF008A),
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _segmentation() {
    return Padding(
      padding: EdgeInsets.only(bottom: 18.h),
      child: Wrap(
        spacing: 2.w,
        children: List.generate(
          4,
          (index) => Container(
            width: 4.w,
            height: 4.w,
            decoration: BoxDecoration(
              color: const Color(0xFFFF008A),
              borderRadius: BorderRadius.circular(2.w),
            ),
          ),
        ).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      margin: EdgeInsets.symmetric(vertical: 16.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildItem("${Constant.msmdsAliCdn}/appNormal/wph1.png", "下单购买"),
          _segmentation(),
          _buildItem("${Constant.msmdsAliCdn}/appNormal/wph2.png", "确认收货"),
          _segmentation(),
          _buildItem("${Constant.msmdsAliCdn}/appNormal/wph3.png", "返现入账"),
          _segmentation(),
          _buildItem("${Constant.msmdsAliCdn}/appNormal/wph4.png", "提取现金"),
        ],
      ),
    );
  }
}
