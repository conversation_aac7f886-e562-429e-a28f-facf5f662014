import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:msmds_platform/app/provider/fun/bawangcan_registration_provider.dart';
import 'package:msmds_platform/app/repository/modals/fun/bawangcan_registration_record.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/bawangcan_registration_record_item.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

/// 霸王餐报名记录页面
///
/// 功能包括：
/// - 显示用户的霸王餐活动报名记录列表
/// - 支持按状态筛选（全部、已报名、已取消、已过期、已下单）
/// - 支持下拉刷新和上拉加载更多
/// - 支持取消报名、前往下单、前往评论等操作
class BawangcanRegistrationRecordPage extends ConsumerStatefulWidget {
  const BawangcanRegistrationRecordPage({super.key});

  @override
  ConsumerState<BawangcanRegistrationRecordPage> createState() =>
      _BawangcanRegistrationRecordPageState();
}

class _BawangcanRegistrationRecordPageState
    extends ConsumerState<BawangcanRegistrationRecordPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: bawangcanRegistrationRecordTabs.length,
      vsync: this,
      initialIndex: 1, // 默认选中"已报名"标签页
    );

    // 监听标签页切换
    _tabController.addListener(_onTabChanged);

    // 初始加载数据（选中"已报名"标签页的数据）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(bawangcanRegistrationProvider.notifier).selectTab(1);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    final currentIndex = _tabController.index;
    ref.read(bawangcanRegistrationProvider.notifier).selectTab(currentIndex);
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(bawangcanRegistrationProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFF4F4F2),
      appBar: AppBar(
        title: const Text('报名记录'),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.black,
          unselectedLabelColor: const Color(0xFF666666),
          indicatorSize: TabBarIndicatorSize.label,
          indicator: UnderlineTabIndicator(
            borderRadius: BorderRadius.all(Radius.circular(5.r)),
            borderSide: BorderSide(width: 3.w, color: const Color(0xFFF93324)),
            insets: EdgeInsets.symmetric(horizontal: 30.w, vertical: 8.w),
          ),
          labelStyle: TextStyle(
            fontSize: 14.sp,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.normal,
          ),
          tabs: bawangcanRegistrationRecordTabs
              .map((tab) => Tab(text: tab.tabName))
              .toList(),
        ),
      ),
      body: Column(
        children: [
          _buildNoticeHeader(),
          Expanded(
            child: _buildRecordList(state),
          ),
        ],
      ),
    );
  }

  /// 构建报名记录列表
  Widget _buildRecordList(BawangcanRegistrationState state) {
    return CustomListView<BawangcanRegistrationRecord>(
      onRefresh: () async {
        await ref.read(bawangcanRegistrationProvider.notifier).refreshRecords();
      },
      onLoadMore: () async {
        await ref.read(bawangcanRegistrationProvider.notifier).loadMoreRecords();
      },
      data: state.records,
      renderItem: (context, index, record) {
        return BawangcanRegistrationRecordItem(
          record: record,
          onCancelRegistration: () {
             ref
                .read(bawangcanRegistrationProvider.notifier)
                .cancelRegistration(record);
            
          },
          onGoToOrder: () => ref.read(bawangcanRegistrationProvider.notifier).goToPlaceOrder(
                context: context,
                ref: ref,
                applyType: record.applyOrderShop?.applyType ?? 0,
                wxPath: record.applyOrderShop?.actionUrl?.wxPath,
                activityId: record.activityId,
                shopId: record.shopId,
              ),
          onGoToComment: () => ref.read(bawangcanRegistrationProvider.notifier).goToComment(
                context: context,
                ref: ref,
                applyType: record.applyOrderShop?.applyType ?? 0,
              ),
        );
      },
      footerState: _getLoadState(state),
      empty: _buildEmptyState(),
      padding: EdgeInsets.all(12.w),
    );
  }

  /// 根据状态获取加载状态
  LoadState _getLoadState(BawangcanRegistrationState state) {
    if (state.isLoadingMore) {
      return LoadState.loading;
    } else if (!state.hasMore && state.records.isNotEmpty) {
      return LoadState.noMore;
    } else {
      return LoadState.idle;
    }
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CachedNetworkImage(
            imageUrl: '${Constant.msmdsAliCdn}/APPSHOW/zwbmjl_img.png',
            width: 180.w,
            height: 500.w,
            placeholder: (context, url) => Container(
              width: 120.w,
              height: 120.w,
              color: Colors.grey[200],
            ),
            errorWidget: (context, url, error) => Container(
              width: 120.w,
              height: 120.w,
              color: Colors.grey[200],
              child: const Icon(Icons.not_interested_rounded),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建提示信息头部
  Widget _buildNoticeHeader() {
    return Container(
      width: double.infinity,
      height: 71.h,
      color: const Color(0xFFFBF0ED),
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      alignment: Alignment.centerLeft,
      child: Text(
        '1.当天18点前提交的订单都将在当天审完，18点之后的次日审完\n'
        '2.禁止预定单和以任何形式向商家索要返利/红包以及同一店铺多平台返利，一经发现全网通报拉黑！',
        style: TextStyle(
          fontSize: 10.sp,
          color: const Color(0xFFF73402),
          height: 18.sp / 10.sp, // 行高 = lineHeight / fontSize
        ),
      ),
    );
  }

}
