import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/config/config_provider.dart';
import 'package:msmds_platform/app/view/home/<USER>/delegate/main_persistent_header.dart';
import 'package:msmds_platform/app/view/home/<USER>/skeleton/main_skeleton.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/login_tip_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/main_icons.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/main_informer_item.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/main_porcelain.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/main_swiper.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/main_tab_pkg.dart';
import 'package:msmds_platform/common/widgets/delegate/persistent_builder.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

import '../../../../config/global_config.dart';
import '../../../provider/home/<USER>';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: main_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/10 14:06
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/10 14:06
/// @UpdateRemark: 更新说明
class MainPage extends ConsumerStatefulWidget {
  const MainPage({super.key});

  @override
  MainPageState createState() => MainPageState();
}

class MainPageState extends ConsumerState<MainPage>
    with SingleTickerProviderStateMixin {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) async {
        /// 打开app时执行粘贴板识别
        if (GlobalConfig.account != null) {
          var result =
              await ref.read(convertContentProvider.notifier).convert();
          if (result) {
            // 没有识别弹窗就请求智能弹窗
            ref.read(configSmartDialogProvider.notifier).showMainSmartDialog();
          }
        }
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: AnnotatedRegion(
        value: const SystemUiOverlayStyle(
          statusBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.dark,
          systemNavigationBarContrastEnforced: false,
        ),
        child: SafeArea(
          child: Stack(
            children: [
              CustomListView(
                onRefresh: () async {
                  ref.refresh(fetchMainIconConfigProvider).unwrapPrevious();
                  await ref.read(homeTabPkgGoodsProvider.notifier).loadData();
                },
                onLoadMore: () async {
                  await ref.read(homeTabPkgGoodsProvider.notifier).loadMore();
                },
                sliverHeader: [
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: MainPersistentHeader(
                      max: 85.h,
                      min: 45.h,
                      scrollController: _scrollController,
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Column(
                      children: const [
                        // SubscribeReminders(),
                        MainSwiper(),
                        MainIcons(),
                        MainPorcelain(),
                        // ZeroPurchaseEntrance(),
                        // MainActivitySection(),
                        // MainFloatToggle(),
                      ],
                    ),
                  ),
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: PersistentBuilder(
                      max: 40.h,
                      min: 40.h,
                      builder: (_, offset) {
                        return const MainTabPkg();
                      },
                    ),
                  ),
                ],
                data: ref.watch(
                  homeTabPkgGoodsProvider.select((value) => value.goods),
                ),
                footerState: ref.watch(
                  homeTabPkgGoodsProvider.select((value) => value.loadState),
                ),
                renderItem: (context, index, o) {
                  return MainInformerItem(
                    index: index + 1,
                    goodsTbPkg: o,
                  );
                },
                empty: const MainSkeleton(),
              ),
              const Positioned(bottom: 0, child: LoginTipWidget()),
              // Positioned(right: 0, bottom: 155.h, child: const MainSidebar()),
            ],
          ),
        ),
      ),
    );
  }
}
