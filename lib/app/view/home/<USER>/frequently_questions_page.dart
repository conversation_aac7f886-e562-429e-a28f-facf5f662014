import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FrequentlyQuestionsPage extends ConsumerWidget {
  const FrequentlyQuestionsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final questionStyle = TextStyle(
      fontSize: 13.sp,
      color: const Color(0xFF333333),
      fontWeight: FontWeight.w700,
      height: 1.6,
    );
    final answerStyle = TextStyle(
      fontSize: 13.sp,
      color: const Color(0xFF333333),
      height: 1.45,
    );
    final highlightStyle = answerStyle.copyWith(
      color: const Color(0xFFFF7B00),
      fontWeight: FontWeight.w600,
    );

    return Scaffold(
      backgroundColor: const Color(0xFFF4F4F2),
      appBar: AppBar(
        title: const Text('常见问题'),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(bottom: 60.h),
        child: Align(
          alignment: Alignment.topCenter,
          child: Container(
            width: 355.w,
            margin: EdgeInsets.only(top: 8.h),
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 16.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '霸王餐常见问题',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                SizedBox(height: 10.h),
                Text(
                  '1、报名抢单后，不下单会过期吗？',
                  style: questionStyle,
                ),
                SizedBox(height: 4.h),
                Text.rich(
                  TextSpan(
                    style: answerStyle,
                    children: [
                      const TextSpan(text: '报名后'),
                      TextSpan(
                        text: '30分钟内必须完成下单',
                        style: highlightStyle,
                      ),
                      const TextSpan(text: '，超时则报名自动取消，下单无返现。'),
                    ],
                  ),
                ),
                SizedBox(height: 10.h),
                Text(
                  '2、报名次数有限制吗？',
                  style: questionStyle,
                ),
                SizedBox(height: 4.h),
                Text(
                  '①同⼀个商家的活动：同⼀个⽤户当⽇只允许报名下单1次同⼀个商家的活动，每周最多可报名下单 3 次同⼀个商家的活动；\n'
                  '②如果商家有多个活动(订单、评价)，⽤户只能报名其中⼀个；\n'
                  '③每日报名次数：同一个⽤户当⽇最多可报名 5 次，报名后又取消也视为报名1次；',
                  style: answerStyle,
                ),
                SizedBox(height: 10.h),
                Text(
                  '3、下单有什么注意事项吗？',
                  style: questionStyle,
                ),
                SizedBox(height: 4.h),
                Text.rich(
                  TextSpan(
                    style: answerStyle,
                    children: [
                      const TextSpan(text: '①'),
                      TextSpan(
                        text: '报名手机号和下单手机号必须一致',
                        style: highlightStyle,
                      ),
                      const TextSpan(text: '，否则无返现；\n②实付金额有要求的商家，'),
                      TextSpan(
                        text: '实付不满不能参与返现',
                        style: highlightStyle,
                      ),
                      const TextSpan(
                        text:
                            '（实付金额不包括券包、买会员产生的金额，若发⽣部分售后则剩余⾦额须满⾜最低实付⻔槛）；\n③需要图文反馈/用餐反馈/评价反馈的订单：',
                      ),
                      TextSpan(
                        text: '最晚在下单次日11点前提交反馈',
                        style: highlightStyle,
                      ),
                      const TextSpan(
                        text:
                            '，否则无返现；\n④需要反馈类的订单，未按活动要求提交用餐评价反馈（评价内容以首次评价为准，后续修改评价视为无效），导致被平台审核失败的，则返现取消；\n⑤',
                      ),
                      TextSpan(
                        text: '饿了么霸王餐，请勿使用“叠红包”',
                        style: highlightStyle,
                      ),
                      const TextSpan(text: '，否则无返现。'),
                    ],
                  ),
                ),
                SizedBox(height: 10.h),
                Text(
                  '4、无法获取我的位置，怎么办？',
                  style: questionStyle,
                ),
                SizedBox(height: 4.h),
                Text(
                  '若APP出现无法获取定位的情况，请检查是否开启手机的GPS定位服务，或切换网络之后重试。',
                  style: answerStyle,
                ),
                SizedBox(height: 10.h),
                Text(
                  '5、报名后找不到店铺怎么办？',
                  style: questionStyle,
                ),
                SizedBox(height: 4.h),
                Text(
                  '报名后在饿了么/美团外卖平台找不到门店，则店家不在配送范围，请及时取消报名。恶劣天气影响，门店配送范围会自动缩小，可稍等后再下单。',
                  style: answerStyle,
                ),
                SizedBox(height: 10.h),
                Text(
                  '6、霸王餐返现什么时候可提现？',
                  style: questionStyle,
                ),
                SizedBox(height: 4.h),
                Text(
                  '无需评价类订单，订单完成收货后，一般当天可提现返现；订单完成评价反馈类订单，订单完成评价反馈后，如反馈审核成功，则一般当天可提现返现，若反馈审核失败，则返现取消。',
                  style: answerStyle,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
