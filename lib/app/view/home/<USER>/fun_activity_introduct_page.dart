import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:video_player/video_player.dart';

/// 吃喝玩乐视频教程页面
class FunActivityIntroductPage extends StatefulWidget {
  const FunActivityIntroductPage({super.key});

  @override
  State<FunActivityIntroductPage> createState() =>
      _FunActivityIntroductPageState();
}

class _FunActivityIntroductPageState extends State<FunActivityIntroductPage> {
  /// 视频播放器控制器
  VideoPlayerController? _controller;
  
  /// 是否显示控制栏
  bool _showControls = true;

  /// 是否正在加载
  bool _isLoading = true;
  
  /// 是否播放完成
  bool _isComplete = false;

  /// 视频 URL
  final String _videoUrl = '${Constant.msmdsAliCdn}/bawangcan/xinshouappbwc.mp4';
  
  @override
  void initState() {
    super.initState();
    _initVideoPlayer();
  }
  
  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
  
  /// 初始化视频播放器
  Future<void> _initVideoPlayer() async {
    try {
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(_videoUrl),
      );
      
      await _controller!.initialize();
      
      // 监听播放完成
      _controller!.addListener(() {
        if (_controller!.value.position >= _controller!.value.duration) {
          if (!_isComplete) {
            setState(() {
              _isComplete = true;
            });
          }
        }
      });
      
      setState(() {
        _isLoading = false;
      });
      
      // 自动播放
      _controller!.play();
      
      // 显示控制栏并自动隐藏
      _showControlsTemporarily();
    } catch (e) {
      debugPrint('视频初始化失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // 视频播放区域
          _buildVideoPlayer(),

          // 顶部控制栏
          if (_showControls) _buildTopBar(),
          
          // 底部控制栏
          if (_showControls && _controller != null) _buildBottomControls(),

          // 加载指示器
          if (_isLoading) _buildLoadingIndicator(),
        ],
      ),
    );
  }

  /// 构建视频播放器
  Widget _buildVideoPlayer() {
    if (_controller == null || !_controller!.value.isInitialized) {
      return const SizedBox.expand();
    }
    
    return GestureDetector(
      onTap: _toggleControls,
      child: Center(
        child: AspectRatio(
          aspectRatio: _controller!.value.aspectRatio,
          child: VideoPlayer(_controller!),
        ),
      ),
    );
  }

  /// 构建顶部控制栏
  Widget _buildTopBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top + 10.w,
          left: 20.w,
          right: 20.w,
          bottom: 10.w,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withOpacity(0.6),
              Colors.transparent,
            ],
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 返回按钮
            GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                width: 30.w,
                height: 30.w,
                alignment: Alignment.center,
                child: Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 24.w,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建底部控制栏
  Widget _buildBottomControls() {
    final position = _controller!.value.position;
    final duration = _controller!.value.duration;
    final progress = duration.inMilliseconds > 0 
        ? position.inMilliseconds / duration.inMilliseconds 
        : 0.0;
    
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: 20.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Colors.black.withOpacity(0.6),
              Colors.transparent,
            ],
          ),
        ),
        child: Row(
          children: [
            // 播放/暂停按钮
            GestureDetector(
              onTap: _togglePlayPause,
              child: Container(
                width: 55.w,
                height: 55.w,
                alignment: Alignment.center,
                child: Icon(
                  _controller!.value.isPlaying ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                  size: 30.w,
                ),
              ),
            ),
            SizedBox(width: 10.w),
            // 进度条
            Expanded(
              child: Container(
                height: 4.w,
                decoration: BoxDecoration(
                  color: const Color(0xFFF4F4F2),
                  borderRadius: BorderRadius.circular(2.w),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: progress.clamp(0.0, 1.0),
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFF93627),
                      borderRadius: BorderRadius.circular(2.w),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建加载指示器
  Widget _buildLoadingIndicator() {
    return Container(
      color: Colors.black.withOpacity(0.5),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      ),
    );
  }

  /// 切换播放/暂停
  void _togglePlayPause() {
    setState(() {
      if (_controller!.value.isPlaying) {
        _controller!.pause();
      } else {
        if (_isComplete) {
          // 如果播放完成，从头开始
          _controller!.seekTo(Duration.zero);
          setState(() {
            _isComplete = false;
          });
        }
        _controller!.play();
      }
    });
    
    // 显示控制栏并自动隐藏
    _showControlsTemporarily();
  }

  /// 切换控制栏显示/隐藏
  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    // 显示控制栏并自动隐藏
    if (_showControls) {
      _showControlsTemporarily();
    }
  }
  
  /// 显示控制栏并在 5 秒后自动隐藏
  void _showControlsTemporarily() {
    setState(() {
      _showControls = true;
    });
    
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }
}
