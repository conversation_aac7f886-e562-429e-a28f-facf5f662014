import 'dart:async';

import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/config/config_provider.dart';

import '../../../../repository/modals/config/icon_config.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package: app.view.home.main.widgets
/// @ClassName: main_swiper
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/7 11:06
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/7 11:06
/// @UpdateRemark: 更新说明
class MainSwiper extends ConsumerWidget {
  const MainSwiper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(fetchMainSwiperConfigProvider).when(
      data: (data) {
        if (data == null || data.isEmpty) {
          return Container();
        }
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: SwiperWidget(list: data),
        );
      },
      error: (o, s) {
        return Container();
      },
      loading: () {
        return Container();
      },
    );
  }
}

class SwiperWidget extends ConsumerStatefulWidget {
  const SwiperWidget({
    super.key,
    required this.list,
  });

  final List<IconConfig> list;

  @override
  SwiperWidgetState createState() => SwiperWidgetState();
}

class SwiperWidgetState extends ConsumerState<SwiperWidget> {
  double? _swiperHeight;

  @override
  void initState() {
    super.initState();
    _initializeSwiperHeight();
  }

  void _initializeSwiperHeight() async {
    try {
      var item = widget.list.first;
      var result = await getImageDimensions(item.pictureUrl ?? "");
      final aspectRatio = result.width / result.height;
      setState(() {
        _swiperHeight = 355.w / aspectRatio;
      });
    } catch (e) {
      setState(() {
        _swiperHeight = 100.h;
      });
    }
  }

  // 获取图片尺寸
  Future<Size> getImageDimensions(String url) {
    final ImageProvider provider = NetworkImage(url);

    final Completer<Size> completer = Completer();

    final ImageStreamListener listener = ImageStreamListener(
      (image, synchronousCall) {
        final size = Size(
          image.image.width.toDouble(),
          image.image.height.toDouble(),
        );

        completer.complete(size);
      },
      onError: (e, s) {
        completer.completeError(e, s);
      },
    );

    final ImageStream stream = provider.resolve(ImageConfiguration.empty);

    stream.addListener(listener);

    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    if (_swiperHeight != null) {
      return SizedBox(
        width: 355.w,
        height: _swiperHeight,
        child: Swiper(
          autoplay: true,
          itemBuilder: (BuildContext context, int index) {
            var item = widget.list[index];
            return ClipRRect(
              borderRadius: BorderRadius.circular(6.r),
              child: InkWell(
                onTap: () {
                  ref
                      .read(configItemClickProvider.notifier)
                      .configItemClick(item);
                },
                child: Image.network(
                  item.pictureUrl ?? "",
                  fit: BoxFit.contain,
                  errorBuilder: (c, o, s) {
                    return Container();
                  },
                ),
              ),
            );
          },
          itemCount: widget.list.length,
          pagination: SwiperCustomPagination(builder: (context, config) {
            return Positioned(
              bottom: 10.h,
              right: 20.w,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 1.h),
                decoration: BoxDecoration(
                  color: const Color(0x99000000),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  "${config.activeIndex + 1}/${config.itemCount}",
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            );
          }),
        ),
      );
    }
    return Container();
  }
}
