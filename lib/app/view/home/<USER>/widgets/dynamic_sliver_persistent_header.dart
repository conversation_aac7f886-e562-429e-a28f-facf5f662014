import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../provider/fun/fun_list_provider.dart';
import 'fun_activity_tab_widget.dart';

/// 基于SizeChangedLayoutNotifier的动态高度SliverPersistentHeader组件,使用稳定的高度监听机制
class DynamicSliverPersistentHeader extends ConsumerStatefulWidget {
  const DynamicSliverPersistentHeader({super.key});

  @override
  ConsumerState<DynamicSliverPersistentHeader> createState() => _DynamicSliverPersistentHeaderState();
}

class _DynamicSliverPersistentHeaderState extends ConsumerState<DynamicSliverPersistentHeader> {
  double _headerHeight = 0;

  @override
  Widget build(BuildContext context) {
    final tabState = ref.watch(manageFunTabConfigProvider);

    // 如果数据还未加载完成，显示loading状态
    if (tabState is AsyncLoading) {
      return SliverToBoxAdapter(
        child: Container(
          height: 180,
          alignment: Alignment.center,
          child: const SizedBox(),
        ),
      );
    }

    // 如果加载出错，显示错误状态
    if (tabState is AsyncError) {
      return SliverToBoxAdapter(
        child: Container(
          height: 100,
          alignment: Alignment.center,
          child: const Text("加载失败"),
        ),
      );
    }

    final tabWidget = FunActivityTabWidget(
      onHeightChanged: (height) {
        // 使用WidgetsBinding确保在下一帧更新状态
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && height > 0 && height != _headerHeight) {
            setState(() {
              _headerHeight = height;
            });
          }
        });
      },
    );

    // 如果还没有测量到高度，使用SliverToBoxAdapter进行测量
    if (_headerHeight <= 0) {
      return SliverToBoxAdapter(child: tabWidget);
    }

    // 已获得高度，使用SliverPersistentHeader实现粘性效果
    return SliverPersistentHeader(
      pinned: true,
      delegate: _SliverAppBarDelegate(
        minHeight: _headerHeight,
        maxHeight: _headerHeight,
        child: tabWidget,
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return minHeight != oldDelegate.minHeight ||
        maxHeight != oldDelegate.maxHeight ||
        child != oldDelegate.child;
  }
}
