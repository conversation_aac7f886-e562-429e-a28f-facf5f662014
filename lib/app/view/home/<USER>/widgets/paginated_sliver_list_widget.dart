import 'package:flutter/material.dart';

/// 通用的分页加载 SliverList 组件
///
/// 封装了以下功能:
/// - 列表分页加载逻辑
/// - 下拉刷新功能(通过外部 RefreshIndicator 实现)
/// - 滚动到底部自动加载更多数据
/// - 加载状态管理(初始加载、加载更多、无更多数据)
///
class PaginatedSliverListWidget<T> extends StatefulWidget {
  /// 列表数据项
  final List<T> items;

  /// 外部传入的滚动控制器(用于监听滚动位置)
  final ScrollController? scrollController;

  /// 是否正在加载中
  final bool isLoading;

  /// 是否还有更多数据
  final bool hasMore;

  /// 加载更多数据的回调函数
  final VoidCallback onLoadMore;

  /// 列表项构建器
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// 空数据时显示的组件
  final Widget? emptyWidget;

  /// 初始加载时显示的组件
  final Widget? initialLoadingWidget;

  /// 加载更多时显示的组件
  final Widget? loadingMoreWidget;

  /// 没有更多数据时显示的组件
  final Widget? noMoreDataWidget;

  /// 加载失败时显示的组件
  final Widget? errorWidget;

  /// 距离底部多少像素时触发加载更多(默认200)
  final double loadMoreThreshold;

  /// 初始加载状态(用于区分首次加载和加载更多)
  final bool isInitialLoading;

  const PaginatedSliverListWidget({
    super.key,
    required this.items,
    required this.isLoading,
    required this.hasMore,
    required this.onLoadMore,
    required this.itemBuilder,
    this.scrollController,
    this.emptyWidget,
    this.initialLoadingWidget,
    this.loadingMoreWidget,
    this.noMoreDataWidget,
    this.errorWidget,
    this.loadMoreThreshold = 200,
    this.isInitialLoading = false,
  });

  @override
  State<PaginatedSliverListWidget<T>> createState() =>
      _PaginatedSliverListWidgetState<T>();
}

class _PaginatedSliverListWidgetState<T>
    extends State<PaginatedSliverListWidget<T>> {
  @override
  void initState() {
    super.initState();
    _setupScrollListener();
  }

  @override
  void didUpdateWidget(PaginatedSliverListWidget<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果 scrollController 发生变化,重新设置监听器
    if (oldWidget.scrollController != widget.scrollController) {
      oldWidget.scrollController?.removeListener(_onScroll);
      _setupScrollListener();
    }
  }

  @override
  void dispose() {
    widget.scrollController?.removeListener(_onScroll);
    super.dispose();
  }

  void _setupScrollListener() {
    widget.scrollController?.addListener(_onScroll);
  }

  void _onScroll() {
    final controller = widget.scrollController;
    if (controller != null &&
        controller.position.pixels >=
            controller.position.maxScrollExtent - widget.loadMoreThreshold &&
        !widget.isLoading &&
        widget.hasMore) {
      widget.onLoadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    // 初始加载状态
    if (widget.items.isEmpty && widget.isInitialLoading) {
      return widget.initialLoadingWidget ?? _buildDefaultInitialLoading();
    }

    // 空数据状态
    if (widget.items.isEmpty && !widget.isLoading) {
      return widget.emptyWidget ?? _buildDefaultEmptyWidget();
    }

    // 列表内容
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          // 在列表末尾显示加载状态指示器
          if (index == widget.items.length) {
            if (widget.isLoading) {
              return widget.loadingMoreWidget ?? _buildDefaultLoadingMore();
            } else if (!widget.hasMore) {
              return widget.noMoreDataWidget ?? _buildDefaultNoMoreData();
            }
            return const SizedBox.shrink();
          }

          final item = widget.items[index];
          return widget.itemBuilder(context, item, index);
        },
        childCount:
            widget.items.length + (widget.hasMore || widget.isLoading ? 1 : 0),
      ),
    );
  }

  /// 默认的初始加载组件
  Widget _buildDefaultInitialLoading() {
    return const SliverToBoxAdapter(
      child: SizedBox(
        height: 100,
        child: Center(
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: Color(0xFFF94B3D),
          ),
        ),
      ),
    );
  }

  /// 默认的空数据组件
  Widget _buildDefaultEmptyWidget() {
    return const SliverToBoxAdapter(
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Text(
            '暂无数据',
            style: TextStyle(
              color: Color(0xFF999999),
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }

  /// 默认的加载更多组件
  Widget _buildDefaultLoadingMore() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 16,
              width: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Color(0xFFF94B3D),
              ),
            ),
            SizedBox(width: 16),
            Text(
              '加载中...',
              style: TextStyle(color: Color(0xFFF94B3D)),
            ),
          ],
        ),
      ),
    );
  }

  /// 默认的没有更多数据组件
  Widget _buildDefaultNoMoreData() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: Text(
          '没有更多数据了',
          style: TextStyle(
            color: Color(0xFF999999),
            fontSize: 14,
          ),
        ),
      ),
    );
  }
}
