import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_free_lunch_response.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/bawangcan_red_envelope_widget.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/widgets/highlight/highlight_text.dart';

class BawangcanActivityCardWidget extends StatelessWidget {
  BawangcanActivityCardWidget(
      {Key? key, required this.activity, this.isSelected, this.isShowTips})
      : super(key: key);
  final FreeLunchActivity activity;
  final bool? isSelected;
  final bool? isShowTips;
  final GlobalKey<TooltipState> tooltipkey = GlobalKey<TooltipState>();
  @override
  Widget build(BuildContext context) {
    final hasThreshold = (activity.commissionThresholdCent ?? 0) > 0;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8.h),
        // 返现信息
        _buildCommissionInfo(activity),
        BaWangCanRedEnvelopeWidget(goodsData: activity),
        SizedBox(height: 8.h),
        // 剩余名额
        _buildInventoryInfo(
          '返利要求',
          activity.planActivityType == 1
              ? '收到餐品后，需完成图文评价（含图含字）；${hasThreshold ? '需实付满${activity.commissionThresholdCent}(实付不含准时宝、券及会员购买等)' : '实付不含准时宝、券及会员购买等'}'
              : '无需评价；${hasThreshold ? '需实付满${activity.commissionThresholdCent}(实付不含准时宝、券及会员购买等)' : '实付不含准时宝、券及会员购买等'}',
        ),
        SizedBox(height: 8.h),
        _buildInventoryInfo(
          '抢单时间',
          '00:00 ～ 23:59',
        ),
        SizedBox(height: 8.h),
        _buildInventoryInfo('剩余名额', '${activity.inventory} 份',
            describeColor: const Color(0xFFF93324)),
        SizedBox(height: 6.h),

        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(
              child: HighlightText(
                style: TextStyle(
                  fontSize: 11.sp,
                  color: Colors.black,
                ),
                data: const ['备注：该店铺每人每天限报名1次，必须在报名', '30分钟内', '下单。'],
                keys: const ['30分钟内'],
                keyStyle: TextStyle(
                  fontSize: 11.sp,
                  color: const Color(0xFFF93324),
                ),
              ),
            ),
            SizedBox(width: 8.w),
            if (isSelected != null)
              isSelected == true
                  ? Image.asset(checkIconImg, width: 20.w, height: 20.w)
                  : Container(
                      width: 20.w,
                      height: 20.w,
                      decoration: BoxDecoration(
                        border: Border.all(
                            color: const Color(0xFFB4B4B4), width: 1.w),
                        borderRadius: BorderRadius.circular(10.w),
                      ),
                    )
          ],
        )
      ],
    );
  }

  /// 构建返现信息
  Widget _buildCommissionInfo(FreeLunchActivity activity) {
    final hasThreshold = (activity.commissionThresholdCent ?? 0) > 0;

    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: const Color(0xFFFFF9E8),
              borderRadius: BorderRadius.circular(4.r),
              border: Border.all(
                color: const Color(0xFFF73301),
                width: 1.w,
              ),
            ),
            child: Text(
              hasThreshold
                  ? '实付满¥${activity.commissionThresholdCent}返¥${activity.commission}'
                  : '按实付${activity.ratio}%返，最高¥${activity.maxCommission}',
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFFF73301),
              ),
            ),
          ),
          if (isShowTips == true) ...[
            SizedBox(width: 16.w),
            Tooltip(
              triggerMode: TooltipTriggerMode.manual,
              key: tooltipkey,
              padding: EdgeInsets.symmetric(
                horizontal: 6.w,
                vertical: 6.h,
              ),
              textStyle: TextStyle(
                fontSize: 11.sp,
                color: Colors.white,
              ),
              decoration: BoxDecoration(
                color: const Color(0xB1000000),
                borderRadius: BorderRadius.circular(6.r),
              ),
              verticalOffset: 12.h,
              preferBelow: false,
              message: '按实际金额返，实付金额不包含买券包、买会员产生的金额',
              child: InkWell(
                onTap: () {
                  tooltipkey.currentState?.ensureTooltipVisible();
                },
                child: CachedNetworkImage(
                  imageUrl:'https://alicdn.msmds.cn/APPSHOW/black_cjwt_icon_img.png',
                  width: 17.w,
                  height: 17.w,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建库存信息
  Widget _buildInventoryInfo(String text, String describeText,
      {Color? describeColor}) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: const Color(0xFFF3F3F3),
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Text(
            text,
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF666666),
            ),
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            describeText,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 12.sp,
              color: describeColor ?? Colors.black,
            ),
          ),
        ),
      ],
    );
  }
}
