import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/config/config_provider.dart';
import 'package:msmds_platform/app/repository/modals/config/icon_config.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: main_icons
/// @Description: 首页顶部icon区
/// @Author: frankylee
/// @CreateDate: 2023/11/16 10:49
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/16 10:49
/// @UpdateRemark: 更新说明

class MainIcons extends ConsumerStatefulWidget {
  const MainIcons({Key? key}) : super(key: key);

  @override
  MainIconsState createState() => MainIconsState();
}

class MainIconsState extends ConsumerState<MainIcons> {
  final PageController _pageController = PageController();

  int activeIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController.addListener(_pageScrollListener);
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.removeListener(_pageScrollListener);
    _pageController.dispose();
  }

  void _pageScrollListener() {
    setState(() {
      if (_pageController.page != null) {
        activeIndex = _pageController.page!.round();
      }
    });
  }

  Widget _buildItem(BuildContext context, IconConfig mainIconData) {
    return InkWell(
      onTap: () {
        ref
            .read(configItemClickProvider.notifier)
            .configItemClick(mainIconData);
      },
      child: SizedBox(
        width: (MediaQuery.of(context).size.width / 5).floorToDouble(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.network(
              mainIconData.pictureUrl ?? "",
              width: 40.w,
              height: 40.w,
              fit: BoxFit.contain,
            ),
            SizedBox(
              height: 2.h,
            ),
            Text(
              mainIconData.title ?? "",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ref.watch(fetchMainIconConfigProvider).when(
      data: (data) {
        if (data == null || data.isEmpty) {
          return Container();
        }
        return Container(
          padding: EdgeInsets.only(bottom: 12.h),
          color: const Color(0xFFF9F9F9),
          child: Column(
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: data[activeIndex].length > 5 ? 130.h : 58.h,
                curve: Curves.ease,
                child: PageView.builder(
                  controller: _pageController,
                  itemBuilder: (context, index) {
                    return Wrap(
                      alignment: WrapAlignment.start,
                      spacing: 0,
                      runSpacing: 14.h,
                      children: data[index]
                          .map(
                            (e) => _buildItem(context, e),
                          )
                          .toList(),
                    );
                  },
                  itemCount: data.length,
                ),
              ),
              Padding(padding: EdgeInsets.only(top: 8.h)),
              SmoothPageIndicator(
                controller: _pageController,
                count: data.length,
                effect: CustomizableEffect(
                  spacing: 5.w,
                  dotDecoration: DotDecoration(
                    width: 4,
                    height: 4,
                    color: const Color(0xFFEAEAEA),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  activeDotDecoration: DotDecoration(
                    width: 11,
                    height: 4,
                    color: const Color(0xFFFF0E37),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
        );
      },
      error: (o, s) {
        return Container();
      },
      loading: () {
        return Container();
      },
    );
  }
}
