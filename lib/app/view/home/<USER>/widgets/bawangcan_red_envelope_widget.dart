import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../common/img/icon_addres.dart';
import '../../../../repository/modals/fun/fun_free_lunch_response.dart';

class BaWangCanRedEnvelopeWidget extends StatelessWidget {
  final FreeLunchActivity? goodsData;

  const BaWangCanRedEnvelopeWidget({
    super.key,
    this.goodsData,
  });

  @override
  Widget build(BuildContext context) {
    // 检查是否应该显示红包膨胀
    if (goodsData?.redPacket?.state != 0) {
      return const SizedBox.shrink();
    }

    // 计算膨胀后的金额
    final String inflatedAmount = _calculateInflatedAmount();

    return Container(
      height: 18.h,
      decoration: BoxDecoration(
        color: const Color(0xFFFFEDEA),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 红包膨胀图标
          Image.asset(
            hbpzIconImg,
            width: 46.w,
            height: 20.h,
          ),
          SizedBox(width: 2.w),
          // 膨胀文案
          Text(
            '返现膨胀至',
            style: TextStyle(
              fontSize: 10.sp,
              color: Colors.black,
            ),
          ),
          Text(
            _getInflationText(inflatedAmount),
            style: TextStyle(
              fontSize: 10.sp,
              color: const Color(0xFFF73500),
            ),
          ),
          SizedBox(width: 4.w),
        ],
      ),
    );
  }

  /// 计算膨胀后的金额
  String _calculateInflatedAmount() {
    final redPacketAmount = goodsData?.redPacket?.amount ?? 0.0;
    double inflatedAmount;

    // 根据原始逻辑计算膨胀金额
    if (goodsData?.commission != null) {
      // 有固定返现金额时
      inflatedAmount = redPacketAmount + (goodsData!.commission!);
    } else {
      // 有最高返现金额时
      inflatedAmount = redPacketAmount + (goodsData?.maxCommission ?? 0.0);
    }

    return inflatedAmount.toStringAsFixed(1);
  }

  /// 获取膨胀显示文案
  String _getInflationText(String amount) {
    // 如果有固定返现，不显示"最高"
    if (goodsData?.commission != null) {
      return '$amount元';
    } else {
      return '最高$amount元';
    }
  }
}