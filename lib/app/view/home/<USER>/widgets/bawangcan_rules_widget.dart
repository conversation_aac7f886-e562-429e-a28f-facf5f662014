import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/fun/bawangcan_provider.dart';
import 'package:msmds_platform/widgets/highlight/highlight_text.dart';

/// 霸王餐详情规则说明区域
class BawangcanRulesWidget extends ConsumerWidget {
  const BawangcanRulesWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isExpanded = ref.watch(bawangcanRulesExpandedProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '报名/下单须知',
          style: TextStyle(
            fontSize: 15.sp,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 10.h),
        _buildRuleText(
          text: [
            '1、报名手机号必须和本次饿了么/美团点餐账户绑定的',
            '手机号一致',
            '，否则无法返现。\n\n',
            '2、报名后，必须在',
            '30分钟内',
            '在饿了么/美团对应的店铺下单，超时失效；\n\n',
            '3、反馈类活动，请在下单',
            '次日11点前',
            '完成反馈，超时则失效，返现取消;',
          ],
          highlightKeys: [
            '手机号一致',
            '30分钟内',
            '次日11点前',
          ],
        ),
        if (isExpanded) ...[
          Text(
            '\n4、报名次数：\n'
            '①同⼀个商家的活动：同⼀个⽤户当⽇只允许报名下单1次同⼀个商家的活动，每周最多可报名下单 3 次同一个商家的活动；\n'
            '②如果商家有多个活动(订单、评价)，用户只能报名其中一个；\n'
            '③每日报名次数：同一个⽤户当⽇最多可报名 5 次，报名后又取消也视为报名1次；\n\n',
            style: TextStyle(
              fontSize: 13.sp,
              color: const Color(0xFF333333),
              height: 1.3,
            ),
          ),
          _buildRuleText(
            text: [
              '5、以下情况无返现：\n'
                  '①实付金额有要求的商家，',
              '实付不满',
              '不能参与返现（实付金额不包括券包、买会员产生的金额，若发⽣部分售后则剩余⾦额须满⾜最低实付⻔槛）；\n',
              '②需要反馈类的订单，未按活动要求提交用餐评价反馈（评价内容以首次评价为准，后续修改评价视为无效），导致被平台',
              '审核失败',
              '的，则返现取消;\n③',
              '饿了么订单',
              '，请',
              '勿使用“叠红包”',
              ' ，否则无返现。\n\n',
              '6、禁止预定单和任何形式向商家索要返利/红包,以及同一店铺多平台返利,一经发现永久拉黑\n\n',
              '7、霸王餐活动中的返现金额只含订单商品费、打包费、配送费，特殊产品如准时宝、买券等金额无法计入返现金额计算，美团拼好饭、到店自取、神枪手商品券等活动不与该活动同享。',
            ],
            highlightKeys: ['实付不满', '审核失败', '饿了么订单', '勿使用“叠红包”'],
            height: 1.3,
          ),
        ],
        GestureDetector(
          onTap: () {
            ref.read(bawangcanRulesExpandedProvider.notifier).toggle();
          },
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isExpanded ? '收起' : '查看更多',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: const Color(0xFF999999),
                  ),
                ),
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  size: 16.w,
                  color: const Color(0xFF999999),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }



  /// 构建规则文本（带高亮）
  Widget _buildRuleText({
    required List<String> text,
    List<String>? highlightKeys,
    double height = 1.6,
  }) {
    return HighlightText(
      style: TextStyle(
        fontSize: 13.sp,
        color: const Color(0xFF333333),
        height: height,
      ),
      data: text,
      keys: highlightKeys ?? [],
      keyStyle: const TextStyle(
        color: Color(0xFFF93324),
      ),
    );
  }
}