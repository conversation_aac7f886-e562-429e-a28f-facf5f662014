import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import 'package:msmds_platform/app/repository/modals/fun/bawangcan_registration_record.dart';

/// 霸王餐报名记录项
class BawangcanRegistrationRecordItem extends StatelessWidget {
  final BawangcanRegistrationRecord record;
  final VoidCallback? onCancelRegistration;
  final VoidCallback? onGoToOrder;
  final VoidCallback? onGoToComment;

  const BawangcanRegistrationRecordItem({
    super.key,
    required this.record,
    this.onCancelRegistration,
    this.onGoToOrder,
    this.onGoToComment,
  });

  BawangcanApplyOrderShop? get _shop => record.applyOrderShop;

  bool get _needEvaluation => _shop?.activityType == 1;

  bool get _showActionSection => record.state == 3 || record.state == 4;

  @override
  Widget build(BuildContext context) {
    final showCancelButton = record.state == 3 && onCancelRegistration != null;
    final primaryAction = _buildPrimaryAction();

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 14.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          if (_shop != null) ...[
            SizedBox(height: 17.h),
            _buildShopInfo(),
          ],
          if (_showActionSection) ...[
            SizedBox(height: 12.h),
            _buildOrderTips(context),
            if (!(record.state == 4 && !_needEvaluation)) ...[
              SizedBox(height: 12.h),
              Container(
                width: double.infinity,
                height: 1.h,
                color: const Color(0xFFE5E5E5),
              ),
            ],
          ],
          if (showCancelButton || primaryAction != null) ...[
            SizedBox(height: 14.h),
            Row(
              children: [
                if (showCancelButton)
                  _buildOutlineButton(
                    text: '取消报名',
                    onTap: onCancelRegistration!,
                  ),
                if (showCancelButton && primaryAction != null)
                  SizedBox(width: 12.w),
                if (primaryAction != null)
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: _buildFilledButton(
                        text: primaryAction.label,
                        onTap: primaryAction.onTap,
                      ),
                    ),
                  )
                else if (showCancelButton)
                  const Spacer(),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final hasOrderNo = (record.applyOrderNo ?? '').isNotEmpty;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: Text(
                  '报名订单号：${hasOrderNo ? record.applyOrderNo : '-'}',
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: const Color(0xFF666666),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (hasOrderNo) ...[
                SizedBox(width: 6.w),
                GestureDetector(
                  onTap: () => _copyOrderNumber(record.applyOrderNo!),
                  child: Container(
                    width: 28.w,
                    height: 16.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15.r),
                      border: Border.all(
                        color: const Color(0xFFD1D1D1),
                        width: 1.w,
                      ),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      '复制',
                      style: TextStyle(
                        fontSize: 8.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        SizedBox(width: 8.w),
        Text(
          _statusText(record.state),
          style: TextStyle(
            fontSize: 11.sp,
            color: _statusColor(record.state),
          ),
        ),
      ],
    );
  }

  Widget _buildShopInfo() {
    final shop = _shop;
    if (shop == null) {
      return const SizedBox.shrink();
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: CachedNetworkImage(
            imageUrl: shop.shopImg ?? '',
            width: 70.w,
            height: 70.w,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              width: 70.w,
              height: 70.w,
              color: Colors.grey[200],
            ),
            errorWidget: (context, url, error) => Container(
              width: 70.w,
              height: 70.w,
              color: Colors.grey[200],
              alignment: Alignment.center,
              child: const Icon(
                Icons.store,
                color: Color(0xFF9A9A9A),
              ),
            ),
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                shop.shopName ?? '',
                style: TextStyle(
                  fontSize: 15.sp,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 10.h),
              _buildCommissionBadges(),
              SizedBox(height: 12.h),
              _buildRebateRequirement(),
              SizedBox(height: 10.h),
              _buildPlatformInfo(),
              if ((record.createTime ?? '').isNotEmpty) ...[
                SizedBox(height: 10.h),
                Text(
                  '参与时间：${record.createTime}',
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
              if ((record.expireTime ?? '').isNotEmpty) ...[
                SizedBox(height: 10.h),
                Text(
                  '过期时间：${record.expireTime}',
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: const Color(0xFFF93324),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCommissionBadges() {
    final shop = _shop;
    if (shop == null) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.r),
            border: Border.all(color: const Color(0xFFF73301), width: 1.w),
          ),
          child: Text(
            _commissionText(shop),
            style: TextStyle(
              fontSize: 10.sp,
              color: const Color(0xFFF73301),
            ),
          ),
        ),
        SizedBox(width: 6.w),
        // Container(
        //   padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
        //   decoration: BoxDecoration(
        //     borderRadius: BorderRadius.circular(4.r),
        //     border: Border.all(
        //       color: shop.activityType == 1
        //           ? const Color(0xFFFE7801)
        //           : const Color(0xFF43CE9A),
        //       width: 1.w,
        //     ),
        //   ),
        //   child: Text(
        //     shop.activityType == 1 ? '需用餐评价' : '无需评价',
        //     style: TextStyle(
        //       fontSize: 10.sp,
        //       color: shop.activityType == 1
        //           ? const Color(0xFFFE7801)
        //           : const Color(0xFF25C68A),
        //     ),
        //   ),
        // ),
      ],
    );
  }

  Widget _buildRebateRequirement() {
    final shop = _shop;
    if (shop == null) {
      return const SizedBox.shrink();
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '返利要求：',
          style: TextStyle(
            fontSize: 10.sp,
            color: const Color(0xFFF93324),
          ),
        ),
        Expanded(
          child: Text(
            _rebateRequirementText(shop),
            style: TextStyle(
              fontSize: 10.sp,
              color: const Color(0xFFF93324),
              height: 1.2,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPlatformInfo() {
    final shop = _shop;
    if (shop == null) {
      return const SizedBox.shrink();
    }

    return Text(
      '所属平台：${shop.applyType == 1 ? '美团官方' : '饿了么官方'}',
      style: TextStyle(
        fontSize: 10.sp,
        color: const Color(0xFF666666),
      ),
    );
  }

  Widget _buildOrderTips(BuildContext context) {
    final shop = _shop;
    if (shop == null) {
      return const SizedBox.shrink();
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Text.rich(
            TextSpan(
              style: TextStyle(
                fontSize: 10.sp,
                color: const Color(0xFF666666),
              ),
              children: [
                const TextSpan(text: '注：请用'),
                TextSpan(
                  text: record.mobile ?? '',
                  style: const TextStyle(color: Color(0xFFF93324)),
                ),
                TextSpan(
                  text: '手机号绑定的${shop.applyType == 1 ? '美团' : '饿了么'}账号下单',
                ),
              ],
            ),
          ),
        ),
        SizedBox(width: 10.w),
        GestureDetector(
          onTap: () {
            showHowToConfirmPhoneNumber(context, shop.applyType ?? 1);
          },
          child: Container(
            width: 70.w,
            height: 22.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.r),
              border: Border.all(
                color: const Color(0xFFF93324),
                width: 1.w,
              ),
            ),
            alignment: Alignment.center,
            child: Text(
              '如何确认?',
              style: TextStyle(
                fontSize: 10.sp,
                color: const Color(0xFFF93324),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 显示如何确认手机号弹窗
  void showHowToConfirmPhoneNumber(BuildContext context, int platformType) {
    showDialog(
      context: context,
      builder: (context) => GestureDetector(
        onTap: () {
          Navigator.of(context).pop();
        },
        child: Container(
          color: Colors.black.withOpacity(0.3),
          alignment: Alignment.center,
          child: Image.network(
            platformType == 1
                ? 'https://alicdn.msmds.cn/APPSHOW/mtTips.png'
                : 'https://alicdn.msmds.cn/APPSHOW/elmTips.png',
            width: platformType == 1 ? 375.w : 375.w,
            height: platformType == 1 ? 557.h : 714.h,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }

  Widget _buildOutlineButton({
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 74.w,
        height: 30.h,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30.r),
          border: Border.all(
            color: const Color(0xFFD1D1D1),
            width: 0.8.w,
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 11.sp,
            color: const Color(0xFF333333),
          ),
        ),
      ),
    );
  }

  Widget _buildFilledButton({
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 90.w,
        height: 32.h,
        decoration: BoxDecoration(
          color: const Color(0xFFF93324),
          borderRadius: BorderRadius.circular(30.r),
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 11.sp,
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  _PrimaryAction? _buildPrimaryAction() {
    if (record.state == 3 && onGoToOrder != null) {
      return _PrimaryAction(label: '前往下单', onTap: onGoToOrder!);
    }

    if (record.state == 4 && _needEvaluation && onGoToComment != null) {
      return _PrimaryAction(label: '前往评论', onTap: onGoToComment!);
    }

    return null;
  }

  void _copyOrderNumber(String orderNumber) {
    Clipboard.setData(ClipboardData(text: orderNumber));
    SmartDialog.showToast('复制成功');
  }

  String _rebateRequirementText(BawangcanApplyOrderShop shop) {
    final hasThreshold = (shop.commissionThresholdCent ?? 0) > 0;
    final thresholdText = hasThreshold
        ? '需实付满${shop.commissionThresholdCent}(实付不含准时宝、券及会员购买等)'
        : '实付不含准时宝、券及会员购买等';

    if (shop.activityType == 1) {
      return '收到餐品后，需完成图文评价（含图含字）；$thresholdText';
    }
    return '无需评价；$thresholdText';
  }

  String _commissionText(BawangcanApplyOrderShop shop) {
    if ((shop.commissionThresholdCent ?? 0) > 0) {
      return '实付满¥${shop.commissionThresholdCent}返¥${_formatDouble(shop.commission)}';
    }
    if ((shop.commissionRate ?? 0) > 0) {
      return '按实付${_formatDouble(shop.commissionRate)}%返，最高¥${_formatDouble(shop.maxCommission)}';
    }
    return '返现活动';
  }

  String _statusText(int? state) {
    switch (state) {
      case 1:
        return '已过期';
      case 2:
        return '已取消';
      case 3:
        return '已报名';
      case 4:
        return '已下单';
      case 5:
        return '已评价';
      case 6:
        return '订单无效';
      default:
        return '未知状态';
    }
  }

  Color _statusColor(int? state) {
    switch (state) {
      case 1:
        return const Color(0xFF727272);
      case 2:
        return const Color(0xFF727272);
      case 3:
        return const Color(0xFFF93324);
      case 4:
        return const Color(0xFFF93324);
      case 5:
        return const Color(0xFFF93324);
      case 6:
        return const Color(0xFF727272);
      default:
        return const Color(0xFF727272);
    }
  }

  String _formatDouble(double? value) {
    if (value == null) {
      return '0';
    }
    if (value % 1 == 0) {
      return value.toStringAsFixed(0);
    }
    return value.toStringAsFixed(2);
  }
}

class _PrimaryAction {
  final String label;
  final VoidCallback onTap;

  const _PrimaryAction({
    required this.label,
    required this.onTap,
  });
}
