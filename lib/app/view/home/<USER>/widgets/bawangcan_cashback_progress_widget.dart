import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:msmds_platform/app/provider/fun/bawangcan_registration_provider.dart';
import 'package:msmds_platform/app/repository/modals/fun/bwc_apply_status.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_free_lunch_response.dart';

/// 展示霸王餐返现进度的卡片组件，内置倒计时功能
class BawangcanCashbackProgress extends ConsumerStatefulWidget {
  const BawangcanCashbackProgress({
    super.key,
    required this.goodsData,
    required this.activity,
    required this.applyStatus,
    this.onReceiveRedPacket,
    this.onOrderNow,
    this.onCountdownFinish,
  });

  /// 报名活动对应的商品数据
  final FreeLunchDetail goodsData;

  /// 当前选中的活动信息
  final FreeLunchActivity activity;

  /// 报名状态信息
  final BwcApplyStatus applyStatus;

  /// 点击领取红包时的回调
  final VoidCallback? onReceiveRedPacket;

  /// 点击“立即下单”按钮时的回调
  final VoidCallback? onOrderNow;

  /// 倒计时结束时的回调
  final VoidCallback? onCountdownFinish;

  @override
  ConsumerState<BawangcanCashbackProgress> createState() =>
      _CashbackProgressCardState();
}

class _CashbackProgressCardState extends ConsumerState<BawangcanCashbackProgress> {
  Timer? _timer;
  Duration _remaining = Duration.zero;
  bool _countdownCompleted = false;
  bool _isEvaluationExpanded = false;
  final GlobalKey<TooltipState> tooltipkey1 = GlobalKey<TooltipState>();
  final GlobalKey<TooltipState> tooltipkey2 = GlobalKey<TooltipState>();

  @override
  void initState() {
    super.initState();
    _setupCountdown();
  }

  @override
  void didUpdateWidget(covariant BawangcanCashbackProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.applyStatus.expireTime != widget.applyStatus.expireTime) {
      _setupCountdown();
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _setupCountdown() {
    _timer?.cancel();
    _countdownCompleted = false;

    final expireTime = _parseExpireTime(widget.applyStatus.expireTime);
    if (expireTime == null) {
      setState(() {
        _remaining = Duration.zero;
      });
      return;
    }

    void updateRemaining() {
      final nextRemaining = _calculateRemaining(expireTime);
      if (!mounted) {
        return;
      }
      setState(() {
        _remaining = nextRemaining;
      });

      if (nextRemaining == Duration.zero && !_countdownCompleted) {
        _countdownCompleted = true;
        widget.onCountdownFinish?.call();
      }
    }

    updateRemaining();

    if (_remaining > Duration.zero) {
      _timer = Timer.periodic(const Duration(seconds: 1), (_) {
        updateRemaining();
        if (_remaining == Duration.zero) {
          _timer?.cancel();
        }
      });
    }
  }

  static DateTime? _parseExpireTime(String? raw) {
    if (raw == null || raw.isEmpty) {
      return null;
    }
    final normalised = raw.contains('T') ? raw : raw.replaceFirst(' ', 'T');
    try {
      return DateTime.parse(normalised);
    } catch (_) {
      return null;
    }
  }

  static Duration _calculateRemaining(DateTime expiry) {
    final diff = expiry.difference(DateTime.now());
    return diff.isNegative ? Duration.zero : diff;
  }

  @override
  Widget build(BuildContext context) {
    final maskedMobile = _maskMobile(widget.applyStatus.mobile);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 14.w, horizontal: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        image: const DecorationImage(
          image:
              NetworkImage('https://alicdn.msmds.cn/APPSHOW/fxjd_bj_img.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(maskedMobile),
          SizedBox(height: 20.h),
          _buildProgressSteps(maskedMobile),
          SizedBox(height: 20.h),
          _buildCountdown(),
          SizedBox(height: 10.h),
          _buildOrderButton(),
          SizedBox(height: 20.h),
          _buildFinalSteps(),
        ],
      ),
    );
  }

  Widget _buildHeader(String maskedMobile) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '返现进度',
          style: TextStyle(
            fontSize: 15.sp,
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          '(报名手机号：$maskedMobile)',
          style: TextStyle(
            fontSize: 13.sp,
            color: const Color(0xFF999999),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressSteps(String maskedMobile) {
    return Stack(
      children: [
        Positioned(
          top: 2,
          child: Image.network(
            'https://alicdn.msmds.cn/APPSHOW/check_icon_img.png',
            width: 16.w,
            height: 16.w,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text('1.领外卖红包，下单用红包可省3-9元',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: const Color(0xFF999999),
                      )),
                  SizedBox(
                    width: 10.w,
                  ),
                  _buildReceiveRedPacketButton()
                ],
              ),
              SizedBox(height: 20.h),
              Row(
                children: [
                  Text('2.立即下单点外卖',
                      style: TextStyle(
                        fontSize: 13.sp,
                      )),
                  SizedBox(
                    width: 10.w,
                  ),
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                    decoration: const BoxDecoration(
                      color: Color(0xFFFEE6E4),
                    ),
                    child: Text(
                      '不支持预约订单',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFFF93929),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '确保饿了么外卖下单账号绑定手机号',
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
                  Text(
                    maskedMobile,
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(width: 6.w),
                  Tooltip(
                    triggerMode: TooltipTriggerMode.manual,
                    key: tooltipkey1,
                    padding: EdgeInsets.symmetric(
                      horizontal: 6.w,
                      vertical: 6.h,
                    ),
                    textStyle: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.white,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xB1000000),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    verticalOffset: 12.h,
                    preferBelow: false,
                    message: '报名手机号需与下单手机号保持一致',
                    child: InkWell(
                      onTap: () {
                        tooltipkey1.currentState?.ensureTooltipVisible();
                      },
                      child: CachedNetworkImage(
                        imageUrl:'https://alicdn.msmds.cn/APPSHOW/black_cjwt_icon_img.png',
                        width: 17.w,
                        height: 17.w,
                      ),
                    ),
                  ),
                  SizedBox(width: 9.w),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReceiveRedPacketButton() {
    return GestureDetector(
        onTap: widget.onReceiveRedPacket ?? _handleReceiveRedPacket,
        child: Container(
          width: 56.w,
          height: 21.w,
          decoration: BoxDecoration(
            color: const Color(0xFFF93324),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Center(
            child: Text(
              '重新领',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.white,
              ),
            ),
          ),
        ));
  }

  Widget _buildCountdown() {
    final totalSeconds = _remaining.inSeconds;
    final minutes = (totalSeconds ~/ 60) % 60;
    final seconds = totalSeconds % 60;
    final minuteText = minutes.toString().padLeft(2, '0');
    final secondText = seconds.toString().padLeft(2, '0');

    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildTimeBlock(minuteText),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 2.w),
            child: Text(
              '分',
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFFF93324),
              ),
            ),
          ),
          _buildTimeBlock(secondText),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 2.w),
            child: Text(
              '秒后失效',
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFFF93324),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeBlock(String value) {
    return Container(
      width: 18.w,
      height: 18.w,
      decoration: BoxDecoration(
        color: const Color(0xFFF93324),
        borderRadius: BorderRadius.circular(2.r),
      ),
      alignment: Alignment.center,
      child: Text(
        value,
        style: TextStyle(
          fontSize: 12.sp,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildOrderButton() {
    return Center(
      child: GestureDetector(
        onTap: widget.onOrderNow ?? _handleGoToOrder,
        child: Container(
          width: 282.w,
          height: 41.h,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: const Color(0xFFF93324),
            borderRadius: BorderRadius.circular(40.r),
          ),
          child: Text(
            '立即下单点外卖',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFinalSteps() {
    final needsReview = widget.activity.planActivityType == 1;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (needsReview) ...[
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                setState(() {
                  _isEvaluationExpanded = !_isEvaluationExpanded;
                });
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    '3.收货后提交"外卖图+文字"评价，超时无返',
                    style: TextStyle(
                      fontSize: 13.sp,
                    ),
                  ),
                  SizedBox(width: 6.w),
                  Icon(
                    _isEvaluationExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    size: 20.r,
                    color: Colors.black,
                  )
                ],
              ),
            ),
            AnimatedCrossFade(
              firstChild: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: 6.h),
                  Text(
                    '请在下单次日11点前完成评价，超时则返现自动取消',
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
                  SizedBox(height: 14.h),
                  Align(
                    alignment: Alignment.center,
                    child: GestureDetector(
                      onTap: _handleGoToComment,
                      child: Container(
                        width: 282.w,
                        height: 41.h,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF93324),
                          borderRadius: BorderRadius.circular(40.r),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          '前往评价',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              secondChild: const SizedBox.shrink(),
              crossFadeState: _isEvaluationExpanded
                  ? CrossFadeState.showFirst
                  : CrossFadeState.showSecond,
              duration: const Duration(milliseconds: 200),
            ),
          ],
          Container(
            margin: EdgeInsets.only(top: 20.h),
            child: Text(
              '${needsReview ? "4" : "3"}.外卖收货后，坐等返现（可提现到支付宝）',
              style: TextStyle(
                fontSize: 13.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _maskMobile(String? mobile) {
    if (mobile == null || mobile.isEmpty) {
      return '';
    }
    return mobile.replaceAllMapped(
      RegExp(r'(\d{3})\d{4}(\d{4})'),
      (match) => '${match.group(1)}****${match.group(2)}',
    );
  }

  /// 领取红包 - 跳转到对应外卖平台小程序领取红包
  Future<void> _handleReceiveRedPacket() async {
    await ref.read(bawangcanRegistrationProvider.notifier).receiveRedPacket(
          context: context,
          ref: ref,
          applyType: widget.goodsData.type ?? 0,
        );
  }

  /// 前往下单 - 跳转到商品详情页
  Future<void> _handleGoToOrder() async {
    await ref.read(bawangcanRegistrationProvider.notifier).goToPlaceOrder(
          context: context,
          ref: ref,
          applyType: widget.goodsData.type ?? 0,
          wxPath: widget.goodsData.actionUrl?.wxPath,
          activityId: widget.applyStatus.activityId,
          shopId: widget.applyStatus.shopId ?? widget.goodsData.shopId,
        );
  }

  /// 前往评价 - 跳转到订单列表页
  Future<void> _handleGoToComment() async {
    await ref.read(bawangcanRegistrationProvider.notifier).goToComment(
          context: context,
          ref: ref,
          applyType: widget.goodsData.type ?? 0,
        );
  }
}
