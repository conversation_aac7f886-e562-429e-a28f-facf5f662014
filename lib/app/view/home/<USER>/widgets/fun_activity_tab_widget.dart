﻿import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_tab_config.dart';

import '../../../../navigation/router.dart';
import '../../../../provider/fun/fun_list_provider.dart';

/// 动态高度 SliverPersistentHeader 组件
class FunActivityTabWidget extends ConsumerStatefulWidget{
  final ValueChanged<double>? onHeightChanged;

  const FunActivityTabWidget({
    super.key,
    this.onHeightChanged,
  });

  @override
  ConsumerState<FunActivityTabWidget> createState() => _FunActivityTabWidgetState();
}

class _FunActivityTabWidgetState extends ConsumerState<FunActivityTabWidget> {
  final GlobalKey _tabWrapKey = GlobalKey();

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 在组件构建完成后立即测量高度
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _measureAndReportHeight();
    });
  }

  /// 主标签
  Widget _buildTabItem(FunTabConfig item, int index, int selectedIndex) {
    return GestureDetector(
      onTap: () {
        ref
            .read(manageFunTabConfigProvider.notifier)
            .getChildrenTab(item.code ?? "");
        if (selectedIndex != index) {
          ref.read(tabSelectionStateProvider.notifier).setMainTabIndex(index);
        }
      },
      child: Column(
        children: [
          if (item.iconUrl != null)
            Image.network(
              item.iconUrl ?? '',
              width: 68,
              height: 23,
              errorBuilder: (context, error, stackTrace) =>
                  Text(item.name ?? '',
                      style: TextStyle(
                        color: selectedIndex == index
                            ? const Color(0xFF2F2F2E)
                            : const Color(0xFF676764),
                        fontWeight: selectedIndex == index
                            ? FontWeight.bold
                            : FontWeight.normal,
                        fontSize: 14,
                      )),
            )
          else
            Text(
              item.name ?? '',
              style: TextStyle(
                color: selectedIndex == index
                    ? const Color(0xFF2F2F2E)
                    : const Color(0xFF676764),
                fontWeight: selectedIndex == index
                    ? FontWeight.bold
                    : FontWeight.normal,
                fontSize: 14,
              ),
            ),
          Container(
            margin: const EdgeInsets.only(top: 2),
            width: 40,
            height: 3,
            decoration: BoxDecoration(
              color: selectedIndex == index
                  ? const Color(0xFFF93324)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(3),
            ),
          ),
        ],
      ),
    );
  }

  // 子标签
  Widget _buildChildTabItem(FunTabConfig item, int index, int selectedIndex) {
    return GestureDetector(
      onTap: () {
        if (selectedIndex != index) {
          ref.read(tabSelectionStateProvider.notifier).setChildTabIndex(index);
        }
      },
      child: Container(
        margin: const EdgeInsets.fromLTRB(5, 12, 5, 0),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          border: Border.all(color: const Color(0xFFBAA38C), width: 0.5),
          color: selectedIndex == index
              ? const Color(0xFFFFFAED)
              : Colors.transparent,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              item.tabName ?? "",
              style: TextStyle(
                color: selectedIndex == index
                    ? const Color(0xFFFE7801)
                    : const Color(0xFF676764),
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final tabState = ref.watch(manageFunTabConfigProvider);
    final tabSelection = ref.watch(tabSelectionStateProvider);
    return Container(
      child: tabState.when(
        data: (data) {
          if (data == null) {
            return const Center(
              child: Text("加载失败"),
            );
          }
          return _buildTab(context, data, tabSelection);
        },
        error: (error, stackTrace) {
          return const Text("error");
        },
        loading: () {
          return const SizedBox();
        },
      ),
    );
  }

  /// 测量Wrap容器高度并通过回调通知父组件
  void _measureAndReportHeight() {
    final renderBox = _tabWrapKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null && mounted) {
      final height = renderBox.size.height;
      if (height > 0) {
        widget.onHeightChanged?.call(height + 100);
      }
    }
  }

  Widget _buildTab(BuildContext context, TabStateConfig data, TabSelection tabSelection) {
    return Container(
      color: const Color(0xFFF4F4F2),
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 主标签栏
          Container(
              padding: const EdgeInsets.fromLTRB(14, 12, 14, 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: data.mainTab.tabs.map((e) {
                  return _buildTabItem(e, data.mainTab.tabs.indexOf(e),
                      tabSelection.mainTabIndex);
                }).toList(),
              )),
          // 搜索框
          _buildSearchBar(context),

          // 第二部分：子标签和排序区域
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: _buildChildrenTabAndSort(context, data, tabSelection),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.pushNamed(context, CsRouter.searchPre),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 10),
        padding: const EdgeInsets.only(left: 14),
        decoration: BoxDecoration(
          color: const Color(0xFFFEFFFF),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            Image.asset(
              'assets/images/searchIcon.png',
              width: 13,
              height: 13,
              color: const Color(0xFFF93324),
              errorBuilder: (context, error, stackTrace) => const Icon(
                Icons.search,
                size: 13,
                color: Color(0xFFF93324),
              ),
            ),
            const SizedBox(width: 10),
            const Expanded(
              child: Text(
                '搜索更多优惠，下单享返现',
                style: TextStyle(
                  color: Color(0xFF999999),
                  fontSize: 14,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Container(
              margin: const EdgeInsets.all(3),
              width: 52,
              height: 26,
              decoration: BoxDecoration(
                color: const Color(0xFFF93324),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Center(
                child: Text(
                  '搜索',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 子标签栏和排序区域
  Widget _buildChildrenTabAndSort(BuildContext context,
      TabStateConfig data, TabSelection tabSelection) {
    return Row(
      children: [
        Expanded(
          child: NotificationListener<SizeChangedLayoutNotification>(
            onNotification: (notification) {       
              _measureAndReportHeight();
              return true;
            },
            child: SizeChangedLayoutNotifier(
              child: Wrap(
                key: _tabWrapKey,
                children: [
                  // 子标签列表
                  ...data.childrenTab.tabs.map((e) {
                    return _buildChildTabItem(
                        e,
                        data.childrenTab.tabs.indexOf(e),
                        tabSelection.childTabIndex);
                  }),
                  // 排序按钮（如果存在）
                  if (data.childrenTab.tabs.isNotEmpty &&
                      tabSelection.childTabIndex < data.childrenTab.tabs.length &&
                      data.childrenTab.tabs[tabSelection.childTabIndex].sort != null &&
                      data.childrenTab.tabs[tabSelection.childTabIndex].sort!.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.fromLTRB(5, 12, 5, 0),
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      height: 26,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: DropdownButton<FunSortOption>(
                        value: data.currentSort,
                        hint: const Text(
                          '排序',
                          style: TextStyle(fontSize: 11, color: Color(0xFF676764)),
                        ),
                        iconSize: 16,
                        elevation: 1,
                        style: const TextStyle(fontSize: 10, color: Color(0xFF676764)),
                        underline: Container(), // 移除下划线
                        dropdownColor: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        items: data.childrenTab.tabs[tabSelection.childTabIndex].sort!
                            .map<DropdownMenuItem<FunSortOption>>((FunSortOption option) {
                          return DropdownMenuItem<FunSortOption>(
                            value: option,
                            child: Text(
                              option.sortName ?? '',
                              style: TextStyle(
                                fontSize: 10,
                                color: data.currentSort?.sortName == option.sortName
                                    ? const Color(0xFF676764)
                                    : const Color(0xFF676764),
                              ),
                            ),
                          );
                        }).toList(),
                        onChanged: (FunSortOption? newValue) {
                          if (newValue != null) {
                            ref
                                .read(manageFunTabConfigProvider.notifier)
                                .setCurrentSort(newValue);
                          }
                        },
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}