import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../common/img/icon_addres.dart';


/// 进度条
/// [totalInventory]  总库存
/// [inventory]  剩余库存
/// [width]  进度条宽度
class BaWangCanProgressWidget extends StatelessWidget {
  final int? totalInventory;
  final int? inventory;
  final double? width;

  const BaWangCanProgressWidget({
    super.key,
    this.totalInventory,
    this.inventory,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final total = totalInventory ?? 0;
    final remaining = inventory ?? 0;
    final used = total - remaining;
    final progress = total > 0 ? used / total : 0.0;

    return SizedBox(
      width: width ?? 80.w,
      height: 16.h, // 增加整体高度以容纳图标
      child: Stack(
        children: [
          // 进度条容器，垂直居中
          Positioned(
            left: 0,
            right: 0,
            top: 4.h, // 垂直居中对齐
            child: Container(
              height: 6.h,
              decoration: BoxDecoration(
                color: const Color(0xFFFFE0BD),
                borderRadius: BorderRadius.circular(3.r),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(3.r),
                child: LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.transparent,
                  valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFFF6B35)),
                ),
              ),
            ),
          ),
          // 进度图标，位置根据进度动态调整
          Positioned(
            left: _calculateIconPosition(progress, width ?? 80.w, 16.w),
            top: 0,
            child: Image.asset(
              progressIconImg,
              width: 14.w,
              height: 14.w,
            ),
          ),
        ],
      ),
    );
  }

  /// 计算图标的正确位置
  /// [progress] 当前进度 (0.0 到 1.0)
  /// [progressBarWidth] 进度条宽度
  /// [iconWidth] 图标宽度
  double _calculateIconPosition(double progress, double progressBarWidth, double iconWidth) {
    // 确保进度在有效范围内
    final clampedProgress = progress.clamp(0.0, 1.0);

    // 计算图标中心应该在的位置
    final iconCenterPosition = clampedProgress * progressBarWidth;

    // 图标左边距 = 图标中心位置 - 图标宽度的一半
    final iconLeft = iconCenterPosition - (iconWidth / 2);

    // 确保图标不会超出进度条边界
    return iconLeft.clamp(0.0, progressBarWidth - iconWidth);
  }
}