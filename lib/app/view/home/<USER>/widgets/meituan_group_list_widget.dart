import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/provider/fun/fun_list_provider.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_meituan_response.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_tab_config.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/paginated_sliver_list_widget.dart';
import 'meituan_group_item_widget.dart';
import 'share_bottom_sheet.dart';

/// 带有分页功能的美团团购列表组件
class MeituanGroupListWithPaginationWidget extends ConsumerStatefulWidget {
  final FunTabConfig mainTabConfig;
  final FunTabConfig childTabConfig;
  final double? latitude;
  final double? longitude;
  final FunSortOption? sortOption;
  final ScrollController? scrollController; // 外部传入的滚动控制器

  const MeituanGroupListWithPaginationWidget({
    super.key,
    required this.mainTabConfig,
    required this.childTabConfig,
    this.latitude,
    this.longitude,
    this.sortOption,
    this.scrollController,
  });

  @override
  ConsumerState<MeituanGroupListWithPaginationWidget> createState() =>
      _MeituanGroupListWithPaginationWidgetState();
}

class _MeituanGroupListWithPaginationWidgetState
    extends ConsumerState<MeituanGroupListWithPaginationWidget> {
  final List<MeituanCouponItem> _allItems = [];
  int _currentPage = 1;
  bool _isLoading = false;
  bool _hasMore = true;
  String? _lastTabKey; // 用于检测标签切换

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  @override
  void didUpdateWidget(MeituanGroupListWithPaginationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 检测标签是否发生变化
    final currentTabKey = _generateTabKey();
    if (_lastTabKey != null && _lastTabKey != currentTabKey) {
      _loadInitialData();
    }
  }

  String _generateTabKey() {
    return '${widget.mainTabConfig.code}_${widget.childTabConfig.tabName}_${widget.sortOption?.sortField ?? ''}';
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _currentPage = 1;
      _allItems.clear();
    });

    // 更新当前标签key
    _lastTabKey = _generateTabKey();

    await _loadPage(1);
  }

  Future<void> _loadMoreData() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    await _loadPage(_currentPage + 1);
  }

  Future<void> _loadPage(int page) async {
    try {
      final provider = fetchMeituanCouponListProvider(
        widget.mainTabConfig,
        widget.childTabConfig,
        latitude: widget.latitude,
        longitude: widget.longitude,
        pageNo: page,
        sortOption: widget.sortOption,
      );

      final response = await ref.read(provider.future);

      if (response.data?.list != null) {
        setState(() {
          if (page == 1) {
            _allItems.clear();
          }
          _allItems.addAll(response.data!.list!);
          _currentPage = page;
          _hasMore = response.data!.hasMore;
        });
      }
    } catch (e) {
      debugPrint('加载数据失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PaginatedSliverListWidget<MeituanCouponItem>(
      items: _allItems,
      scrollController: widget.scrollController,
      isLoading: _isLoading,
      hasMore: _hasMore,
      onLoadMore: _loadMoreData,
      isInitialLoading: _allItems.isEmpty && _isLoading,
      loadMoreThreshold: 300,
      itemBuilder: (context, item, index) {
        return MeituanGroupItemWidget(
          item: item,
          index: index,
          onShareTap: () => _handleShare(item),
          onBuyTap: () => _handleBuy(item),
        );
      },
    );
  }

  void _handleShare(MeituanCouponItem item) async {
    final shareContent = await ref.read(meituanShareLinkProvider(item).future);

    if (!mounted) return;

    ShareBottomSheet.show(
      context,
      shareContent.isNotEmpty ? shareContent : "暂无可分享内容",
    );
  }

  void _handleBuy(MeituanCouponItem item) {
    ref.read(funItemClickProvider.notifier).meituanGetCoupon(item);
    debugPrint('购买商品: ${item.goodsName}');
  }
}
