import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

/// 位置权限提示组件
///
/// 用于在用户未授予位置权限或位置获取失败时显示提示界面
/// 包含以下状态：
/// - 正在获取定位中（加载状态）
/// - 定位获取失败（可重新获取）
/// - 定位未开启（需要开启权限）
class LocationPermissionWidget extends StatelessWidget {
  /// 位置权限状态
  final LocationPermissionState state;

  /// 点击按钮的回调
  final VoidCallback onButtonPressed;

  const LocationPermissionWidget({
    super.key,
    required this.state,
    required this.onButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildContent(),
        ],
      ),
    );
  }

  /// 构建不同状态的内容
  Widget _buildContent() {
    switch (state) {
      case LocationPermissionState.loading:
        return _buildLoadingState();
      case LocationPermissionState.failed:
        return _buildFailedState();
      case LocationPermissionState.noPermission:
        return _buildNoPermissionState();
    }
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(height: 18.w),
        // 加载动画（使用 gif 图片）
        Image.asset(
          funActivityLoading,
          width: 65.w,
          height: 30.w,
          fit: BoxFit.contain,
        ),
        SizedBox(height: 10.w),
        Text(
          '正在获取定位中...',
          style: TextStyle(
            color: const Color(0xFF000000),
            fontSize: 11.sp,
          ),
        ),
      ],
    );
  }

  /// 构建定位获取失败状态
  Widget _buildFailedState() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 无定位图标
        CachedNetworkImage(
          imageUrl:
              'https://alicdn.msmds.cn/APPSHOW/fun_activity_no_location.png',
          width: 89.28.w,
          height: 64.17.w,
          fit: BoxFit.contain,
          errorWidget: (context, url, error) => Icon(
            Icons.location_off,
            size: 64.w,
            color: Colors.grey,
          ),
        ),
        SizedBox(height: 7.w),
        Text(
          '定位获取失败，请点击重新获取',
          style: TextStyle(
            color: const Color(0xFF000000),
            fontSize: 11.sp,
          ),
        ),
        SizedBox(height: 9.w),
        // 重新获取按钮
        _buildButton('重新获取'),
      ],
    );
  }

  /// 构建定位未开启状态
  Widget _buildNoPermissionState() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 无定位图标
        CachedNetworkImage(
          imageUrl:
              'https://alicdn.msmds.cn/APPSHOW/fun_activity_no_location.png',
          width: 89.28.w,
          height: 64.17.w,
          fit: BoxFit.contain,
          errorWidget: (context, url, error) => Icon(
            Icons.location_off,
            size: 64.w,
            color: Colors.grey,
          ),
        ),
        SizedBox(height: 7.w),
        Text(
          '定位未开启，开启后才能看到附近的活动和优惠哦～',
          style: TextStyle(
            color: const Color(0xFF000000),
            fontSize: 11.sp,
          ),
        ),
        SizedBox(height: 9.w),
        // 立即开启按钮
        _buildButton('立即开启'),
      ],
    );
  }

  /// 构建按钮组件
  Widget _buildButton(String text) {
    return InkWell(
      onTap: onButtonPressed,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 50.w,
          vertical: 6.w,
        ),
        decoration: BoxDecoration(
          color: const Color(0xFFF93324),
          borderRadius: BorderRadius.circular(16.w),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: const Color(0xFFFFFFFF),
            fontSize: 13.sp,
          ),
        ),
      ),
    );
  }
}

/// 位置权限状态枚举
enum LocationPermissionState {
  /// 正在加载
  loading,

  /// 获取失败
  failed,

  /// 没有权限
  noPermission,
}
