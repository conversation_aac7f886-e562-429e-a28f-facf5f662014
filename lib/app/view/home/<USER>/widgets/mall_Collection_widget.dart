import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/provider/fun/mall_collection_provider.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_promotion.dart';
import 'package:msmds_platform/utils/smart_jump_util.dart';

class MallCollectionWidget extends ConsumerStatefulWidget {
  final dynamic navigation; // 占位符，实际项目中需要替换为实际的导航对象

  const MallCollectionWidget({Key? key, this.navigation}) : super(key: key);

  @override
  ConsumerState<MallCollectionWidget> createState() => _MallCollectionWidgetState();
}

class _MallCollectionWidgetState extends ConsumerState<MallCollectionWidget> {
  late ScrollController _scrollController;
  bool _isScrolling = false; // 标记是否正在手动滚动，避免循环更新

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScrollChanged);

    // 确保初始状态为第一个tab选中
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref.read(mallCollectionSelectedIndexProvider.notifier).reset();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScrollChanged);
    _scrollController.dispose();
    super.dispose();
  }

  /// 滚动监听器，处理滚动时更新Tab选中状态
  void _onScrollChanged() {
    if (_isScrolling || !_scrollController.hasClients) return;

    final displayData = ref.read(mallCollectionContentDataProvider);
    final leftMenuData = ref.read(mallCollectionTopMenuDataProvider);
    if (displayData.isEmpty || leftMenuData.isEmpty) return;

    final scrollOffset = _scrollController.offset;
    final maxScrollExtent = _scrollController.position.maxScrollExtent;
    final viewportWidth = _scrollController.position.viewportDimension;

    int newSelectedIndex;

    // 边界条件处理 - 滚动到最左侧时选择第一个分类
    if (scrollOffset <= 30) {
      // 增加容差到30像素，确保更好的响应
      newSelectedIndex = 0;
    }
    // 边界条件处理 - 滚动到最右侧时选择最后一个分类
    else if (maxScrollExtent > 0 && scrollOffset >= maxScrollExtent - 30) {
      // 增加容差到30像素
      newSelectedIndex = leftMenuData.length - 1;
    }
    // 特殊情况：当内容不足以滚动时，默认选择第一个分类
    else if (maxScrollExtent <= 0) {
      newSelectedIndex = 0;
    } else {
      // 中间区域：根据视口中心位置计算对应的分类
      final centerOffset = scrollOffset + viewportWidth / 2;

      // 使用平均宽度估算（因为使用自适应宽度）
      const avgItemWidth = 70;
      int estimatedItemIndex = (centerOffset / avgItemWidth).floor();
      estimatedItemIndex = estimatedItemIndex.clamp(0, displayData.length - 1);

      // 找到当前项目对应的分类索引
      if (estimatedItemIndex < displayData.length) {
        final currentItem = displayData[estimatedItemIndex];
        newSelectedIndex = currentItem.categoryIndex;
      } else {
        // 安全回退：如果计算出错，根据滚动位置选择合适的分类
        final progress = scrollOffset / maxScrollExtent;
        newSelectedIndex = (progress * (leftMenuData.length - 1))
            .round()
            .clamp(0, leftMenuData.length - 1);
      }
    }

    // 确保索引在有效范围内
    newSelectedIndex = newSelectedIndex.clamp(0, leftMenuData.length - 1);

    // 更新选中状态（只有在确实发生变化时才更新）
    final currentSelectedIndex = ref.read(mallCollectionSelectedIndexProvider);
    if (newSelectedIndex != currentSelectedIndex) {
      ref
          .read(mallCollectionSelectedIndexProvider.notifier)
          .setIndex(newSelectedIndex);
    }
  }

  @override
  Widget build(BuildContext context) {
    final mallDataAsync = ref.watch(fetchListPromotionProvider);
    final selectedIndex = ref.watch(mallCollectionSelectedIndexProvider);
    final topMenuData = ref.watch(mallCollectionTopMenuDataProvider);
    final contentData = ref.watch(mallCollectionContentDataProvider);

    return mallDataAsync.when(
      data: (mallData) {
        if (mallData.isEmpty) {
          return const SizedBox.shrink();
        }
        return _buildContent(selectedIndex, topMenuData, contentData);
      },
      loading: () => _buildLoading(),
      error: (error, stack) => _buildError(error),
    );
  }

  /// 构建主要内容
  Widget _buildContent(
    int selectedIndex,
    List<FunPromotionCollection> topMenuData,
    List<MallCollectionContentItem> contentData,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 顶部菜单
          Container(
            padding: const EdgeInsets.only(left: 16),
            // color: Colors.red,
            height: 45, // 固定高度
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(topMenuData.length, (index) {
                final item = topMenuData[index];
                return _buildTopItem(item, index, selectedIndex, contentData);
              }),
            ),
          ),

          // 返现平台跳转内容 - 自适应高度
          SingleChildScrollView(
            controller: _scrollController,
            scrollDirection: Axis.horizontal,
            child: Row(
              children:
                  contentData.map((item) => _buildContentItem(item)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoading() {
    return Container(
      height: 130,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  /// 构建错误状态
  Widget _buildError(Object error) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('加载失败: $error'),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () {
                ref.invalidate(fetchListPromotionProvider);
              },
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  /// 顶部菜单项
  Widget _buildTopItem(
    FunPromotionCollection item,
    int index,
    int selectedIndex,
    List<MallCollectionContentItem> displayData,
  ) {
    final bool isSelected = index == selectedIndex;

    return GestureDetector(
      onTap: () => _topItemClick(index, displayData),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 11),
        margin: const EdgeInsets.only(right: 30),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题文本
            Text(
              item.typeName ?? '',
              style: TextStyle(
                color: isSelected
                    ? const Color(0xFFFE7801)
                    : const Color(0xFF333333),
                fontSize: 12,
              ),
            ),
            // 选中状态下划线
            Container(
              width: 19,
              height: 2,
              margin: const EdgeInsets.only(top: 4),
              decoration: BoxDecoration(
                color:
                    isSelected ? const Color(0xFFFE7801) : Colors.transparent,
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(3),
                  bottomRight: Radius.circular(3),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 返现平台跳转内容项
  Widget _buildContentItem(MallCollectionContentItem item) {
    // 如果是分类标题，显示空视图（或者可以显示分类标题）
    if (item.isTitle) {
      return const SizedBox.shrink();
    }

    final jumpData = item.typeData?.jump;

    return GestureDetector(
      onTap: () => _contentItemClick(item),
      child: IntrinsicWidth(
        child: Container(
          padding: const EdgeInsets.fromLTRB(14, 0, 14, 14),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 图标
              SizedBox(
                width: 34,
                child: AspectRatio(
                  aspectRatio: 1.0, // 保持图标为正方形
                  child: jumpData?.profile != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(
                            36, // 动态圆角
                          ),
                          child: Image.network(
                            jumpData!.profile!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(
                                    16,
                                  ),
                                ),
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey[400],
                                  size: 16,
                                ),
                              );
                            },
                          ),
                        )
                      : Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(
                              16,
                            ),
                          ),
                        ),
                ),
              ),

              // 标题
              const SizedBox(height: 4),
              Text(
                jumpData?.title ?? '',
                style: const TextStyle(
                  color: Color(0xFF333333),
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 2, // 允许两行显示
                overflow: TextOverflow.ellipsis,
              ),

              // 描述
              const SizedBox(height: 2),
              Text(
                jumpData?.desc ?? '',
                style: const TextStyle(
                  color: Color(0xFF999999),
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
                maxLines: 2, // 允许两行显示
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 顶部菜单列表项点击事件
  void _topItemClick(
      int categoryIndex, List<MallCollectionContentItem> displayData) {
    final currentSelectedIndex = ref.read(mallCollectionSelectedIndexProvider);
    if (categoryIndex == currentSelectedIndex) return;

    // 设置标记，避免滚动监听器触发状态更新
    _isScrolling = true;

    // 更新选中状态
    ref
        .read(mallCollectionSelectedIndexProvider.notifier)
        .setIndex(categoryIndex);

    // 计算目标滚动位置（滚动到第一个属于该分类的项目）
    double targetOffset = 0;
    const avgItemWidth = 70; // 使用平均宽度估算

    for (int i = 0; i < displayData.length; i++) {
      if (displayData[i].categoryIndex == categoryIndex) {
        // 使用更精确的计算：让目标分类显示在视口中心
        targetOffset = (i * avgItemWidth) -
            (_scrollController.position.viewportDimension / 4);
        targetOffset =
            targetOffset.clamp(0.0, _scrollController.position.maxScrollExtent);
        break;
      }
    }

    // 执行滚动动画
    _scrollController
        .animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    )
        .then((_) {
      // 动画完成后重置标记
      _isScrolling = false;
    });
  }

  /// 平台跳转内容列表项点击事件
  void _contentItemClick(MallCollectionContentItem item) {
    final jumpData = item.typeData?.jump;
    final url = item.typeData?.url;

    if (jumpData == null) {
      debugPrint('_contentItemClick: jumpData is null');
      return;
    }

    debugPrint('点击了: ${jumpData.desc} (type: ${jumpData.type}, url: $url)');

    // 使用智能跳转工具
    SmartJumpUtil.smartJump(
      ref: ref,
      context: context,
      jumpData: jumpData,
      fallbackUrl: url,
      isReturnUrl: false,
    );
  }
}
