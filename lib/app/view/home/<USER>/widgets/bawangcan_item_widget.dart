import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/bawangcan_progress_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/bawangcan_red_envelope_widget.dart';
import 'package:msmds_platform/config/global_config.dart';
import '../../../../repository/modals/fun/fun_free_lunch_response.dart';

class BaWangCanItemWidget extends StatelessWidget {
  final FreeLunchDetail item;
  final int index;
  final VoidCallback? onTap;

  const BaWangCanItemWidget({
    super.key,
    required this.item,
    required this.index,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final goodsData =
        item.activityList?.isNotEmpty == true ? item.activityList!.first : null;
    final isOutOfStock =
        goodsData?.inventory == null || goodsData!.inventory! <= 0;

    return GestureDetector(
      onTap: () {
        // 检查登录状态和库存
        if (GlobalConfig.bwcToken == null) {
          SmartDialog.showToast('登录过期，可退出APP重新登录');
          return;
        }
        if (isOutOfStock) {
          SmartDialog.showToast('优惠已抢光～');
          return;
        }
        onTap?.call();
      },
      child: Stack(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Stack(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 商品图片
                    ClipRRect(
                      borderRadius: BorderRadius.circular(10.r),
                      child: CachedNetworkImage(
                        imageUrl: item.picture ?? '',
                        width: 96.w,
                        height: 96.h,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          width: 96.w,
                          height: 96.h,
                          color: Colors.grey[200],
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    // 内容区域
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 商家名称
                          Text(
                            item.name ?? '',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.black,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 6.h),
                          // 平台和距离
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Image.network(
                                    item.type == 1
                                        ? 'https://img.yunzhanxinxi.com/static/personal/bwc/<EMAIL>'
                                        : 'https://img.yunzhanxinxi.com/static/home/<USER>/202401/20240108113502407.png',
                                    width: 14.w,
                                    height: 14.h,
                                  ),
                                  SizedBox(width: 4.w),
                                  Text(
                                    item.type == 1 ? '美团' : '饿了么',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: const Color(0xFF999999),
                                    ),
                                  ),
                                ],
                              ),
                              Text(
                                '距离 ${item.deliveryDistance ?? ''}',
                                style: TextStyle(
                                  fontSize: 11.sp,
                                  color: const Color(0xFF999999),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 8.h),
                          // 返现信息
                          Row(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 5.w,
                                  vertical: 2.h,
                                ),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: const Color(0xFFF73301)),
                                  borderRadius: BorderRadius.circular(4.r),
                                ),
                                child: Text(
                                  _getCommissionText(goodsData),
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    color: const Color(0xFFF73301),
                                  ),
                                ),
                              ),
                              SizedBox(width: 6.w),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 5.w,
                                  vertical: 2.h,
                                ),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: goodsData?.planActivityType == 1
                                        ? const Color(0xFFFE7801)
                                        : const Color(0xFF43CE9A),
                                  ),
                                  borderRadius: BorderRadius.circular(4.r),
                                ),
                                child: Text(
                                  goodsData?.planActivityType == 1
                                      ? '需用餐评价'
                                      : '无需评价',
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    color: goodsData?.planActivityType == 1
                                        ? const Color(0xFFFE7801)
                                        : const Color(0xFF25C68A),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 7.h),
                          // 红包膨胀组件
                          BaWangCanRedEnvelopeWidget(goodsData: goodsData),
                          SizedBox(height: 7.h),
                          // 抢单时间
                          Text(
                            '抢单时间：00:00-23:59',
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: const Color(0xFF999999),
                            ),
                          ),
                          SizedBox(height: 5.h),
                          // 进度条和剩余数量
                          Row(
                            children: [
                              BaWangCanProgressWidget(
                                totalInventory: goodsData?.totalInventory,
                                inventory: goodsData?.inventory,
                              ),
                              SizedBox(width: 10.w),
                              Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text: '还剩 ',
                                      style: TextStyle(
                                        fontSize: 11.sp,
                                        color: const Color(0xFF999999),
                                      ),
                                    ),
                                    TextSpan(
                                      text: '${goodsData?.inventory ?? 0}',
                                      style: TextStyle(
                                        fontSize: 11.sp,
                                        color: const Color(0xFFFE7801),
                                      ),
                                    ),
                                    TextSpan(
                                      text: ' 份',
                                      style: TextStyle(
                                        fontSize: 11.sp,
                                        color: const Color(0xFF999999),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // 抢单按钮
                Positioned(
                  right: 0,
                  bottom: 2.h,
                  child: Container(
                    width: 62.w,
                    height: 32.h,
                    decoration: BoxDecoration(
                      color: isOutOfStock
                          ? const Color(0xFFEEEEEE)
                          : const Color(0xFFF93324),
                      borderRadius: BorderRadius.circular(32.r),
                    ),
                    child: Center(
                      child: Text(
                        isOutOfStock ? '已抢光' : '去抢单',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: isOutOfStock
                              ? const Color(0xFF9A9A9A)
                              : Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 多活动标识
          if ((item.activityList?.length ?? 0) > 1)
            Positioned(
              top: 4,
              left: 10,
              child: Container(
                width: 68.w,
                height: 22.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 0,
                      offset: const Offset(1, 1),
                    ),
                  ],
                  borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(8.r),
                    topLeft: Radius.circular(8.r),
                  ),
                ),
                child: Center(
                  child: Text(
                    '共${item.activityList?.length}个活动',
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _getCommissionText(FreeLunchActivity? goodsData) {
    if (goodsData == null) return '';

    if (goodsData.commissionThresholdCent != null) {
      return '实付满¥${goodsData.commissionThresholdCent}返¥${goodsData.commission}';
    } else {
      return '按实付${goodsData.ratio}%返，最高¥${goodsData.maxCommission}';
    }
  }
}
