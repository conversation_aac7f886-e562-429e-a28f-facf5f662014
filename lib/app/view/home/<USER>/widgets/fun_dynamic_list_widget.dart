import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/fun/bawangcan_countdown_provider.dart';

import '../../../../provider/fun/fun_list_provider.dart';
import 'meituan_group_list_widget.dart';
import 'bawangcan_list_widget.dart';
import 'location_permission_widget.dart';

class FunDynamicListWidget extends ConsumerStatefulWidget {
  const FunDynamicListWidget({super.key});

  @override
  ConsumerState<FunDynamicListWidget> createState() =>
      _FunDynamicListWidgetState();
}

class _FunDynamicListWidgetState extends ConsumerState<FunDynamicListWidget> {
  ScrollController? _scrollController;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 获取外层CustomScrollView的ScrollController
    _scrollController = PrimaryScrollController.maybeOf(context);
  }

  @override
  Widget build(BuildContext context) {
    final tabConfigState = ref.watch(manageFunTabConfigProvider);
    final tabSelection = ref.watch(tabSelectionStateProvider);
    final locationState = ref.watch(locationManagerProvider);

    return tabConfigState.when(
      data: (tabConfig) {
        if (tabConfig == null) {
          return SliverToBoxAdapter(
            child: _buildErrorWidget('标签配置加载失败'),
          );
        }

        // 获取当前选中的主标签和子标签
        final mainTabIndex = tabSelection.mainTabIndex;
        final childTabIndex = tabSelection.childTabIndex;
        debugPrint("当前标签索引: $mainTabIndex, $childTabIndex");

        if (mainTabIndex >= tabConfig.mainTab.tabs.length ||
            childTabIndex >= tabConfig.childrenTab.tabs.length) {
          return SliverToBoxAdapter(
            child: _buildErrorWidget('标签索引超出范围'),
          );
        }

        final currentMainTab = tabConfig.mainTab.tabs[mainTabIndex];
        final currentChildTab = tabConfig.childrenTab.tabs[childTabIndex];
        final currentSort = tabConfig.currentSort;
        debugPrint("当前标签信息${currentMainTab.code} ${currentSort?.sortName}");

        // 检查标签类型并显示相应内容
        final tabCode = currentMainTab.code ?? '';
        debugPrint('tabCode: $tabCode ');

        // 检查位置权限状态（当列表只有一项且没有位置数据时）
        if (locationState.isLoading) {
          // 正在获取位置
          return SliverToBoxAdapter(
            child: LocationPermissionWidget(
              state: LocationPermissionState.loading,
              onButtonPressed: () {},
            ),
          );
        }

        if (!locationState.hasLocation && locationState.hasError) {
          // 位置获取失败
          final isPermissionError = locationState.isPermissionError;
          return SliverToBoxAdapter(
            child: LocationPermissionWidget(
              state: isPermissionError
                  ? LocationPermissionState.noPermission
                  : LocationPermissionState.failed,
              onButtonPressed: () {
                // 重新请求位置
                ref.read(locationManagerProvider.notifier).refreshLocation();
              },
            ),
          );
        }

        // 有位置数据，显示正常列表
        final latitude = locationState.latitude;
        final longitude = locationState.longitude;

        if (tabCode == 'bawangcan_sort') {
          // 加载倒计时提醒数据（延迟到构建完成后执行，避免在 build 方法中修改状态）
          Future.microtask(() {
            ref
                .read(bawangcanCountdownProvider.notifier)
                .loadFirstRegisteredRecord();
          });
          final widgetKey = ValueKey('${currentMainTab.code}_${currentChildTab.code}_${currentSort?.sortField ?? 'default'}');
          debugPrint('当前经纬度🌍 - 纬度: $latitude, 经度: $longitude');
          // 霸王餐功能
          return BaWangCanListWidget(
            key: widgetKey,
            mainTabConfig: currentMainTab,
            childTabConfig: currentChildTab,
            latitude: latitude,
            longitude: longitude,
            sortOption: currentSort,
            scrollController: _scrollController,
          );
        } else if (tabCode == 'meituan_groupBuying' || tabCode == 'meituan_sharpshooter') {
          // 生成唯一的key来确保标签切换时重新创建widget
          final widgetKey = ValueKey('${currentMainTab.code}_${currentChildTab.code}_${currentSort?.sortField ?? 'default'}');
          debugPrint('当前经纬度🌍 - 纬度: $latitude, 经度: $longitude');
          return MeituanGroupListWithPaginationWidget(
            key: widgetKey,
            mainTabConfig: currentMainTab,
            childTabConfig: currentChildTab,
            latitude: latitude,
            longitude: longitude,
            sortOption: currentSort,
            scrollController: _scrollController, // 传入外层的ScrollController
          );
        } else {
          // 其他标签显示占位符
          return SliverToBoxAdapter(
            child: _buildPlaceholderContent('请选择相关标签查看内容'),
          );
        }
      },
      loading: () => SliverToBoxAdapter(
        child: Container(),
      ),
      error: (error, stackTrace) => SliverToBoxAdapter(
        child: _buildErrorWidget('加载失败: $error'),
      ),
    );
  }
  /// 构建错误提示组件
  Widget _buildErrorWidget(String message) {
    return Container(
      margin: EdgeInsets.only(top: 80.h),
      alignment: Alignment.center,
      child: Text(
        message,
        style: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFF999999),
        ),
      ),
    );
  }
  Widget _buildPlaceholderContent(String text) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Center(
        child: Text(
          text,
          style: const TextStyle(fontSize: 16, color: Colors.grey),
        ),
      ),
    );
  }
}
