import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/fun/bawangcan_countdown_provider.dart';
import 'package:msmds_platform/app/provider/fun/bawangcan_registration_provider.dart';
import 'package:msmds_platform/app/repository/modals/fun/bawangcan_registration_record.dart';
import 'package:msmds_platform/utils/router_util.dart';

/// 霸王餐新报名提醒组件
///
/// 显示已报名但未下单的倒计时提醒卡片
/// Timer 在 Provider 中管理，Widget dispose 时调用 Provider 停止倒计时
class BawangcanNewRegistrationWidget extends ConsumerStatefulWidget {
  const BawangcanNewRegistrationWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _BawangcanNewRegistrationWidgetState();
}

class _BawangcanNewRegistrationWidgetState
    extends ConsumerState<BawangcanNewRegistrationWidget> {
  @override
  void initState() {
    super.initState();
    // 延迟启动倒计时，避免在 build 期间修改状态
    Future.microtask(() {
      if (mounted) {
        ref.read(bawangcanCountdownProvider.notifier).startCountdown();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final countdownState = ref.watch(bawangcanCountdownProvider);
    final record = countdownState.reminderRecord;

    if (!countdownState.hasActiveCountdown || record == null) {
      return const SizedBox.shrink();
    }

    final countdown = countdownState.formattedCountdown;
    return GestureDetector(
      onTap: () => _handleRegistrationReminderTap(record),
      child: Container(
        width: 186.w,
        height: 46.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(46.r),
          color: Colors.black.withOpacity(0.66),
        ),
        child: Row(
          children: [
            // 左侧店铺头像和文字
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Row(
                  children: [
                    // 店铺头像
                    Container(
                      width: 37.w,
                      height: 37.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(38.r),
                        color: Colors.white,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(38.r),
                        child: CachedNetworkImage(
                          imageUrl: record.applyOrderShop?.shopImg ?? '',
                          width: 35.w,
                          height: 35.h,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) => Container(
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(38.r),
                            ),
                            child: Icon(
                              Icons.store,
                              size: 20.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 7.w),
                    // 文字信息
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '前往下单',
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 6.h),
                          RichText(
                            text: TextSpan(
                              style: TextStyle(
                                fontSize: 9.sp,
                                color: Colors.white,
                              ),
                              children: [
                                const TextSpan(text: '预计'),
                                TextSpan(
                                  text: countdown,
                                  style: const TextStyle(
                                    color: Color(0xFFFF6F1F),
                                  ),
                                ),
                                const TextSpan(text: '后超时'),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // 右侧箭头
            Padding(
                padding: EdgeInsets.only(right: 15.w),
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: 12.sp,
                  color: Colors.white,
                )),
          ],
        ),
      ),
    );
  }

  void _handleRegistrationReminderTap(BawangcanRegistrationRecord record) {
    RouterUtil.checkLogin(
      context,
      execute: true,
      call: () => _launchMiniProgram(record),
    );
  }

  Future<void> _launchMiniProgram(BawangcanRegistrationRecord record) async {
    await ref.read(bawangcanRegistrationProvider.notifier).goToPlaceOrder(
          context: context,
          ref: ref,
          applyType: record.applyOrderShop?.applyType ?? 0,
          wxPath: record.applyOrderShop?.actionUrl?.wxPath,
          activityId: record.activityId,
          shopId: record.shopId,
        );
  }
}
