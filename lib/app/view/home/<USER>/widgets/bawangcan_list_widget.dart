import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'paginated_sliver_list_widget.dart';
import '../../../../repository/modals/fun/fun_free_lunch_response.dart';
import '../../../../provider/fun/bawangcan_provider.dart';
import '../../../../repository/modals/fun/fun_tab_config.dart';
import '../../../../navigation/router.dart';
import '../bawangcan_detail_page.dart';
import 'bawangcan_item_widget.dart';

class BaWangCanListWidget extends ConsumerWidget {
  final FunTabConfig mainTabConfig;
  final FunTabConfig childTabConfig;
  final double? latitude;
  final double? longitude;
  final FunSortOption? sortOption;
  final ScrollController? scrollController;

  const BaWangCanListWidget({
    super.key,
    required this.mainTabConfig,
    required this.childTabConfig,
    this.latitude,
    this.longitude,
    this.sortOption,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final baWangCanListAsync = ref.watch(baWangCanListProvider(
      mainTabConfig,
      childTabConfig,
      latitude: latitude,
      longitude: longitude,
      sortOption: sortOption,
    ));

    // 使用 read 获取 notifier，用于调用方法
    final notifier = ref.read(baWangCanListProvider(
      mainTabConfig,
      childTabConfig,
      latitude: latitude,
      longitude: longitude,
      sortOption: sortOption,
    ).notifier);

    return baWangCanListAsync.when(
      data: (items) {
        return PaginatedSliverListWidget<FreeLunchDetail>(
          items: items,
          scrollController: scrollController,
          isLoading: notifier.isLoadingMore,
          hasMore: notifier.hasMore,
          onLoadMore: () => notifier.loadMore(),
          itemBuilder: (context, item, index) {
            return BaWangCanItemWidget(
              item: item,
              index: index,
              onTap: () => _handleItemTap(context, item, index, ref),
            );
          },
        );
      },
      loading: () => const SliverToBoxAdapter(
        child: SizedBox(
          height: 100,
          child: Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: Color(0xFFF94B3D),
            ),
          ),
        ),
      ),
      error: (error, stackTrace) => SliverToBoxAdapter(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Column(
              children: [
                Text(
                  '加载失败: $error',
                  style: const TextStyle(color: Colors.red),
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleItemTap(
      BuildContext context, FreeLunchDetail item, int index, WidgetRef ref) {
    // 获取美团PV ID
    final meituanPvId = ref
        .read(baWangCanListProvider(
          mainTabConfig,
          childTabConfig,
          latitude: latitude,
          longitude: longitude,
          sortOption: sortOption,
        ).notifier)
        .meituanPvId;

    // 导航到霸王餐详情页
    Navigator.of(context).pushNamed(
      CsRouter.baWangCanDetailPage,
      arguments: BaWangCanDetailArguments(
        item: item,
        meituanPvId: meituanPvId,
        latitude: latitude,
        longitude: longitude,
      ),
    );
  }
}
