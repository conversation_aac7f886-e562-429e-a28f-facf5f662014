import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/fun/bawangcan_countdown_provider.dart';
import 'package:msmds_platform/app/provider/fun/bawangcan_provider.dart';
import 'package:msmds_platform/app/provider/fun/bawangcan_registration_provider.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/bawangcan_activity_card_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/bawangcan_cashback_progress_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/bawangcan_rules_widget.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/config/global_config.dart';
import '../../../repository/modals/fun/fun_free_lunch_response.dart';
import 'dialog/order_grabbing_dialog.dart';
import 'dialog/confirm_phone_number_dialog.dart';
import 'dialog/confirm_signature_dialog.dart';
import 'dialog/receive_red_envelope_dialog.dart';

/// 霸王餐活动报名详情页
///
/// 功能包括：
/// - 显示霸王餐活动详情
/// - 活动报名
/// - 跳转到外卖平台（美团/饿了么）小程序
/// - 显示返现进度
class BaWangCanDetailPage extends ConsumerStatefulWidget {
  final BaWangCanDetailArguments arguments;

  const BaWangCanDetailPage({
    super.key,
    required this.arguments,
  });

  @override
  ConsumerState<BaWangCanDetailPage> createState() =>
      _BaWangCanDetailPageState();
}

class _BaWangCanDetailPageState extends ConsumerState<BaWangCanDetailPage>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;
  late final ScrollController _scrollController;
  bool _isTabScrollInProgress = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabChange);
    _scrollController = ScrollController();
    _scrollController.addListener(_handleScroll);

    // 初始化 provider 数据并查询报名状态
    _initializeProviderData();
  }

  /// 初始化 provider 数据并查询报名状态
  Future<void> _initializeProviderData() async {
    final provider = ref.read(bawangcanActivityManageProvider.notifier);
    provider.initializeData(widget.arguments.item);
    await provider.checkAllActivitiesApplyStatus();
  }

  /// 倒计时结束时的回调
  void _handleCountdownFinish() {
    ref.read(bawangcanActivityManageProvider.notifier).handleCountdownFinish();
  }

  Widget _buildTabBar() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 60.w),
      child: TabBar(
        controller: _tabController,
        onTap: _handleTabTap,
        labelColor: Colors.black,
        unselectedLabelColor: Colors.black,
        labelStyle: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
        ),
        indicator: UnderlineTabIndicator(
          borderRadius: BorderRadius.all(Radius.circular(5.r)),
          borderSide: BorderSide(width: 3.w, color: const Color(0xFFF93324)),
          insets: EdgeInsets.symmetric(horizontal: 30.w, vertical: 5.w),
        ),
        indicatorSize: TabBarIndicatorSize.label,
        tabs: const [
          Tab(text: '返利'),
          Tab(text: '规则'),
        ],
      ),
    );
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      return;
    }
    if (_tabController.index == 1 &&
        !ref.read(bawangcanRulesExpandedProvider)) {
      ref.read(bawangcanRulesExpandedProvider.notifier).setExpanded(true);
    }
  }

  /// 监听滚动事件-如果滚到顶部,自动切换到返利 Tab
  void _handleScroll() {
    if (_isTabScrollInProgress || !_scrollController.hasClients) {
      return;
    }

    if (_scrollController.offset <= 5 &&
        _tabController.index != 0 &&
        !_tabController.indexIsChanging) {
      _tabController.animateTo(0);
    }
  }

  Future<void> _animateScrollTo(double offset) async {
    if (!_scrollController.hasClients) {
      return;
    }

    final maxExtent = _scrollController.position.maxScrollExtent;
    final target = offset.clamp(0.0, maxExtent).toDouble();

    _isTabScrollInProgress = true;
    try {
      await _scrollController.animateTo(
        target,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } finally {
      _isTabScrollInProgress = false;
    }
  }

  /// 滚动到报名规则
  void _handleTabTap(int index) {
    if (index == 1) {
      if (!ref.read(bawangcanRulesExpandedProvider)) {
        // 展开规则
        ref.read(bawangcanRulesExpandedProvider.notifier).setExpanded(true);
      }

      final activityCount = widget.arguments.item.activityList?.length ?? 0;
      final targetOffset = activityCount > 1 ? 540.h : 360.h;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        _animateScrollTo(targetOffset);
      });
      return;
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final goodsData = widget.arguments.item;

    return Scaffold(
      backgroundColor: const Color(0xFFF4F4F2),
      appBar: AppBar(
        title: const Text('活动报名'),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Stack(
          children: [
            CachedNetworkImage(
                imageUrl:
                    'https://alicdn.msmds.cn/APPSHOW/gradient_bj_img.png'),
            Column(
              children: [
                _buildShopInfoCard(goodsData),
                // 根据报名状态显示不同内容
                Consumer(
                  builder: (context, ref, child) {
                    final applyStatus = ref
                        .watch(bawangcanActivityManageProvider.notifier)
                        .applyStatus;
                    if (applyStatus != null) {
                      // 已报名,显示返现进度
                      return _buildAppliedStatusSection(goodsData);
                    } else {
                      // 未报名,显示活动列表和规则
                      return Column(
                        children: [
                          _buildTabBar(),
                          const SizedBox(height: 8),
                          _buildActivityList(goodsData),
                          const SizedBox(height: 8),
                          _buildRulesSection(),
                        ],
                      );
                    }
                  },
                ),
                const SizedBox(height: 80), // 底部按钮的空间
              ],
            ),
          ],
        ),
      ),
      bottomNavigationBar: Consumer(
        builder: (context, ref, child) {
          final applyStatus =
              ref.watch(bawangcanActivityManageProvider.notifier).applyStatus;
          return applyStatus != null
              ? const SizedBox.shrink()
              : _buildBottomBar(goodsData);
        },
      ),
    );
  }

  /// 构建商铺信息卡片
  Widget _buildShopInfoCard(FreeLunchDetail goodsData) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(10, 10, 10, 5),
      child: Container(
        padding: EdgeInsets.all(10.w),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 商铺图片
            ClipRRect(
              borderRadius: BorderRadius.circular(10.r),
              child: Image.network(
                goodsData.picture ?? '',
                width: 86.w,
                height: 86.w,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 86.w,
                    height: 86.w,
                    color: Colors.grey[300],
                    child: const Icon(Icons.image_not_supported),
                  );
                },
              ),
            ),
            SizedBox(width: 12.w),
            // 商铺信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          goodsData.name ?? '',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // 复制按钮
                      GestureDetector(
                        onTap: _copyToClipboard,
                        child: CachedNetworkImage(
                          imageUrl:
                              'https://alicdn.msmds.cn/APPSHOW/copy_icon_img.png',
                          width: 14.w,
                          height: 14.w,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),
                  Row(
                    children: [
                      Image.network(
                        goodsData.type == 1
                            ? 'https://img.yunzhanxinxi.com/static/personal/bwc/<EMAIL>'
                            : 'https://img.yunzhanxinxi.com/static/home/<USER>/202401/20240108113502407.png',
                        width: 14.w,
                        height: 14.w,
                        errorBuilder: (context, error, stackTrace) =>
                            const SizedBox.shrink(),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        goodsData.type == 1 ? '美团' : '饿了么',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFF999999),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '距离 ${goodsData.deliveryDistance ?? ""}',
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: const Color(0xFF999999),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20.h),
                  InkWell(
                    onTap: () {
                      final provider =
                          ref.watch(bawangcanActivityManageProvider.notifier);
                      final state = ref.read(bawangcanActivityManageProvider);
                      final applyStatus = provider.applyStatus;
                      // 获取 activityId：
                      // 1. 如果已报名，优先使用 applyStatus 中的 activityId
                      // 2. 如果未报名，使用当前选中活动的 activityId
                      String? activityId = applyStatus?.activityId;
                      if (activityId == null || activityId.isEmpty) {
                        final activityList = goodsData.activityList ?? [];
                        final selectedIndex = state.selectedActivityIndex;
                        if (selectedIndex < activityList.length) {
                          activityId = activityList[selectedIndex].activityId;
                        }
                      }
                      // 跳转到商品详情页
                      ref
                          .read(bawangcanRegistrationProvider.notifier)
                          .goToPlaceOrder(
                            context: context,
                            ref: ref,
                            applyType: goodsData.type ?? 0,
                            wxPath: goodsData.actionUrl?.wxPath,
                            activityId: activityId,
                            shopId: applyStatus?.shopId ?? goodsData.shopId,
                          );
                    },
                    child: Text(
                      '先进店查看商品(非点餐入口) >',
                      style: TextStyle(
                          fontSize: 10.sp, color: const Color(0xFFFF7A00)),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建活动列表
  Widget _buildActivityList(FreeLunchDetail goodsData) {
    final activityList = goodsData.activityList ?? [];

    if (activityList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: Column(
        children: activityList.asMap().entries.map((entry) {
          final index = entry.key;
          final activity = entry.value;
          final state = ref.watch(bawangcanActivityManageProvider);
          final selectedIndex = state.selectedActivityIndex;
          final isSelected = selectedIndex == index;

          return GestureDetector(
            onTap: () {
              ref
                  .read(bawangcanActivityManageProvider.notifier)
                  .setIndex(index);
            },
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 10.h),
                  padding: EdgeInsets.all(10.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFFF93324)
                          : Colors.transparent,
                      width: 1.w,
                    ),
                  ),
                  child: BawangcanActivityCardWidget(
                      activity: activity, isSelected: isSelected),
                ),

                // 活动标签
                Positioned(
                  top: -5,
                  left: -5,
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFD982),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      '活动${index + 1}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建已报名状态区域 - 显示返现进度
  Widget _buildAppliedStatusSection(FreeLunchDetail goodsData) {
    final provider = ref.watch(bawangcanActivityManageProvider.notifier);
    final applyStatus = provider.applyStatus;
    if (applyStatus == null) {
      return const SizedBox.shrink();
    }

    final activityList = goodsData.activityList ?? [];
    final state = ref.watch(bawangcanActivityManageProvider);
    final selectedIndex = state.selectedActivityIndex;
    if (selectedIndex >= activityList.length) {
      return const SizedBox.shrink();
    }

    final selectedActivity = activityList[selectedIndex];

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: Column(
        children: [
          const SizedBox(height: 8),
          // 活动要求卡片
          _buildActivityRequirementCard(selectedActivity),
          const SizedBox(height: 10),
          // 返现进度卡片
          BawangcanCashbackProgress(
            goodsData: goodsData,
            activity: selectedActivity,
            applyStatus: applyStatus,
            onCountdownFinish: _handleCountdownFinish,
          ),
        ],
      ),
    );
  }

  /// 构建活动要求卡片
  Widget _buildActivityRequirementCard(FreeLunchActivity activity) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '活动要求',
            style: TextStyle(
              fontSize: 15.sp,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 6.h),
          BawangcanActivityCardWidget(
            activity: activity,
            isShowTips: true,
          ),
        ],
      ),
    );
  }

  /// 构建规则说明区域
  Widget _buildRulesSection() {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 10.w),
        padding: EdgeInsets.all(14.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: const BawangcanRulesWidget());
  }

  /// 构建底部操作栏
  Widget _buildBottomBar(FreeLunchDetail goodsData) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 霸王餐按钮
            SizedBox(width: 24.w),
            _buildBottomButton(
              icon: bwcIcon,
              label: '霸王餐',
              onTap: () {
                Navigator.pop(context);
              },
            ),
            SizedBox(width: 24.w),
            // 常见问题按钮
            _buildBottomButton(
              icon: cjwtIcon,
              label: '常见问题',
              onTap: () {
                Navigator.of(context).pushNamed(
                  CsRouter.frequentlyQuestionsPage,
                );
              },
            ),
            SizedBox(width: 24.w),
            // 立即抢单按钮
            Expanded(
              child: GestureDetector(
                onTap: () {
                  _handleGrabOrder(goodsData);
                },
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 40.w, vertical: 10.h),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF93324),
                    borderRadius: BorderRadius.circular(36.r),
                  ),
                  child: Text(
                    '立即抢单',
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建底部小按钮
  Widget _buildBottomButton({
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            icon,
            width: 20,
          ),
          SizedBox(height: 4.h),
          Text(
            label,
            style: TextStyle(
              fontSize: 10.sp,
              color: const Color(0xFF979797),
            ),
          ),
        ],
      ),
    );
  }

  /// 处理抢单操作
  void _handleGrabOrder(FreeLunchDetail goodsData) {
    final activityList = goodsData.activityList ?? [];
    final state = ref.read(bawangcanActivityManageProvider);
    final selectedIndex = state.selectedActivityIndex;
    if (activityList.isEmpty || selectedIndex >= activityList.length) {
      return;
    }

    final selectedActivity = activityList[selectedIndex];
    final phone = GlobalConfig.account?.data?.encryptionPhone ?? '';
    // 美团流程
    if (goodsData.type == 1) {
      _showMeituanFlow(phone, goodsData, selectedActivity);
    } else {
      // 饿了么流程
      _showElemeFlow(phone, goodsData, selectedActivity);
    }
  }

  /// 美团抢单流程
  void _showMeituanFlow(String phone, FreeLunchDetail goodsData,
      FreeLunchActivity selectedActivity) {
    // 1. 显示手机号确认弹窗
    OrderGrabbingDialog.show(
      context,
      userPhone: phone,
      platformType: 1,
    ).then((phone) {
      if (phone != null) {
        // 2. 显示活动确认弹窗
        ConfirmSignatureDialog.show(
          context,
          shopData: goodsData,
          selectData: selectedActivity,
          onConfirm: () {
            // 3. 调用报名接口
            _grabOrdersClick(phone, goodsData, selectedActivity);
          },
        );
      }
    });
  }

  /// 饿了么抢单流程
  void _showElemeFlow(String phone, FreeLunchDetail goodsData,
      FreeLunchActivity selectedActivity) {
    // 1. 显示饿了么叠红包提示
    ConfirmPhoneNumberDialog.show(
      context,
      onConfirm: () {
        // 2. 显示手机号确认弹窗
        OrderGrabbingDialog.show(
          context,
          userPhone: phone,
          platformType: 2,
        ).then((phone) {
          if (phone != null) {
            // 3. 显示活动确认弹窗
            ConfirmSignatureDialog.show(
              context,
              shopData: goodsData,
              selectData: selectedActivity,
              onConfirm: () {
                // 4. 调用报名接口
                _grabOrdersClick(phone, goodsData, selectedActivity);
              },
            );
          }
        });
      },
    );
  }

  /// 调用报名接口
  Future<void> _grabOrdersClick(String phone, FreeLunchDetail goodsData,
      FreeLunchActivity selectedActivity) async {
    try {
      SmartDialog.showLoading(msg: '报名中...');

      final provider = ref.read(bawangcanActivityManageProvider.notifier);
      final success = await provider.applyActivity(
        phone: phone,
        goodsData: goodsData,
        selectedActivity: selectedActivity,
        latitude: widget.arguments.latitude ?? 0.0,
        longitude: widget.arguments.longitude ?? 0.0,
        meituanPvId: widget.arguments.meituanPvId,
      );

      SmartDialog.dismiss();

      if (success) {
        SmartDialog.showToast('报名成功!');
        await ref.read(bawangcanCountdownProvider.notifier).refreshCountdown();
        _showReceiveRedEnvelopeDialog(goodsData.type ?? 1);
      }
    } catch (e) {
      SmartDialog.dismiss();
      SmartDialog.showToast('报名失败');
    }
  }

  void _copyToClipboard() {
    Clipboard.setData(ClipboardData(text: widget.arguments.item.name ?? ''));
    SmartDialog.showToast('复制成功');
  }

  void _showReceiveRedEnvelopeDialog(int platformType) {
    ReceiveRedEnvelopeDialog.show(
      platformType: platformType,
      onReceive: () =>
          ref.read(bawangcanRegistrationProvider.notifier).receiveRedPacket(
                context: context,
                ref: ref,
                applyType: platformType,
              ),
    );
  }
}

/// 霸王餐详情页参数
class BaWangCanDetailArguments {
  final FreeLunchDetail item;
  final String? meituanPvId;
  final double? latitude;
  final double? longitude;

  const BaWangCanDetailArguments({
    required this.item,
    this.meituanPvId,
    this.latitude,
    this.longitude,
  });
}
