import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/provider/fun/fun_list_provider.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/bawangcan_new_registration_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/dynamic_sliver_persistent_header.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/fun_dynamic_list_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/mall_Collection_widget.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/utils/prefs_util.dart';
import 'package:msmds_platform/utils/router_util.dart';

/// 重构后的 FunPage - 使用纯 Riverpod 状态管理
class FunPage extends ConsumerStatefulWidget {
  const FunPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _FunPageState();
}

class _FunPageState extends ConsumerState<FunPage> {
  final ScrollController _scrollController = ScrollController();

  /// 视频教程是否已关闭（true：已关闭显示简化版，false：未关闭显示完整版）
  bool _isVideoTutorialClosed = false;

  @override
  void initState() {
    super.initState();
    _loadVideoTutorialState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// 从本地存储加载视频教程状态
  Future<void> _loadVideoTutorialState() async {
    final prefsKey = _videoTutorialPrefsKey();
    bool? isClosed = PrefsUtil().getBool(prefsKey);

    if (mounted) {
      setState(() {
        _isVideoTutorialClosed = isClosed ?? false;
      });
    }
  }

  /// 用户级别的视频教程存储键
  String _videoTutorialPrefsKey() {
    final userId = ref.read(authProvider)?.data?.userId;
    return PrefsKeys.funActivityShowVideoKey(userId: userId);
  }

  /// 点击位置按钮
  void _onLocationTap() {
    ref.read(locationManagerProvider.notifier).refreshLocation();
  }

  @override
  Widget build(BuildContext context) {
    // 获取当前选中的主标签页代码
    final currentTabCode = ref.watch(currentMainTabCodeProvider);

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: _buildHeader(),
        backgroundColor: Colors.transparent,
        elevation: 0,
        toolbarHeight: 39,
        centerTitle: true,
        titleSpacing: 10,
      ),
      backgroundColor: const Color(0xFFF4F4F2),
      body: Stack(
        clipBehavior: Clip.none,
        children: [
          // 背景图片
          Positioned(
            child: CachedNetworkImage(
              imageUrl: 'https://alicdn.msmds.cn/APPSHOW/fun_activity_bg.png',
              width: double.infinity,
              height: 370,
              fit: BoxFit.cover,
            ),
          ),
          SafeArea(
            child: PrimaryScrollController(
              controller: _scrollController,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // 头部组件（视频教程 + 返现平台）
                  SliverToBoxAdapter(
                    child: _buildHeaderComponents(),
                  ),
                  // 使用动态高度的SliverPersistentHeader
                  const DynamicSliverPersistentHeader(),
                  const FunDynamicListWidget(),
                ],
              ),
            ),
          ),
          // 条件性显示霸王餐报名记录浮动按钮
          if (currentTabCode == 'bawangcan_sort') ...[
            Positioned(
              bottom: 120,
              right: 10,
              child: _buildBmjlFloatButton(),
            ),
            // 新的报名提醒组件（在浮动按钮下方）
            const Positioned(
              bottom: 60,
              right: 10,
              child: BawangcanNewRegistrationWidget(),
            ),
          ]
        ],
      ),
    );
  }

  /// 构建霸王餐报名记录浮动按钮
  Widget _buildBmjlFloatButton() {
    return InkWell(
      onTap: () {
        // 检查登录状态，登录后跳转到报名记录页面
        RouterUtil.checkLogin(
          context,
          call: () {
            Navigator.of(context).pushNamed(
              CsRouter.bawangcanRegistrationRecordPage,
            );
          },
        );
      },
      borderRadius: BorderRadius.circular(8),
      child: CachedNetworkImage(
        imageUrl: '${Constant.msmdsAliCdn}/APPSHOW/bmjl_icon_img.png',
        height: 52,
        width: 54,
        fit: BoxFit.contain,
        errorWidget: (context, url, error) => Container(
          width: 54,
          height: 52,
          decoration: BoxDecoration(
            color: const Color(0xFFFF6B35),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.list_alt,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    // 获取位置状态
    final locationState = ref.watch(locationManagerProvider);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CachedNetworkImage(
          imageUrl:
              "https://alicdn.msmds.cn/APPSHOW/fun_activity_title_new.png",
          width: 131,
        ),
        InkWell(
          onTap: _onLocationTap,
          child: Row(
            children: [
              Icon(
                Icons.room,
                color: Colors.white,
                size: 16.w,
              ),
              SizedBox(width: 5.w),
              // 根据加载状态显示不同内容
              Text(
                locationState.locationName,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14.sp,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderComponents() {
    return Column(
      children: [
        // 视频教程组件
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          alignment: Alignment.center,
          child: _buildVideoTutorial(),
        ),
        // 返现平台视图
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          child: const MallCollectionWidget(),
        ),
      ],
    );
  }

  Widget _buildVideoTutorial() {
    // 根据 _isVideoTutorialClosed 状态显示不同的 UI
    if (_isVideoTutorialClosed) {
      // 简化版：显示步骤图 + 悬浮的"视频教程"按钮
      return Stack(
        children: [
          Container(
            margin: const EdgeInsets.all(10),
            alignment: Alignment.center,
            child: Image.network(
              'https://alicdn.msmds.cn/APPSHOW/fun_activity_step.png',
              fit: BoxFit.contain,
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: GestureDetector(
              onTap: () => _handleVideoTutorialTap(),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 3),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.network(
                      'https://alicdn.msmds.cn/APPSHOW/fun_activity_play_tag.png',
                      width: 14,
                      height: 14,
                      fit: BoxFit.contain,
                    ),
                    const SizedBox(width: 3),
                    const Text(
                      '视频教程',
                      style: TextStyle(color: Colors.black, fontSize: 11),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      // 完整版：显示视频预览图 + 播放按钮 + "关闭视频"按钮
      return GestureDetector(
        onTap: _handleVideoTutorialTap,
        child: Container(
          margin: EdgeInsets.only(top: 10.w),
          width: 355.w,
          height: 97.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.w),
            image: const DecorationImage(
              image: CachedNetworkImageProvider(
                'https://alicdn.msmds.cn/APPSHOW/fun_activity_video_guide_new.jpeg',
              ),
              fit: BoxFit.cover,
            ),
          ),
          child: Stack(
            children: [
              // 中央播放按钮
              Center(
                child: CachedNetworkImage(
                  imageUrl:
                      '${Constant.msmdsAliCdn}/APPSHOW/fun_activity_play.png',
                  width: 34.w,
                  height: 34.w,
                  fit: BoxFit.contain,
                ),
              ),
              // 右上角关闭按钮
              Positioned(
                right: 8.w,
                top: 8.w,
                child: GestureDetector(
                  onTap: _handleCloseVideoTap,
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.w),
                    decoration: BoxDecoration(
                      color: const Color(0x70000000),
                      borderRadius: BorderRadius.circular(8.w),
                    ),
                    child: Text(
                      '关闭视频',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 9.sp,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  /// 处理视频教程点击事件
  void _handleVideoTutorialTap() {
    // 导航到视频播放页面
    Navigator.of(context).pushNamed(CsRouter.funActivityIntroductPage);

    PrefsUtil().setBool(_videoTutorialPrefsKey(), true);

    setState(() {
      _isVideoTutorialClosed = true;
    });
  }

  /// 处理关闭视频按钮点击事件
  void _handleCloseVideoTap() {
    PrefsUtil().setBool(_videoTutorialPrefsKey(), true);

    setState(() {
      _isVideoTutorialClosed = true;
    });
  }
}
