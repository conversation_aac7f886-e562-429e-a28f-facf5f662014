import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

/// 霸王餐报名成功后显示的领奖红包弹窗
class ReceiveRedEnvelopeDialog extends StatefulWidget {
  const ReceiveRedEnvelopeDialog({
    super.key,
    required this.platformType,
    this.onReceive,
    this.onClose,
  });

  /// 平台类型：1-美团 2-饿了么
  final int platformType;

  /// 点击“去领取”按钮时回调
  final VoidCallback? onReceive;

  /// 点击关闭按钮时回调
  final VoidCallback? onClose;

  static const String _dialogTag = 'receive_red_envelope_dialog';

  /// 显示红包弹窗
  static void show({
    required int platformType,
    VoidCallback? onReceive,
    VoidCallback? onClose,
  }) {
    SmartDialog.show(
      tag: _dialogTag,
      keepSingle: true,
      clickMaskDismiss: false,
      builder: (_) => ReceiveRedEnvelopeDialog(
        platformType: platformType,
        onReceive: onReceive,
        onClose: onClose,
      ),
    );
  }

  /// 关闭红包弹窗
  static Future<void> dismiss() {
    return SmartDialog.dismiss(tag: _dialogTag);
  }

  @override
  State<ReceiveRedEnvelopeDialog> createState() =>
      _ReceiveRedEnvelopeDialogState();
}

class _ReceiveRedEnvelopeDialogState extends State<ReceiveRedEnvelopeDialog>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
      lowerBound: 0.0,
      upperBound: 1.0,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(_controller);
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final platformName = widget.platformType == 1 ? '美团' : '饿了么';

    return Material(
      color: Colors.black.withOpacity(0.72),
      child: Center(
        child: SizedBox(
          width: 312.w,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: GestureDetector(
                  onTap: _handleClose,
                  child: Image.network(
                    'https://alicdn.msmds.cn/APPSHOW/close_icon_img.png',
                    width: 11.5.w,
                    height: 11.5.w,
                  ),
                ),
              ),
              SizedBox(height: 8.h),
              Container(
                width: 312.w,
                height: 302.h,
                padding: EdgeInsets.only(top: 90.h),
                alignment: Alignment.topCenter,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: NetworkImage(
                      'https://alicdn.msmds.cn/APPSHOW/lhbzxd_bj_img.png',
                    ),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    _buildTimeline(platformName),
                    const Spacer(),
                    GestureDetector(
                      onTap: _handleReceive,
                      child: ScaleTransition(
                        scale: _scaleAnimation,
                        child: Image.network(
                          'https://alicdn.msmds.cn/APPSHOW/qulingqu_button_img.png',
                          width: 190.w,
                          height: 47.h,
                        ),
                      ),
                    ),
                    SizedBox(height: 27.h),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeline(String platformName) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStepText('活动报名成功', isCurrentStep: true),
              _buildStepLine(),
              _buildStepText('即将前往【$platformName】领平台红包'),
              _buildStepLine(),
              _buildStepText('领完红包后，关闭页面回来点击下单'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStepLine() {
    return Container(
      margin: EdgeInsets.only(left: 8.w),
      width: 1.w,
      height: 18.h,
      color: const Color(0xFFFF7B00),
    );
  }

  Widget _buildStepText(String text, {bool? isCurrentStep}) {
    return Row(
      children: [
        isCurrentStep == true
            ? CachedNetworkImage(
                imageUrl:
                    'https://alicdn.msmds.cn/APPSHOW/orange_select_img.png',
                width: 17.w,
                height: 17.w,
              )
            : Container(
                width: 17.w,
                height: 17.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(17.r),
                  border: Border.all(
                    color: const Color(0xFFFF7B00),
                    width: 1.w,
                  ),
                ),
              ),
        SizedBox(width: 10.w),
        Text(
          text,
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  Future<void> _handleReceive() async {
    widget.onReceive?.call();
    await ReceiveRedEnvelopeDialog.dismiss();
  }

  Future<void> _handleClose() async {
    widget.onClose?.call();
    await ReceiveRedEnvelopeDialog.dismiss();
  }
}
