import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


/// 用于饿了么平台的叠红包提示
class ConfirmPhoneNumberDialog extends StatelessWidget {
  final VoidCallback? onConfirm;

  const ConfirmPhoneNumberDialog({
    super.key,
    this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Container(
        color: Colors.black.withOpacity(0.3),
        alignment: Alignment.center,
        child: GestureDetector(
          onTap: () {}, // 阻止点击事件冒泡
          child: Container(
            width: 320.w,
            height: 325.h,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: NetworkImage(
                  'https://alicdn.msmds.cn/APPSHOW/elmdhb_tips_img.png',
                ),
                fit: BoxFit.fill,
              ),
            ),
            alignment: Alignment.bottomCenter,
            padding: EdgeInsets.only(bottom: 15.h),
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
                onConfirm?.call();
              },
              child: Container(
                width: 220.w,
                height: 40.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFF93324),
                  borderRadius: BorderRadius.circular(40.r),
                ),
                alignment: Alignment.center,
                child: Text(
                  '确认',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 显示弹窗
  static Future<void> show(
    BuildContext context, {
    VoidCallback? onConfirm,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => ConfirmPhoneNumberDialog(
        onConfirm: onConfirm,
      ),
    );
  }
}
