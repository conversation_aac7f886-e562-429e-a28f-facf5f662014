import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/bawangcan_activity_card_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/bawangcan_rules_widget.dart';
import '../../../../repository/modals/fun/fun_free_lunch_response.dart';

/// 霸王餐活动确认弹窗
class ConfirmSignatureDialog extends StatefulWidget {
  final FreeLunchDetail shopData;
  final FreeLunchActivity selectData;
  final VoidCallback? onConfirm;

  const ConfirmSignatureDialog({
    super.key,
    required this.shopData,
    required this.selectData,
    this.onConfirm,
  });

  @override
  State<ConfirmSignatureDialog> createState() => _ConfirmSignatureDialogState();

  /// 显示弹窗
  static Future<void> show(
    BuildContext context, {
    required FreeLunchDetail shopData,
    required FreeLunchActivity selectData,
    VoidCallback? onConfirm,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => ConfirmSignatureDialog(
        shopData: shopData,
        selectData: selectData,
        onConfirm: onConfirm,
      ),
    );
  }
}

class _ConfirmSignatureDialogState extends State<ConfirmSignatureDialog> {

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 主内容
        SafeArea(
          child: Container(
            width: double.infinity,
            height: 600.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Column(
              children: [
                // 滚动内容
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Column(
                      children: [
                        SizedBox(height: 2.h),
                        _buildShopInfo(),
                        BawangcanActivityCardWidget(
                          activity: widget.selectData,
                        ),
                        Container(
                          margin: EdgeInsets.symmetric(vertical: 10.h),
                          width: 343.w,
                          height: 1.h,
                          color: const Color(0xFFE5E5E5),
                        ),
                        const BawangcanRulesWidget()
                      ],
                    ),
                  ),
                ),
                // 底部抢单按钮
                Container(
                  padding: EdgeInsets.symmetric(vertical: 20.h),
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                      widget.onConfirm?.call();
                    },
                    child: Container(
                      width: 238.w,
                      height: 36.h,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF93324),
                        borderRadius: BorderRadius.circular(36.r),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        '立即抢单',
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建店铺信息
  Widget _buildShopInfo() {
    return Container(
      width: 343.w,
      padding: EdgeInsets.symmetric(vertical: 10.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: const Color(0xFFE5E5E5),
            width: 1.w,
          ),
        ),
      ),
      child: Row(
        children: [
          // 店铺图片
          ClipRRect(
            borderRadius: BorderRadius.circular(10.r),
            child: Image.network(
              widget.shopData.picture ?? '',
              width: 86.w,
              height: 86.w,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 86.w,
                  height: 86.w,
                  color: Colors.grey[300],
                  child: const Icon(Icons.image_not_supported),
                );
              },
            ),
          ),
          SizedBox(width: 12.w),
          // 店铺信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.shopData.name ?? '',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Clipboard.setData(
                          ClipboardData(text: widget.shopData.name ?? ''),
                        );
                        SmartDialog.showToast('复制成功');
                      },
                      child: CachedNetworkImage(
                        imageUrl: 'https://alicdn.msmds.cn/APPSHOW/copy_icon_img.png',
                        width: 14.w,
                        height: 14.w, 
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 10.h),
                Row(
                  children: [
                    Image.network(
                      widget.shopData.type == 1
                          ? 'https://alicdn.msmds.cn/static/personal/bwc/<EMAIL>'
                          : 'https://alicdn.msmds.cn/static/home/<USER>/202401/20240108113502407.png',
                      width: 14.w,
                      height: 14.w,
                      errorBuilder: (context, error, stackTrace) =>
                          const SizedBox.shrink(),
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      widget.shopData.type == 1 ? '美团' : '饿了么',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF999999),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '距离 ${widget.shopData.deliveryDistance ?? ""}',
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: const Color(0xFF999999),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

}
