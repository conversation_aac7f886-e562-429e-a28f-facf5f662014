import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: convert_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/29 11:28
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/29 11:28
/// @UpdateRemark: 更新说明
/// 客服二维码弹窗
class ServiceDialog {
  static void showServiceDialog() {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "service_dialog",
      builder: (context) {
        return SizedBox(
          width: 316.w,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Image.network(
                      serviceDialogBg,
                      width: 316.w,
                      fit: BoxFit.contain,
                    ),
                    Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 27.h, bottom: 26.h),
                          child: Text(
                            "截图二维码，扫一扫添加客服",
                            style: TextStyle(
                              fontSize: 20.sp,
                              color: const Color(0xFFC30000),
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(bottom: 39.h),
                          padding: EdgeInsets.symmetric(
                            horizontal: 9.w,
                            vertical: 9.h,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                            boxShadow: const [
                              BoxShadow(
                                color: Colors.black12,
                                blurRadius: 12,
                              )
                            ],
                          ),
                          child: Image.network(
                            "https://ecocdn.szprize.cn/APP/kefugewei20231222.jpeg",
                            width: 192.w,
                            fit: BoxFit.fitWidth,
                            errorBuilder: (context, o, s) {
                              return Container(
                                width: 192.w,
                                height: 192.w,
                                color: Colors.grey.withAlpha(60),
                              );
                            },
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
              Padding(padding: EdgeInsets.only(bottom: 20.h)),
              InkWell(
                onTap: () {
                  SmartDialog.dismiss(tag: "service_dialog");
                },
                child: Image.asset(
                  close,
                  width: 30.w,
                  height: 30.w,
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
