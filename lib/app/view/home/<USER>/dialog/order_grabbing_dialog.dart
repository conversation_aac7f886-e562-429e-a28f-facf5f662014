import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/utils/toast_util.dart';
import 'package:msmds_platform/widgets/highlight/highlight_text.dart';

/// 霸王餐抢单手机号确认弹窗
class OrderGrabbingDialog extends StatefulWidget {
  final String? userPhone;
  final int platformType; // 1: 美团, 2: 饿了么
  final Function(String phone)? onConfirm;

  const OrderGrabbingDialog({
    super.key,
    this.userPhone,
    required this.platformType,
    this.onConfirm,
  });

  @override
  State<OrderGrabbingDialog> createState() => _OrderGrabbingDialogState();

  /// 显示弹窗
  static Future<String?> show(
    BuildContext context, {
    String? userPhone,
    required int platformType,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => 
       AnimatedPadding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        duration: Duration.zero,
        child: OrderGrabbingDialog(
          userPhone: userPhone,
          platformType: platformType,
        ),
      )
    );
  }
}

class _OrderGrabbingDialogState extends State<OrderGrabbingDialog> {
  late TextEditingController _phoneController;

  @override
  void initState() {
    super.initState();
    _phoneController = TextEditingController(text: widget.userPhone ?? '');
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 提示图片覆盖层
        Container(
          width: double.infinity,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: NetworkImage(
                'https://alicdn.msmds.cn/APPSHOW/qdsm_bj_img.png',
              ),
              fit: BoxFit.fill,
            ),
          ),
          padding: EdgeInsets.fromLTRB(16.w, 55.h, 16.w, 0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Text(
                '请确认手机号，确保输入的手机号与下单手机号一致',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.black,
                ),
              ),
              SizedBox(height: 12.h),
              // 手机号输入框
              Container(
                width: 342.w,
                height: 45.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFF7F7F7),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                padding: EdgeInsets.symmetric(horizontal: 12.w),
                child: TextField(
                  controller: _phoneController,
                  keyboardType: TextInputType.number,
                  maxLength: 11,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.black,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    counterText: '',
                    hintText: '请输入手机号',
                  ),
                ),
              ),
              SizedBox(height: 18.h),
              // 提示文本
        
              HighlightText(
                data: [
                  '1、请确认上方手机号，是${widget.platformType == 1 ? '美团' : '饿了么'}外卖下单账号绑定的手机号。',
                  '如何确认？',
                  '\n2、务必先抢单报名，再去${widget.platformType == 1 ? '美团' : '饿了么'}外卖下单。\n',
                  '3、抢单报名后不可改绑${widget.platformType == 1 ? '美团' : '饿了么'}外卖绑定的手机号',
                ],
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF666666),
                  height: 1.85,
                ),
                keyStyle: TextStyle(
                  color: const Color(0xFFF93324),
                  fontSize: 14.sp,
                  height: 1.85,
                ),
                keys: const [
                  "如何确认？",
                ],
                onTapCallback: (String key) {
                  if (key == "如何确认？") {
                    showHowToConfirmPhoneNumber(context,widget.platformType);
                  }
                },
              ),
              SizedBox(height: 22.h),
              // 底部按钮
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 9.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 取消按钮
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Container(
                        width: 150.w,
                        height: 40.h,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF3F4F6),
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          '取消',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: const Color(0xFF333333),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    // 确定按钮
                    GestureDetector(
                      onTap: _onConfirm,
                      child: Container(
                        width: 150.w,
                        height: 40.h,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF93324),
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          '确定',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20 + MediaQuery.of(context).padding.bottom),
            ],
          ),
        ),
      ],
    );
  }
    /// 显示如何确认手机号弹窗
  void showHowToConfirmPhoneNumber(BuildContext context, int platformType) {
    showDialog(
      context: context,
      builder: (context) => GestureDetector(
        onTap: () {
          Navigator.of(context).pop();
        },
        child: Container(
          color: Colors.black.withOpacity(0.3),
          alignment: Alignment.center,
          child: Image.network(
            platformType == 1
                ? 'https://alicdn.msmds.cn/APPSHOW/mtTips.png'
                : 'https://alicdn.msmds.cn/APPSHOW/elmTips.png',
            width: platformType == 1 ? 375.w : 375.w,
            height: platformType == 1 ? 557.h : 714.h,
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }

  /// 确认按钮点击
  void _onConfirm() {
    final phone = _phoneController.text.trim();
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');

    if (!phoneRegex.hasMatch(phone)) {
      ToastUtil.showToast('请输入正确的手机号');
      return;
    }

    Navigator.of(context).pop(phone);
    widget.onConfirm?.call(phone);
  }
}
