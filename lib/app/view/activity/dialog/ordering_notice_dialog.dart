import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

import '../../../../widgets/button/gradient_button.dart';
import '../../../repository/modals/activity/free_buy_good_item.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: ordering_notice_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/3/29 15:02
/// @UpdateUser: frankylee
/// @UpdateData: 2024/3/29 15:02
/// @UpdateRemark: 更新说明
class OrderingNoticeDialog {
  /// 下单须知提示弹窗
  static void noticeDialog(FreeBuyGoodItem? goodItem, Function onPress) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "ordering_notice_dialog",
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 296.w,
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              margin: EdgeInsets.only(bottom: 36.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 16.h, bottom: 18.h),
                    child: Text(
                      "下单前须知",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      vertical: 8.h,
                      horizontal: 10.w,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF7F7F7),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(4.r),
                          child: Image.network(
                            goodItem?.cover ?? "",
                            width: 75.w,
                            height: 75.w,
                            fit: BoxFit.fitWidth,
                          ),
                        ),
                        Padding(padding: EdgeInsets.only(right: 11.w)),
                        Expanded(
                          child: Container(
                            height: 75.w,
                            padding: EdgeInsets.only(top: 5.h),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "${goodItem?.title}",
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: Colors.black,
                                  ),
                                ),
                                Text.rich(
                                  TextSpan(
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: const Color(0xFF666666),
                                    ),
                                    children: [
                                      const TextSpan(text: "先付"),
                                      TextSpan(
                                        text: "¥${goodItem?.priceAfterCoupon}",
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          fontWeight: FontWeight.w600,
                                          color: const Color(0xFFF93324),
                                        ),
                                      ),
                                      const TextSpan(text: "购买，后返"),
                                      TextSpan(
                                        text: "¥${goodItem?.priceAfterCoupon}",
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          fontWeight: FontWeight.w600,
                                          color: const Color(0xFFF93324),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 6.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "全额返规则：",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.black,
                          ),
                        ),
                        Padding(padding: EdgeInsets.only(bottom: 7.h)),
                        Text.rich(
                          TextSpan(
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: const Color(0xFF666666),
                            ),
                            children: [
                              const TextSpan(text: "1、领取后请及时选择"),
                              TextSpan(
                                text: "券后价¥${goodItem?.priceAfterCoupon}",
                                style: const TextStyle(
                                  color: Color(0xFFF93324),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const TextSpan(text: "的商品规格付款；"),
                            ],
                          ),
                        ),
                        Padding(padding: EdgeInsets.only(bottom: 3.h)),
                        Text.rich(
                          TextSpan(
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: const Color(0xFF666666),
                            ),
                            children: [
                              const TextSpan(text: "2、若实付款"),
                              TextSpan(
                                text: "超出${goodItem?.priceAfterCoupon}元，则无法全额返",
                                style: const TextStyle(
                                  color: Color(0xFFF93324),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const TextSpan(
                                text: "，仅享受部分比例返现；或者返回活动页选择其他商品下单。",
                              ),
                            ],
                          ),
                        ),
                        Padding(padding: EdgeInsets.only(bottom: 3.h)),
                        Text(
                          "3、不可使用淘礼金、集分宝、淘宝币、超级红包；",
                          style: TextStyle(
                            fontSize: 11.sp,
                            color: const Color(0xFF666666),
                          ),
                        ),
                        Padding(padding: EdgeInsets.only(bottom: 3.h)),
                        Text(
                          "4、如遇疫情严重地区商家不发货的情况，可选择其他商品下单、更换其他发货地区。",
                          style: TextStyle(
                            fontSize: 11.sp,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 18.h)),
                  GradientButton(
                    margin: EdgeInsets.symmetric(horizontal: 22.w),
                    padding: EdgeInsets.symmetric(
                      // horizontal: 44.w,
                      vertical: 9.h,
                    ),
                    onPress: () {
                      SmartDialog.dismiss(tag: "ordering_notice_dialog");
                      onPress();
                    },
                    shadow: false,
                    radius: 20,
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xFFFE5640),
                        Color(0xFFFA2E1B),
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    child: Text(
                      "已了解，去下单拿返现",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 46.h)),
                ],
              ),
            ),
            InkWell(
              onTap: () {
                SmartDialog.dismiss(tag: "ordering_notice_dialog");
              },
              child: Image.asset(
                close,
                width: 28.w,
                height: 28.w,
              ),
            ),
          ],
        );
      },
    );
  }
}
