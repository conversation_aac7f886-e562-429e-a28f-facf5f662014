import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: free_buy_rule_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/3/29 15:02
/// @UpdateUser: frankylee
/// @UpdateData: 2024/3/29 15:02
/// @UpdateRemark: 更新说明
class FreeBuyRuleDialog {
  /// 0元购活动规则弹窗
  static void ruleDialog() {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "free_buy_rule_dialog",
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 296.w,
              height: 395.h,
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              margin: EdgeInsets.only(bottom: 36.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 16.h, bottom: 18.h),
                    child: Text(
                      "0元购活动规则",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Text(
                            "活动用户参与0元购免单活动需符合规则要求，包含但不限于如下:",
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: const Color(0xFF666666),
                            ),
                          ),
                          _ruleItem("1、参与次数",
                              "每个用户仅限购买1件商品享受0元购免单。(一个用户不同手机号，不同手机终端，均视为一个用户，不能参与多次)。恶意刷资格者，奖励清空，并加入黑名单。"),
                          _ruleItem("2、0元购免单资格有效期",
                              "0元购免单资格有效期请以页面显示的为准，逾期失效不予补发，请尽快下单使用。"),
                          _ruleItem("3、商品优惠券有效期", "商品优惠分分钟被抢光，领取优惠券后建议尽快去下单。"),
                          _ruleItem("4、商品规格",
                              "一个商品有多个规格，仅限活动页面指定价格的规格可以参与全额返现0元购。若实付款超出页面指定金额(不包含邮费)，则不能全额返。商品详情图片仅供参考，实物请以淘宝实际下单为准。"),
                          _ruleItem("5、划线价格说明",
                              "划线价格:一般情况下，划线的价格可能是商品的销售指导价或该商品的曾经展示过的销售价等，并非官网价，仅供参考。未划线价格:一般情况下，未划线的价格是商品在阿里巴巴中国站上的销售标价，具体的成交价格根据商品参加活动，或因用户使用优惠券等发生变化，最终以订单结算页价格为准。"),
                          _ruleItem("6、下单付款",
                              "下单付款时不能使用淘礼金、集分宝、淘金币以及其他平台的双11红包，否则无返现。"),
                          _ruleItem("7、包邮说明",
                              "免单商品部分偏远地区和疫情严重地区不包邮，如石家庄、北京、新疆、西藏、港澳台、青海、内蒙、甘肃、海南等地区商家不发货，具体以商家规定为准。"),
                          _ruleItem("8、发货说明",
                              "如遇疫情严重地区商家不发货的情况，可选择其他商品下单、更换其它发货地区或回首页点击退积分。"),
                          _ruleItem("9、提现说明",
                              "在淘宝APP完成对应商品的签收后，最快签收后第8天可前往返利快车APP将返现提现到支付宝(1元起提，0手续费)。走其他返利软件返利无返现，注销过返利快车app账号的无法再提现。"),
                          _ruleItem("10、售后说明",
                              "本活动仅为用户提供优惠券及返利优惠，所有商品配送售后均与本活动无关，用户如有需要请联系淘宝商家或淘宝商家，本活动组织方不承担售后相关的任何责任。"),
                          _ruleItem("11、淘宝，支付宝账号说明",
                              "一个淘宝账号只能授权一个返利快车APP账号，不能重复授权，一个支付宝账号只能一个返利快车APP账号进行提现，不能重复绑定提现。"),
                          _ruleItem("12、其他说明",
                              "如有其他疑问，可添加客服微信咨询，服务时间:周一至周五09:30-18:30。"),
                        ],
                      ),
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 26.h)),
                ],
              ),
            ),
            InkWell(
              onTap: () {
                SmartDialog.dismiss(tag: "free_buy_rule_dialog");
              },
              child: Image.asset(
                close,
                width: 28.w,
                height: 28.w,
              ),
            ),
          ],
        );
      },
    );
  }

  static Widget _ruleItem(String title, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(padding: EdgeInsets.only(top: 10.h)),
        Text(
          title,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.black,
          ),
        ),
        Padding(padding: EdgeInsets.only(bottom: 4.h)),
        Text(
          value,
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF666666),
          ),
        ),
      ],
    );
  }
}
