import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/config/constant.dart';

import '../../common/img/icon_addres.dart';
import '../../config/global_config.dart';
import '../navigation/coosea.dart';
import '../navigation/router.dart';
import '../provider/search/platform_provider.dart';
import '../provider/search/search_provider.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: convert_fail_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/29 14:49
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/29 14:49
/// @UpdateRemark: 更新说明
class ConvertFailDialog {
  /// 唤起识别失败弹窗
  static void showConvertFailDialog(
    String? content,
  ) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "convert_dialog",
      animationType: SmartAnimationType.fade,
      builder: (_) {
        return SizedBox(
          width: 316.w,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Stack(
                  children: [
                    Image.network(
                      convertFailBg,
                      width: 316.w,
                      fit: BoxFit.fitWidth,
                    ),
                    Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          child: Text(
                            "搜索以下内容吗",
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF871800),
                            ),
                          ),
                        ),
                        Container(
                          width: 296.w,
                          padding: EdgeInsets.fromLTRB(11.w, 18.h, 11.w, 5.h),
                          margin: EdgeInsets.only(bottom: 26.h),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xAEFFFFFF), Colors.white],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            "$content",
                            textAlign: TextAlign.center,
                            maxLines: 4,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: const Color(0xFF333333),
                            ),
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              width: 39.w,
                              height: 1,
                              color: const Color(0xFFE1E1E1),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 15.w),
                              child: Text(
                                "按平台搜索",
                                style: TextStyle(
                                  fontSize: 15.sp,
                                  color: const Color(0xFF757575),
                                ),
                              ),
                            ),
                            Container(
                              width: 39.w,
                              height: 1,
                              color: const Color(0xFFE1E1E1),
                            ),
                          ],
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 18.h),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              SearchIcon(
                                platformType: 1,
                                name: "淘宝",
                                icon: "${Constant.msmdsAliCdn}/APPSHOW/convert_search_tb.png",
                                content: content,
                              ),
                              SearchIcon(
                                platformType: 2,
                                name: "京东",
                                icon: "${Constant.msmdsAliCdn}/APPSHOW/convert_search_jd.png",
                                content: content,
                              ),
                              SearchIcon(
                                platformType: 3,
                                name: "拼多多",
                                icon: "${Constant.msmdsAliCdn}/APPSHOW/convert_search_pdd.png",
                                content: content,
                              ),
                              SearchIcon(
                                platformType: 7,
                                name: "唯品会",
                                icon: "${Constant.msmdsAliCdn}/APPSHOW/convert_search_wph.png",
                                content: content,
                              ),
                              // SearchIcon(
                              //   platformType: 6,
                              //   name: "抖音",
                              //   icon: "${Constant.msmdsAliCdn}/APPSHOW/convert_search_dy.png",
                              //   content: content,
                              // ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Padding(padding: EdgeInsets.only(bottom: 20.h)),
              InkWell(
                onTap: () {
                  SmartDialog.dismiss(tag: "convert_dialog");
                },
                child: Image.asset(
                  close,
                  width: 30.w,
                  height: 30.w,
                ),
              )
            ],
          ),
        );
      },
    );
  }
}

class SearchIcon extends StatelessWidget {
  const SearchIcon({
    super.key,
    required this.platformType,
    required this.name,
    required this.icon,
    this.content,
  });

  final int platformType;
  final String name;
  final String icon;
  final String? content;

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        return InkWell(
          onTap: () {
            SmartDialog.dismiss(tag: "convert_dialog");
            ref.watch(platformProvider.notifier).setCurrentPlatform(
                  platformType,
                  isSearch: false,
                );
            ref.watch(searchKeywordProvider.notifier).setKeyword(content);
            if (GlobalConfig.currentRouter == CsRouter.search) {
              navigatorKey.currentState?.pushReplacementNamed(CsRouter.search);
            } else if (GlobalConfig.currentRouter == CsRouter.searchPre) {
              navigatorKey.currentState?.pushNamed(CsRouter.search);
            } else {
              ref.watch(searchAutoFocusProvider.notifier).setAutoFocus(false);
              navigatorKey.currentState?.pushNamed(CsRouter.searchPre);
              navigatorKey.currentState?.pushNamed(CsRouter.search);
            }
          },
          child: Column(
            children: [
              Image.network(
                icon,
                width: 38.w,
                height: 38.w,
                fit: BoxFit.contain,
              ),
              Padding(padding: EdgeInsets.only(top: 3.h)),
              Text.rich(
                TextSpan(
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: const Color(0xFF4A4A4A),
                  ),
                  children: [
                    TextSpan(text: name),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 4),
                        child: Image.network(
                          rightArrowImg,
                          width: 5,
                          height: 9,
                          color: const Color(0x80333333),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
