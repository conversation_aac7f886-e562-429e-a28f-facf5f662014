import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../common/img/icon_addres.dart';
import '../provider/config/config_provider.dart';
import '../repository/modals/config/icon_config.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: ordinary_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/5/14 15:42
/// @UpdateUser: frankylee
/// @UpdateData: 2024/5/14 15:42
/// @UpdateRemark: 更新说明
class OrdinaryDialog {
  /// 普通配置弹窗
  static void show(List<IconConfig> iconConfigs) {
    var iconConfig = iconConfigs.first;
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "ordinary_dialog",
      onDismiss: () {
        iconConfigs.removeAt(0);
        if (iconConfigs.isNotEmpty) {
          show(iconConfigs);
        }
      },
      builder: (context) {
        return Consumer(
          builder: (context, ref, child) {
            return SizedBox(
              width: 286.w,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      InkWell(
                        onTap: () {
                          SmartDialog.dismiss(tag: "ordinary_dialog");
                        },
                        child: Image.asset(
                          ordinaryClose,
                          width: 13.w,
                          height: 13.w,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 33.h,
                  ),
                  InkWell(
                    onTap: () {
                      SmartDialog.dismiss(tag: "ordinary_dialog");
                      ref
                          .read(configItemClickProvider.notifier)
                          .configItemClick(iconConfig);
                    },
                    child: Image.network(
                      iconConfig.pictureUrl ?? "",
                      width: 260.w,
                    ),
                  ),
                  SizedBox(
                    height: 46.h,
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
