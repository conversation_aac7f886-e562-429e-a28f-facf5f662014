import 'package:flutter/cupertino.dart';
import 'package:msmds_platform/app/navigation/router_util.dart';
import 'package:msmds_platform/app/view/activity/pdd_activity_page.dart';
import 'package:msmds_platform/app/view/activity/tb_activity_page.dart';
import 'package:msmds_platform/app/view/activity/zero_purchase_activity_page.dart';
import 'package:msmds_platform/app/view/archive/favorites_page.dart';
import 'package:msmds_platform/app/view/archive/history_page.dart';
import 'package:msmds_platform/app/view/bill/bill_page.dart';
import 'package:msmds_platform/app/view/goods_detail/goods_detail_page.dart';
import 'package:msmds_platform/app/view/home/<USER>';
import 'package:msmds_platform/app/view/activity/jd_activity_page.dart';
import 'package:msmds_platform/app/view/inner/invite_details_page.dart';
import 'package:msmds_platform/app/view/inner/red_packets_page.dart';
import 'package:msmds_platform/app/view/inner/suning_shop_page.dart';
import 'package:msmds_platform/app/view/inner/vip_shop_page.dart';
import 'package:msmds_platform/app/view/integral/my_integral_page.dart';
import 'package:msmds_platform/app/view/login/login_page.dart';
import 'package:msmds_platform/app/view/login/verification_page.dart';
import 'package:msmds_platform/app/view/order_detail/order_detail_page.dart';
import 'package:msmds_platform/app/view/proxy/proxy_page.dart';
import 'package:msmds_platform/app/view/proxy/server_page.dart';
import 'package:msmds_platform/app/view/search/search_page.dart';
import 'package:msmds_platform/app/view/search/search_pre_page.dart';
import 'package:msmds_platform/app/view/setting/info_collection_page.dart';
import 'package:msmds_platform/app/view/setting/logoff_page.dart';
import 'package:msmds_platform/app/view/setting/not_found_page.dart';
import 'package:msmds_platform/app/view/setting/setting_page.dart';
import 'package:msmds_platform/app/view/splash/splash_ad_page.dart';
import 'package:msmds_platform/app/view/splash/splash_page.dart';
import 'package:msmds_platform/app/view/web/web_page.dart';
import 'package:msmds_platform/app/view/my_order/my_order_page.dart';
import 'package:msmds_platform/app/view/withdrawal/withdrawal_page.dart';
import 'package:msmds_platform/app/view/withdrawal/withdrawal_record_page.dart';
import 'package:msmds_platform/app/view/withdrawal/withdrawal_result_page.dart';
import 'package:msmds_platform/app/view/withdrawal/withdrawal_rule_page.dart';
import 'package:msmds_platform/app/view/home/<USER>/bawangcan_detail_page.dart';
import 'package:msmds_platform/app/view/home/<USER>/bawangcan_registration_record_page.dart';
import 'package:msmds_platform/app/view/home/<USER>/frequently_questions_page.dart';
import 'package:msmds_platform/app/view/home/<USER>/fun_activity_introduct_page.dart';

import '../view/integral/integral_detail_page.dart';

class CsRouter {
  /// splash
  static const String splash = "SplashPage";

  static const String splashAd = "SplashAdPage";

  /// home
  static const String home = "HomePage";

  /// login
  static const String login = "LoginPage";

  /// verification
  static const String verification = "VerificationPage";

  /// web view
  static const String webPage = "WebPage";

  /// search
  static const String search = "SearchPage";

  /// search pre
  static const String searchPre = "SearchPrePage";

  /// myOrderPage
  static const String myOrderPage = "MyOrderPage";

  /// bill page
  static const String bill = "BillPage";

  /// setting page
  static const String setting = "SettingPage";

  /// order detail
  static const String orderDetail = "OrderDetailPage";

  /// Personal information collection
  static const String infoCollection = "InfoCollectionPage";

  /// logoff page
  static const String logoff = "LogoffPage";

  /// proxy page
  static const String proxy = "ProxyPage";

  /// server page
  static const String server = "ServerPage";

  /// withdrawal page
  static const String withdrawal = "WithdrawalPage";

  /// withdrawal result page
  static const String withdrawalResult = "WithdrawalResultPage";

  /// withdrawal record page
  static const String withdrawalRecord = "WithdrawalRecordPage";

  /// withdrawal rule page
  static const String withdrawalRule = "WithdrawalRulePage";

  /// pdd activity page
  static const String pddActivity = "PddActivityPage";

  /// jd activity page
  static const String jdActivity = "JdActivityPage";

  /// tb activity page
  static const String tbActivity = "TbActivityPage";

  /// zero purchase activity
  static const String zeroPurchaseActivity = "ZeroPurchaseActivityPage";

  // goods detail
  static const String goodsDetailPage = "GoodsDetailPage";

  // my integral
  static const String myIntegralPage = "MyIntegralPage";
  static const String integralDetailPage = "IntegralDetailPage";

  // 内部页面
  static const String vipShopPage = "VipWphPage";
  static const String suNingShopPage = "SuNingPage";
  // 我的红包
  static const String redPacketsPage = "redPacketsPage";
  // 粉丝收益
  static const String inviteDetailsPage = "InviteDetailsPage";

  // 我的收藏
  static const String favoritesPage = "favoritesPage";
  // 浏览足迹
  static const String historyPage = "historyPage";

  // 霸王餐详情页
  static const String baWangCanDetailPage = "BaWangCanDetailPage";
  // 霸王餐报名记录页
  static const String bawangcanRegistrationRecordPage =
      "BawangcanRegistrationRecordPage";
  // 霸王餐常见问题
  static const String frequentlyQuestionsPage = "FrequentlyQuestionsPage";
  // 吃喝玩乐视频教程页
  static const String funActivityIntroductPage = "FunActivityIntroductPage";

  static Route<dynamic> generateRoute(
    RouteSettings settings,
  ) {
    switch (settings.name) {
      case splash:
        return FadeRouter(
          child: const SplashPage(),
          setting: settings,
        );
      case splashAd:
        return FadeRouter(
          child: const SplashAdPage(),
          setting: settings,
        );
      case home:
        return FadeRouter(
          child: HomePage(arguments: settings.arguments),
          setting: settings,
        );
      case login:
        return DragBackRouter(
          builder: (_) => const LoginPage(),
          settings: settings,
        );
      case verification:
        return CupertinoPageRoute(
          builder: (_) => const VerificationPage(),
          settings: settings,
        );
      case webPage:
        return CupertinoPageRoute(
          builder: (_) => WebPage(arguments: settings.arguments),
          settings: settings,
        );
      case search:
        return CupertinoPageRoute(
          builder: (_) => const SearchPage(),
          settings: settings,
        );
      case searchPre:
        return CupertinoPageRoute(
          builder: (_) => const SearchPrePage(),
          settings: settings,
        );
      case myOrderPage:
        return CupertinoPageRoute(
          builder: (_) => const MyOrderPage(),
          settings: settings,
        );
      case bill:
        return CupertinoPageRoute(
          builder: (_) => const BillPage(),
          settings: settings,
        );
      case setting:
        return CupertinoPageRoute(
          builder: (_) => const SettingPage(),
          settings: settings,
        );
      case orderDetail:
        return CupertinoPageRoute(
          builder: (_) => const OrderDetailPage(),
          settings: settings,
        );
      case infoCollection:
        return CupertinoPageRoute(
          builder: (_) => const InfoCollectionPage(),
          settings: settings,
        );
      case logoff:
        return CupertinoPageRoute(
          builder: (_) => const LogoffPage(),
          settings: settings,
        );
      case proxy:
        return CupertinoPageRoute(
          builder: (_) => const ProxyPage(),
          settings: settings,
        );
      case server:
        return CupertinoPageRoute(
          builder: (_) => const ServerPage(),
          settings: settings,
        );
      case withdrawal:
        return CupertinoPageRoute(
          builder: (_) => const WithdrawalPage(),
          settings: settings,
        );
      case withdrawalResult:
        return CupertinoPageRoute(
          builder: (_) => const WithdrawalResultPage(),
          settings: settings,
        );
      case withdrawalRecord:
        return CupertinoPageRoute(
          builder: (_) => const WithdrawalRecordPage(),
          settings: settings,
        );
      case withdrawalRule:
        return CupertinoPageRoute(
          builder: (_) => const WithdrawalRulePage(),
          settings: settings,
        );
      case pddActivity:
        return CupertinoPageRoute(
          builder: (_) => const PddActivityPage(),
          settings: settings,
        );
      case jdActivity:
        return CupertinoPageRoute(
          builder: (_) => const JdActivityPage(),
          settings: settings,
        );
      case tbActivity:
        return CupertinoPageRoute(
          builder: (_) => const TbActivityPage(),
          settings: settings,
        );
      case zeroPurchaseActivity:
        return CupertinoPageRoute(
          builder: (_) => const ZeroPurchaseActivityPage(),
          settings: settings,
        );
      case goodsDetailPage:
        return CupertinoPageRoute(
          builder: (_) => GoodsDetailPage(arguments: settings.arguments),
          settings: settings,
        );
      case myIntegralPage:
        return CupertinoPageRoute(
          builder: (_) => const MyIntegralPage(),
          settings: settings,
        );
      case integralDetailPage:
        return CupertinoPageRoute(
          builder: (_) => const IntegralDetailPage(),
          settings: settings,
        );
      case vipShopPage:
        return CupertinoPageRoute(
          builder: (_) => const VipShopPage(),
          settings: settings,
        );
      case suNingShopPage:
        return CupertinoPageRoute(
          builder: (_) => const SuNingShopPage(),
          settings: settings,
        );
      case redPacketsPage:
        return CupertinoPageRoute(
          builder: (_) => const RedPacketsPage(),
          settings: settings,
        );
      case inviteDetailsPage:
        return CupertinoPageRoute(
          builder: (_) => const InviteDetailsPage(),
          settings: settings,
        );
      case favoritesPage:
        return CupertinoPageRoute(
          builder: (_) => const FavoritesPage(),
          settings: settings,
        );
      case historyPage:
        return CupertinoPageRoute(
          builder: (_) => const HistoryPage(),
          settings: settings,
        );
      case baWangCanDetailPage:
        return CupertinoPageRoute(
          builder: (_) => BaWangCanDetailPage(
            arguments: settings.arguments as BaWangCanDetailArguments,
          ),
          settings: settings,
        );
      case bawangcanRegistrationRecordPage:
        return CupertinoPageRoute(
          builder: (_) => const BawangcanRegistrationRecordPage(),
          settings: settings,
        );
      case frequentlyQuestionsPage:
        return CupertinoPageRoute(
          builder: (_) => const FrequentlyQuestionsPage(),
          settings: settings,
        );
      case funActivityIntroductPage:
        return CupertinoPageRoute(
          builder: (_) => const FunActivityIntroductPage(),
          settings: settings,
        );
      default:
        return CupertinoPageRoute(
          builder: (_) => const NotFountPage(),
          settings: settings,
        );
    }
  }
}
