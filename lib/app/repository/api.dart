import 'package:msmds_platform/config/global_config.dart';

class Api {
  /// ========================账户==================///

  // 获取服务器时间戳
  static const String serviceTimestamp = "api/common/timestamp";

  // 获取手机验证码
  // phone: 手机号
  static const String getPhoneVerifyCode = "api/user/sendLoginVerifyCode";

  // 登录
  // code: 验证码
  // phone: 手机号
  static const String loginByPhone = "NTI/user/loginByPhoneNew";

  // 获取用户钱包收益金额
  static const String getUserWallet = "api/order/userBalance/getAccountInfo";

  // 获取用户最新订单
  static const String newestOrder = "api/order/findNewestOrderInfo";

  // 用户信息
  static const String accountInfo = "api/user/findById";

  /// 账户注销
  static const String cancelAccount = "app/appUser/cancelAccount";

  /// 发送绑定手机号验证码
  /// phone	手机号
  static const String sendBindPhoneCode = "app/appUser/sendBindPhoneCode";

  /// 绑定手机号
  /// code	验证码
  /// phone	手机号
  static const String bindPhone = "app/appUser/bindPhone";

  // 绑定支付宝提现账户
  // accountName	账户姓名
  // accountNo	账户号码
  static const String bindWithdrawalAccount =
      "api/userAliPayInfo/updateAliPayInfo";

  /// 是否有授权拼多多
  static const String isAuthPdd = "api/user/appUserAuthorizePdd/isAuthPdd";

  /// 获取拼多多授权链接
  static const String getAuthUrl = "api/user/appUserAuthorizePdd/getAuthUrl";

  // topNative授权
  // accessToken
  // userId
  // userNick
  static const String tbTopNativeAuth = "api/tbUser/appAuthorization";

  /// ========================账户==================///

  /// ========================转链==================///

  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%8B%BC%E5%A4%9A%E5%A4%9A%E8%BD%AC%E9%93%BE/getDdkResourceUrlUsingPOST
  /// 生成多多进宝频道推广
  ///{
  ///   "generateSchemaUrl": true,
  ///   "generateWeApp": true,
  ///   "pid": "",
  ///   "resourceType": 0,
  ///   "url": ""
  /// }
  static const String getDdkResourceUrl =
      "api/pdd/link/generate/getDdkResourceUrl";

  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%8B%BC%E5%A4%9A%E5%A4%9A%E8%BD%AC%E9%93%BE/getDdkCmsPromUrlGenerateUsingPOST
  /// 获取商城-频道推广链接
  ///{
  ///   "channelType": 0,
  ///   "generateMobile": true,
  ///   "generateSchemaUrl": true,
  ///   "generateShortUrl": true,
  ///   "generateWeApp": true,
  ///   "keyword": "",
  ///   "multiGroup": true,
  ///   "pidList": []
  /// }
  static const String getDdkCmsPromUrlGenerate =
      "api/pdd/link/generate/getDdkCmsPromUrlGenerate";

  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%8B%BC%E5%A4%9A%E5%A4%9A%E8%BD%AC%E9%93%BE/getDdkRpPromUrlGenerateUsingPOST
  /// 生成营销工具推广链接
  /// {
  ///   "channelType": 0,
  ///   "generateQqApp": true,
  ///   "generateSchemaUrl": true,
  ///   "generateShortUrl": true,
  ///   "generateWeApp": true,
  ///   "pidList": []
  /// }
  static const String getDdkRpPromUrlGenerate =
      "api/pdd/link/generate/getDdkRpPromUrlGenerate";

  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%B7%98%E5%AE%9D%E8%BD%AC%E9%93%BE/getPurchaseLinkUsingPOST
  /// code	商户pid配置code
  /// goodsId	商品id（淘宝需整段）
  static const String getPurchaseLink = "api/tb/purchaseLink/getPurchaseLink";

  /// 京东活动转链
  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%B4%BB%E5%8A%A8%E8%BD%AC%E9%93%BE/jdChangeLinkUsingGET
  /// activityUrl	活动链接
  static const String jdActivityChangeUrl = "activityChangeLink/jdChangeLink";

  /// 淘宝活动转链
  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E6%B4%BB%E5%8A%A8%E8%BD%AC%E9%93%BE/tbChangeLinkUsingGET
  /// activityId	活动ID
  static const String tbActivityChangeUrl = "activityChangeLink/tbChangeLink";

  /// ========================转链==================///

  /// ========================商品包==================///
  /// 获取商品包配置信息列表
  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%95%86%E5%93%81%E5%8C%85%E9%85%8D%E7%BD%AE%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listGoodsPkgUsingGET
  /// displayArea	显示区域
  /// goodsType	商品类型
  static const String listGoodsPkg = "api/goodsPkgConfig/listGoodsPkg";

  /// 获取商品包商品信息列表
  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%95%86%E5%93%81%E4%BF%A1%E6%81%AF%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listByPkgIdUsingGET
  /// goodsType	商品类型
  /// pageNo	页码数
  /// pageSize	页码大小
  /// pkgId	商品包id
  static const String listGoodsByPkgId = "api/goodsInfo/listByPkgId";

  /// ========================商品包==================///

  /// ========================用户订单==================///
  // 订单列表
  static const String getUserOrderList = "api/order/findOrder";

  // 订单详情
  // orderNo	订单号
  // orderId 订单id
  // 以上参数看情况先判断orderNo，没有则传orderId
  static const String getUserOrderDetail = "api/order/findOrderDetail";

  // 订单tab列表，所有支持的订单类型
  static const String getAllOrderTab = "api/order/orderListTabConfig/getAll";

  /// ========================用户订单==================///

  /// ========================活动转链（美团、饿了么等）==================///
  /// 饿了么推广官方活动查询
  /// activityId	活动ID
  /// includeQrCode	是否包含二维码，如果为false，不返回二维码和图片，只有链接
  /// includeWxImg	是否返回微信推广图片
  /// pid	渠道PID
  static const String getPromotionOfficialActivity =
      "api/elm/link/generate/getPromotionOfficialActivity";

  /// 获取美团推广链接
  /// actId	活动物料ID，我要推广-活动推广中第一列的id信息（和商品券展示id、活动链接三选一填写，不能全填）
  /// linkType	链接类型，枚举值：1 H5长链接；2 H5短链接；3 deeplink(唤起)链接；4 微信小程序唤起路径径
  /// skuViewId	商品券展示id，对商品券查询接口返回的skuViewid（和活动物料ID、活动链接三选一，不能全填）
  /// text	活动链接，即想要推广的目标链接，出参会返回成自己可推的链接，限定为当前可推广的活动链接或者商品券链接，请求内容尽量保持在200字以内，文本中仅存在一个http协议头的链接
  static const String getReferralLink =
      "api/mt/mtz/link/generate/getReferralLink";

  /// ========================活动转链（美团、饿了么等）==================///

  /// 获取用户0元购资格
  static const String userValidFreeBuy =
      "activity/generalFreeBuy/getUserValidFreeBuy";

  /// 获取icon配置
  /// iconType	icon类型
  /// version	版本号
  static const String iconConfig = "config/iconConfig/listByPkgId";

  /// 分页查询商品下单提醒
  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%95%86%E5%93%81%E4%B8%8B%E5%8D%95%E6%8F%90%E9%86%92%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/listPageUsingGET
  /// pageNo	页码数
  /// pageSize	页码大小
  static const String goodsReminderList = "goods/goodsOrderRemind/listPage";

  /// 保存商品下单提醒
  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%95%86%E5%93%81%E4%B8%8B%E5%8D%95%E6%8F%90%E9%86%92%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/saveGoodsOrderRemindUsingPOST
  ///{
  ///   "completeGoodsId": "",
  ///   "couponInfo": "",
  ///   "couponPrice": 0,
  ///   "cover": "",
  ///   "goodsId": "",
  ///   "goodsName": "",
  ///   "goodsType": 0,
  ///   "originalPrice": 0,
  ///   "receivedPrice": 0,
  ///   "userCommission": 0
  /// }
  static const String goodsReminderSave =
      "goods/goodsOrderRemind/saveGoodsOrderRemind";

  /// 删除单个提醒
  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%95%86%E5%93%81%E4%B8%8B%E5%8D%95%E6%8F%90%E9%86%92%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/deleteOneUsingGET
  /// id	ID
  static const String deleteReminder = "goods/goodsOrderRemind/deleteOne";

  /// 删除全部提醒
  /// https://testecoapi.szprize.cn/doc.html#/App%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/%E5%95%86%E5%93%81%E4%B8%8B%E5%8D%95%E6%8F%90%E9%86%92%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/deleteAllUsingGET
  static const String deleteReminderAll = "goods/goodsOrderRemind/deleteAll";

  /// 打开-关闭商品订单通知
  /// goodsOrderRemind	商品下单提醒：0、关闭 1、打开
  static const String toggleReminder = "app/appUser/switchGoodsOrderRemind";

  // 0元购商品
  static const String giftGoodsList = "api/tbDtk/findGoodsPkgOrInfo";

  // 收藏 足迹 红包 新手视频总数
  static const String myNumber = "api/user/findMyNumber";

  // 收藏数显示
  static const String collectCount =
      "api/userAppGoodsCollection/findUserCollectTotalCount";

  // 浏览足迹数显示
  static const String browsingCount =
      "api/userAppGoodsBrowsingRecord/findTotalCount";

  // 获取用户红包列表
  // status
  // pageNum
  // pageSize
  static const String getMyGiftList = "api/user/gift/findMyGift";

  // 邀请粉丝收益
  // pageNum
  // pageSize
  static const String getInvitationInfo = "api/user/getInvitationInfo";

  // 收藏商品列表
  // goodsType
  // pageNo
  // pageSize
  static const String findUserGoodsCollect =
      "api/userAppGoodsCollection/listUserCollect";

  // 删除收藏列表
  static const String removeGoodsCollect =
      "api/userAppGoodsCollection/removeByIds";

  // 检查商品是否收藏
  static const String checkGoodsCollect =
      "api/userAppGoodsCollection/checkCollect";

  // 删除商品收藏
  // deleteGoodsIds
  // goodsType
  static const String removeGoodsCollectGoods =
      "api/userAppGoodsCollection/removeCollect";

  // 添加商品收藏
  // goodsId
  // goodsType
  static const String saveGoodsCollect =
      "api/userAppGoodsCollection/saveCollect";

  // 用户浏览商品列表
  // goodsType
  // pageNo
  // pageSize
  static const String findGoodsBrowsingRecord =
      "api/userAppGoodsBrowsingRecord/listUserBrowsingRecord";

  // 商品删除用户浏览记录
  static const String deleteGoodsBrowsingRecord =
      "api/userAppGoodsBrowsingRecord/removeByIds";

  // 商品详情保存用户浏览记录
  static const String saveGoodsBrowsingRecord =
      "api/userAppGoodsBrowsingRecord/saveBrowsingRecord";

  // 获取App推广栏目列表
  static const String listPromotionColumn =
      "/api/promotionColumn/listPromotionColumn";

  static const String getByCode = "api/parameter/getByCode";

  /// 吃喝玩乐，美团团购商品列表
  static const String mtlmQueryCoupon = "api/mtlm/mtlmQueryCoupon";

  /// 吃喝玩乐，抖音团购列表和搜索
  static const String funLifeSearch = "api/dy/goods/lifeSearch";

  /// 获取美团联盟分享链接
  static const String mtlmShareLink = "/api/mtlm/shareLinkByCouponDetail";
}

/// 获取活动链接API
class ActivityApi {
  // 美团活动
  // {
  //   activity: item?.url,
  //   pageLevel: 2,
  //   sourceCode: 'mtmain',
  // }
  static const String getMtActivityInfo = "api/mt/getActivityInfo";
  // 获取美团联盟推广链接
  static const String getMtReferralLink = "api/mtlm/getReferralLink";

  // 获取淘宝活动链接
  // activityId
  // isShortUrl
  static const String getTbActivityLink = "api/tbk/getActivityLink";

  // 获取多麦活动链接
  // adsId
  // siteId
  static const String duoMaiCpsLink = "api/duomaiCps/cpsLinkGet";

  // 获取聚推客活动链接
  // actId
  static const String juTuiKeCpsLink = "api/jtk/unionAct";

  // 获取京东活动链接
  // url
  static const String getJDActivityUrl = "api/jdjos/getActivityUrl";

  // 获取饿了么新零售推广链接接口
  // type
  static const String getNewRetailUrl = "api/elm/getNewRetailUrl";

  // 获取大众点评和美团生活券链接
  // type
  static const String getLifeCouponUrl = "api/byn/getLifeCouponUrl";

  // 优惠雷达
  static const String getDiscountRadarUrl = "api/tbk/getDiscountRadarUrl";

  // 唯品会根据url获取推广链接
  static const String wphGetUrlByUrl = "api/wph/goods/genByUrl";

  // 唯品会根据商品id获取推广链接
  static const String wphGetUrlById = "api/wph/goods/genByGoodsId";

  // 获取苏宁商城转链链接
  static const String getSuningShop = "api/sn/getSnshopGoodsUrl";

  // 滴滴转链
  // activityId: item?.url || item?.code,
  // isMiniApp: isMiniApp,
  // promotionId: promotionId || item?.promotionId,
  static const String diGenerateLink = "api/diUnion/generateLink";

  // 获取饿了么小程序和活动链接
  // activityId
  static const String elePromotionLink = "api/elm/activityPromotionLink";

  // 美团活动转链
  static const String lmGenerateLink = "api/mt/generateBuyUrl/lmGenerateLink";
}

/// 云配API
class ConfigApi {
  // 首页tab bar配置
  static const String homePagePkgConfig = "api/homePageGoodsPkg/findPkgConfig";

  // 云配icon，banner等等
  // type (场景type参数) type=4是首页icon,type=15首页轮播
  static const String findAllOfEffective =
      "api/shop/advertising/findAllOfEffective";

  // tab列表
  // 首页临时使用这个商品包获取商品
  static const String secondTabList = "api/shop/category/second/icon/list";

  // 活动配置
  // 配置通知消息等内容（type=4）
  static const String popularizeList = "api/activityPopularize/findAll";

  // 签到背景图片
  static const String signBackImg = "api/layout/getBackGroundPic";

  // 签到记录获取
  // param： version: 'v4'
  // response： signModel签到模块，查询是否签到
  static const String signRecord = "api/user/sign/findByUserNew";

  // 签到
  static const String signAdd = "api/user/sign/add";

  // 获取签到页配置数据（商品，兑换红包等数据）
  // param：version
  // param：platformType
  // param：layoutType
  static const String getSignLayout = "api/layout/getLayout";

  // 新签到——获取所有气泡
  static const String getAllIntegralInfo =
      "api/user/integral/collect/getAllIntegralInfo";

  // 获取指定积分
  static const String getIntegral = "api/user/integral/collect/getIntegral";

  // 获取用户当前积分
  static const String getUserIntegral = "api/user/integral/findByUser";

  // 获取会员兑换商品列表
  static const String findVipGoodsList =
      "api/user/integral/goods/findVipGoodsList";

  // 获取积分兑换列表
  static const String getIntegralExchangeList =
      "api/user/integral/goods/getIntegralExchangeList";

  // 积分兑换指定商品
  static const String getGoodsV4 = "api/user/integral/goods/getGoodsV4";

  // 获取用户积分明细
  // pageNo: page,
  // pageSize: PAGE_SIZE,
  // addOrSubtract: integralType ? '增加' : '减少',
  static const String getIntegralDetail =
      "api/user/integralDetailed/findByUser";

  // 获取锦鲤红包具体信息
  static const String luckyGiftInfo = "api/luckyGift/info";

  // 领取指定锦鲤红包
  static const String receiveLuckyGift = "api/luckyGift/add";

  // 开屏广告
  static const String splashConfigAd = "api/user/homeAd/getAd";

  /// 配置智能弹窗
  /// popUpScene
  /// popUpType
  static const String getIntelligentPopup = "api/intelligentPopup/findPopUp";

  /// 记录弹窗关闭点击量
  /// baseId	弹窗id
  static const String recordCloseHits = "api/intelligentPopup/recordCloseHits";

  /// 记录弹窗曝光量
  /// baseId	弹窗id
  static const String recordExposure = "api/intelligentPopup/recordExposure";

  /// 记录弹窗点击量
  /// baseId	弹窗id
  static const String recordHits = "api/intelligentPopup/recordHits";

  /// 记录弹窗次数
  /// channelId	渠道id : 1	query	false integer(int64)
  /// os	操作系统 0：全平台（不包含鸿蒙），1：ios，2：安卓，3：鸿蒙，10：全平台（包含鸿蒙）	query	false integer(int32)
  /// popUpScene	弹窗场景	query	false integer(int32)
  /// popUpType	弹窗类型1弹窗，2飘窗	query	false integer(int32)
  /// subChannelId	子渠道id: 1	query	false integer(int64)
  /// version	版本	query	false string
  static const String recordPopupNum = "api/intelligentPopup/recordPopupNum";

  /// 记录用户主动关闭的弹窗
  /// baseId	弹窗id	query	true	integer(int64)
  /// channelId	渠道id : 1	query	false integer(int64)
  /// os	操作系统 0：全平台（不包含鸿蒙），1：ios，2：安卓，3：鸿蒙，10：全平台（包含鸿蒙）	query	false integer(int32)
  /// popUpScene	弹窗场景	query	false	integer(int32)
  /// popUpType	弹窗类型1弹窗，2飘窗	query	false integer(int32)
  /// subChannelId	子渠道id: 1	query	false	integer(int64)
  /// version	版本	query	false	string
  static const String recordUserClosePopUpIds =
      "api/intelligentPopup/recordUserClosePopUpIds";
}

/// 商品API
class GoodsApi {
  // 商品包加载淘宝精选商品
  // code: 'appMsmdsTb'
  // pkgId: pkgId,
  // pageNo: page,
  // pageSize: PAGE_SIZE,
  static const String findTBPkgSku = "api/shop/findTBPkgSku";

  // 文本识别
  // version: 版本号
  // content：需要识别的内容
  static const String appAnalysis = "api/common/analysis/appAnalysis";

  // 京东商品/链接/优惠券转链
  // skuId
  // couponLink
  // webId： 1
  // code
  static const String jdChangeUrl = "api/goods/changeCouponUrl";

  // 京东精选
  // 根据商品包获取京东商品列表
  static const String findJDFeatured = "api/shop/findSku/jdjx";

  // 拼多多商品转链
  // {
  //   "skuId": skuId,
  //   "showUrl": true,
  // }
  static const String pddChangeUrl = "api/pdd/goods/getCouponInfo";

  // 唯品会商品转链
  // skuId
  // val
  // adCode（可选）
  // rid（可选）
  static const String wphChangeUrl = "api/wph/goods/genByGoodsId";

  // 抖音商品转链
  // itemInfo goodsUrl || goodsId
  static const String dyChangeUrl = "api/goodsBuy/dyUrl";

  // 获取商品详情
  // version
  // goodsId
  // platformType
  // bizSceneId 淘宝需要的参数（可选）
  static const String goodsDetail = "api/goods/detail";

  // 淘宝商品转链
  // code(可选)
  // url
  static const String getTBGoodUrl = "api/tbDtk/changeUrl";

  // 转链分享
  // goodsId
  // platformType
  // purchaseDescription
  // bizSceneId
  // version
  static const String shareContent = "api/goods/shareContent";
}

// 搜索API
class SearchApi {
  // 查询用户搜索记录
  static const String searchHistoryList =
      "api/userSearchHistory/findUserAllHistory";

  // 搜索-保存搜索记录
  // searchTerm 搜索词
  static const String saveSearchHistory =
      "api/userSearchHistory/saveUserSearchHistory";

  // 搜索-删除搜索记录
  // all true删除所有记录
  static const String deleteSearchHistory =
      "api/userSearchHistory/deleteSearchHistory";

  // hasCoupon	是否有券：1、有优惠券                    integer(int32)
  // keyword	关键词                                   string
  // maxPrice	最高价                                 number
  // minPrice	最低价                                 number
  // pageNo	页码                                    integer(int32)
  // sort	排序：1、升序 2、降序                       integer(int32)
  // sortType	排序类型：1、价格 2、销量                integer(int32)
  static const String search = "api/goods/aggregateSearch/search";
}

// 提现与账单
class BillApi {
  // 获取已提现列表
  // pageNo: page,
  // pageSize: PAGE_SIZE,
  static const String listWithdrawalRecord =
      "api/order/withdraw/listPageWithdrawalRecord";

  // 获取用户账单列表
  // status：0-全部，1-即将入账，2-已入账
  // pageNo: page,
  // pageSize: PAGE_SIZE,
  static const String listPageBill = "api/order/userBalance/listPageBill";

  // 获取提现账户
  static const String getWithdrawalAccount =
      "api/userAliPayInfo/getUserAliPayInfo";

  // 获取提现金额列表，快速选择金额提现
  static const String getWithdrawAmountList =
      "api/order/withdraw/getWithdrawTypeJsonByUser";

  // 提现到支付宝
  // version：app版本
  // withdrawalType：withdrawalType
  // amount：amount
  static const String withdrawByAliPayNew =
      "api/order/withdraw/withdrawByAliPayNew";

  // 订单可以使用红包列表
  static const String getOrderCanUseRedPacket =
      "api/order/getOrderCanUseRedPacket";

  // 手动激活红包
  static const String activationGift = "api/user/gift/activationGift";
}

/// 霸王餐
class FreeLunch {
  /// 霸王餐美团活动列表
  static String freeLunchList =
      "${GlobalConfig.freeLunchBaseUrl}app-api/freeLunch/activity/list";

  /// 霸王餐美团活动列表
  static String mtActivityList =
      "${GlobalConfig.freeLunchBaseUrl}app-api/api/mt/activity/list";

  /// 霸王餐饿了么活动列表
  static String elmActivityList =
      "${GlobalConfig.freeLunchBaseUrl}app-api/api/elm/activity/list";

  /// 霸王餐免登获取token
  static String phoneLoginByNoVerify =
      "${GlobalConfig.freeLunchBaseUrl}app-api/app/appUser/phoneLoginByNoVerify";

  /// 霸王餐查询是否有报名
  static String isApply =
      "${GlobalConfig.freeLunchBaseUrl}app-api/order/applyOrder/isApply";

  /// 霸王餐美团活动报名
  static String mtApply =
      "${GlobalConfig.freeLunchBaseUrl}app-api/api/mt/activity/apply";

  /// 霸王餐饿了么活动转链
  static String getActionUrl =
      "${GlobalConfig.freeLunchBaseUrl}app-api/api/elm/activity/getActionUrl";

  /// 霸王餐饿了么活动报名
  static String elmActivityApply =
      "${GlobalConfig.freeLunchBaseUrl}app-api/api/elm/activity/apply";

  /// 霸王餐报名记录列表
  static String applyOrderList =
      "${GlobalConfig.freeLunchBaseUrl}app-api/order/applyOrder/list";

  /// 霸王餐美团取消报名
  static String activityCancel =
      "${GlobalConfig.freeLunchBaseUrl}app-api/api/mt/activity/cancel";

  /// 霸王餐饿了么取消报名
  static String elmActivityCancel =
      "${GlobalConfig.freeLunchBaseUrl}app-api/api/elm/activity/cancel";

  /// 美团领红包转链
  static const String mtGenerateLink = "app-api/mtlm/promotion/generateLink";

  /// 饿了么领红包转链
  static String elmActivityTransform =
      "${GlobalConfig.freeLunchBaseUrl}app-api/elm/promotion/wkyActivityTransform";
}
