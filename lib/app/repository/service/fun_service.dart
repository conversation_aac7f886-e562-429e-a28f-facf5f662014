import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:msmds_platform/app/repository/modals/fun/bawangcan_registration_record.dart';
import 'package:msmds_platform/app/repository/modals/fun/bwc_apply_status.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_free_lunch_response.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_meituan_response.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_promotion.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_tab_config.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/common/http/app_exceptions.dart';
import 'package:msmds_platform/common/http/base/base_response.dart';
import 'package:msmds_platform/common/http/http_utils.dart';

import '../api.dart';

class FunService {
  static Future<ApiResponse<List<FunPromotionCollection>>>
      fetchListPromotionColumn() async {
    try {
      final response = await HttpUtils.get(Api.listPromotionColumn);

      BaseResponse<List<FunPromotionCollection>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => FunPromotionCollection.fromJson(list[index]),
          ).toList();
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<FunTabResult>> fetchTabConfig(String code) async {
    try {
      final response = await HttpUtils.get(
        Api.getByCode,
        params: {'code': code},
      );

      final BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json as String,
      );

      final data = result.data;
      if (data == null || data.isEmpty) {
        return ApiResponse.completed(const FunTabResult());
      }

      final dynamic decoded = jsonDecode(data);
      if (decoded is List) {
        final tabs = decoded
            .map((e) =>
                FunTabConfig.fromJson(Map<String, dynamic>.from(e as Map)))
            .toList();
        return ApiResponse.completed(FunTabResult(tabs: tabs));
      }

      return ApiResponse.completed(const FunTabResult());
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<MeituanCouponResponse>> fetchMeituanCoupons(
    MeituanCouponParams params,
  ) async {
    try {
      final response = await HttpUtils.get(
        Api.mtlmQueryCoupon,
        params: params.toQueryParameters(),
      );

      final BaseResponse<MeituanCouponResponse> result = BaseResponse.fromJson(
        response,
        (json) => MeituanCouponResponse.fromJson(
          Map<String, dynamic>.from(json as Map),
        ),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<FreeLunchResponse>> fetchFreeLunchList(
    FreeLunchParams params,
  ) async {
    try {
      final response = await HttpUtils.post(
        FreeLunch.freeLunchList,
        data: params.toJson(),
        // options: Options(baseUrl: _resolveFreeLunchBaseUrl()),
      );

      final BaseResponse<FreeLunchResponse> result = BaseResponse.fromJson(
        response,
        (json) => FreeLunchResponse.fromJson(
          Map<String, dynamic>.from(json as Map),
        ),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<FreeLunchResponse>> fetchMtActivityList(
    FreeLunchParams params,
  ) async {
    try {
      final response = await HttpUtils.post(
        FreeLunch.mtActivityList,
        data: params.toJson(),
        // options: Options(baseUrl: _resolveFreeLunchBaseUrl()),
      );

      final BaseResponse<FreeLunchResponse> result = BaseResponse.fromJson(
        response,
        (json) => FreeLunchResponse.fromJson(
          Map<String, dynamic>.from(json as Map),
        ),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<FreeLunchResponse>> fetchElmActivityList(
    FreeLunchParams params,
  ) async {
    try {
      final response = await HttpUtils.post(
        FreeLunch.elmActivityList,
        data: params.toJson(),
        // options: Options(baseUrl: _resolveFreeLunchBaseUrl()),
      );

      final BaseResponse<FreeLunchResponse> result = BaseResponse.fromJson(
        response,
        (json) => FreeLunchResponse.fromJson(
          Map<String, dynamic>.from(json as Map),
        ),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse> fetchMeituanShareLink(
      MeituanCouponItem item) async {
    try {
      final params = item.toJson();
      params.removeWhere((key, value) => value == null);
      // params追加字段
      params['pid'] = 'msmds';

      final response = await HttpUtils.get(
        Api.mtlmShareLink,
        params: params,
      );

      final BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json as String,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<String>> fetchMeituanGetCoupon(
    MeituanCouponItem item,
    int linkType,
  ) async {
    try {
      final params = item.toJson();
      params.removeWhere((key, value) => value == null);
      params['pid'] = 'msmds';
      params['linkType'] = linkType;
      params['skuViewId'] = item.skuViewId;
      params['platform'] = item.platform;
      params['bizLine'] = item.bizLine;

      final response = await HttpUtils.get(
        ActivityApi.getMtReferralLink,
        params: params,
      );

      final BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json as String,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 霸王餐查询是否有报名
  ///
  /// 参数:
  /// - shopId: 商铺ID
  /// - activityId: 活动ID
  ///
  /// 返回: 如果已报名返回报名信息，否则返回 null
  static Future<ApiResponse<BwcApplyStatus?>> checkApplyStatus({
    required String shopId,
    required String activityId,
  }) async {
    try {
      final response = await HttpUtils.get(
        FreeLunch.isApply,
        params: {
          'shopId': shopId,
          'activityId': activityId,
        },
      );

      final BaseResponse<BwcApplyStatus?> result = BaseResponse.fromJson(
        response,
        (json) {
          if (json == null) {
            return null;
          }
          return BwcApplyStatus.fromJson(
            Map<String, dynamic>.from(json as Map),
          );
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 霸王餐美团活动报名
  ///
  /// 参数:
  /// - activityId: 活动ID
  /// - channelCode: 渠道码（默认 'msmds'）
  /// - lat: 纬度
  /// - lng: 经度
  /// - mobile: 报名手机号
  /// - pid: PID（默认 'wky_8792_665_654'）
  /// - detail: 活动详细信息
  /// - meituanPvId: 美团 PV ID（可选）
  static Future<ApiResponse<bool>> mtApply({
    required String activityId,
    required double lat,
    required double lng,
    required String mobile,
    required Map<String, dynamic> detail,
    String channelCode = 'msmds',
    String pid = 'wky_8792_665_654',
    String? meituanPvId,
  }) async {
    try {
      final data = {
        'activityId': activityId,
        'channelCode': channelCode,
        'lat': lat,
        'lng': lng,
        'mobile': mobile,
        'pid': pid,
        'detail': detail,
      };

      if (meituanPvId != null && meituanPvId.isNotEmpty) {
        data['meituanPvId'] = meituanPvId;
      }

      debugPrint('mtApply request: $data');

      final response = await HttpUtils.post(
        FreeLunch.mtApply,
        data: data,
      );

      final BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => true,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint('mtApply error: $e');
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 霸王餐饿了么获取活动转链
  ///
  /// 参数:
  /// - activityId: 活动ID
  /// - shopId: 商铺ID
  /// - channelCode: 渠道码（默认 'msmds'）
  static Future<ApiResponse<Map<String, dynamic>?>> getActionUrl({
    required String activityId,
    required String shopId,
    String channelCode = 'msmds',
  }) async {
    try {
      final data = {
        'activityId': activityId,
        'shopId': shopId,
        'channelCode': channelCode,
      };

      final response = await HttpUtils.post(
        FreeLunch.getActionUrl,
        data: data,
      );

      final BaseResponse<Map<String, dynamic>?> result = BaseResponse.fromJson(
        response,
        (json) {
          if (json == null) {
            return null;
          }
          return Map<String, dynamic>.from(json as Map);
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 霸王餐饿了么活动报名
  ///
  /// 参数:
  /// - activityId: 活动ID
  /// - channelCode: 渠道码（默认 'msmds'）
  /// - mobile: 报名手机号
  /// - detail: 活动详细信息
  static Future<ApiResponse<bool>> elmActivityApply({
    required String activityId,
    required String mobile,
    required Map<String, dynamic> detail,
    String channelCode = 'msmds',
  }) async {
    try {
      final data = {
        'activityId': activityId,
        'channelCode': channelCode,
        'mobile': mobile,
        'detail': detail,
      };

      debugPrint('elmActivityApply request: $data');

      final response = await HttpUtils.post(
        FreeLunch.elmActivityApply,
        data: data,
      );

      final BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => true,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint('elmActivityApply error: $e');
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取霸王餐报名记录列表
  ///
  /// 参数:
  /// - params: 查询参数（页码、页大小、状态筛选）
  ///
  /// 返回: 报名记录列表响应数据
  static Future<ApiResponse<BawangcanRegistrationRecordResponse>>
      getApplyOrderList(
    BawangcanRegistrationRecordParams params,
  ) async {
    try {
      final response = await HttpUtils.get(
        FreeLunch.applyOrderList,
        params: params.toJson(),
      );

      final BaseResponse<BawangcanRegistrationRecordResponse> result =
          BaseResponse.fromJson(
        response,
        (json) => BawangcanRegistrationRecordResponse.fromJson(
          Map<String, dynamic>.from(json as Map),
        ),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint('getApplyOrderList error: $e');
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 霸王餐美团取消报名
  ///
  /// 参数:
  /// - applyId: 报名ID
  /// - mobile: 手机号
  /// - channelCode: 渠道码（默认 'msmds'）
  ///
  /// 返回: 取消结果
  static Future<ApiResponse<bool>> cancelMtActivity({
    required String applyId, // 报名ID
    required String mobile, // 手机号
  }) async {
    try {
      final data = {
        'applyId': applyId,
        'channelCode': 'msmds',
        'mobile': mobile,
      };

      final response = await HttpUtils.post(
        FreeLunch.activityCancel,
        data: data,
      );

      final BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => true,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint('cancelMtActivity error: $e');
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 霸王餐饿了么取消报名
  ///
  /// 参数:
  /// - applyId: 报名ID
  /// - mobile: 手机号
  /// - channelCode: 渠道码（默认 'msmds'）
  ///
  /// 返回: 取消结果
  static Future<ApiResponse<bool>> cancelElmActivity({
    required String applyId,
    required String mobile,
  }) async {
    try {
      final data = {
        'applyId': applyId,
        'channelCode': 'msmds',
        'mobile': mobile,
      };

      final response = await HttpUtils.post(
        FreeLunch.elmActivityCancel,
        data: data,
      );

      final BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => true,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint('cancelElmActivity error: $e');
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 美团领红包转链（用于霸王餐返利进度页面）
  ///
  /// 参数:
  /// - actId: 活动ID (固定为7)
  /// - channelCode: 渠道代码 (默认 'msmds')
  /// - linkType: 链接类型 (固定为4，表示微信小程序)
  /// - pid: 渠道PID (默认 'xiaoyuan')
  /// - userId: 用户ID
  ///
  /// 返回: 包含推广链接的字符串
  static Future<ApiResponse<String?>> getMtRedPacketLink({
    required String userId,
  }) async {
    try {
      final data = {
        'actId': 7,
        'channelCode': 'msmds',
        'linkType': 4,
        'pid': 'xiaoyuan',
        'userId': userId,
      };

      final response = await HttpUtils.post(
        FreeLunch.mtGenerateLink,
        data: data,
      );

      final BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json as String,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint('getMtRedPacketLink error: $e');
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 饿了么领红包转链（用于霸王餐返利进度页面）
  ///
  /// 参数:
  /// - activityId: 活动ID (固定为1)
  /// - channelCode: 渠道代码 (默认 'msmds')
  /// - pid: 渠道PID (默认 'wky_8792_664_647')
  /// - storeId: 店铺ID (固定为8792)
  /// - userId: 用户ID
  ///
  /// 返回: 包含推广链接的 Map，成功时返回字段包括 wxPath 和 wxOrgAppId
  static Future<ApiResponse<Map<String, dynamic>?>> getElmRedPacketLink({
    required String userId,
  }) async {
    try {
      final data = {
        'activityId': 1,
        'channelCode': 'msmds',
        'pid': 'wky_8792_664_647',
        'storeId': 8792,
        'userId': userId,
      };

      final response = await HttpUtils.post(
        FreeLunch.elmActivityTransform,
        data: data,
      );

      final BaseResponse<Map<String, dynamic>> result = BaseResponse.fromJson(
        response,
        (json) => json as Map<String, dynamic>,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint('getElmRedPacketLink error: $e');
      return ApiResponse.error(e.error as AppException?);
    }
  }
}

class MeituanCouponParams {
  final double latitude;
  final double longitude;
  final int listTopiId;
  final int platform;
  final int pageNo;
  final int pageSize;
  final int? sortField;
  final int? ascDescOrder;
  final int? bizLine;

  const MeituanCouponParams({
    required this.latitude,
    required this.longitude,
    required this.listTopiId,
    required this.platform,
    required this.pageNo,
    required this.pageSize,
    this.sortField,
    this.ascDescOrder,
    this.bizLine,
  });

  Map<String, dynamic> toQueryParameters() {
    final params = <String, dynamic>{
      'latitude': (latitude * 1000000).floor(),
      'longitude': (longitude * 1000000).floor(),
      'listTopiId': listTopiId,
      'platform': platform,
      'pageNo': pageNo,
      'pageSize': pageSize,
    };
    if (sortField != null) {
      params['sortField'] = sortField;
    }
    if (ascDescOrder != null) {
      params['ascDescOrder'] = ascDescOrder;
    }
    if (bizLine != null) {
      params['bizLine'] = bizLine;
    }
    return params;
  }
}

class DouyinLifeParams {
  final double latitude;
  final double longitude;
  final int pageNum;
  final int pageSize;
  final int sort;
  final int categoryId;

  const DouyinLifeParams({
    required this.latitude,
    required this.longitude,
    required this.pageNum,
    required this.pageSize,
    required this.sort,
    required this.categoryId,
  });

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'pageNum': pageNum,
      'pageSize': pageSize,
      'sort': sort,
      'categoryId': categoryId,
    };
  }
}

class FreeLunchParams {
  final double lat;
  final double lng;
  final int sort;
  final String channelCode;
  final String? mobile;
  final String? mtPageId;
  final String? pageId;

  const FreeLunchParams({
    required this.lat,
    required this.lng,
    required this.sort,
    this.channelCode = 'msmds',
    this.mobile,
    this.mtPageId,
    this.pageId,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'channelCode': channelCode,
      'lat': lat,
      'lng': lng,
      'sort': sort,
    };
    if (mobile != null) {
      data['mobile'] = mobile;
    }
    if (mtPageId != null) {
      data['mtPageId'] = mtPageId;
    }
    if (pageId != null) {
      data['pageId'] = pageId;
    }
    return data;
  }

  @override
  String toString() {
    // TODO: implement toString
    return 'FreeLunchParams{lat: $lat, lng: $lng, sort: $sort, channelCode: $channelCode, mobile: $mobile, mtPageId: $mtPageId, pageId: $pageId}';
  }
}
