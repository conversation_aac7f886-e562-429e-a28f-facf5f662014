import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:msmds_platform/app/repository/api.dart';
import 'package:msmds_platform/app/repository/modals/account/account.dart';
import 'package:msmds_platform/app/repository/modals/account/account_detail.dart';
import 'package:msmds_platform/app/repository/modals/account/bwc_token.dart';
import 'package:msmds_platform/app/repository/modals/account/my_number.dart';
import 'package:msmds_platform/app/repository/modals/account/red_packet_list.dart';
import 'package:msmds_platform/app/repository/modals/account/user_fans_list.dart';
import 'package:msmds_platform/app/repository/modals/account/wallet.dart';
import 'package:msmds_platform/app/repository/modals/account/withdrawal_account.dart';
import 'package:msmds_platform/app/repository/modals/account/withdrawal_message.dart';
import 'package:msmds_platform/app/repository/modals/activity/free_buy_good.dart';
import 'package:msmds_platform/app/repository/modals/activity/valid_free_buy.dart';
import 'package:msmds_platform/app/repository/modals/bill/withdrawal_amount.dart';
import 'package:msmds_platform/app/repository/modals/order/order.dart';
import 'package:msmds_platform/app/repository/modals/transfer/pdd_change_url.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/common/http/app_exceptions.dart';
import 'package:msmds_platform/common/http/base/base_response.dart';
import 'package:msmds_platform/common/http/http_utils.dart';
import 'package:msmds_platform/config/global_config.dart';
import 'package:msmds_platform/utils/prefs_util.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../modals/account/bind_alipay.dart';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: account_service
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/30 17:59
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/30 17:59
/// @UpdateRemark: 更新说明
class AccountService {

  // 获取服务器时间戳
  static Future<ApiResponse<int?>> getServiceTime() async {
    try {
      var response = await HttpUtils.get(Api.serviceTimestamp);

      BaseResponse<int> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取验证码
  static Future<ApiResponse<String?>> getVerCode(
    String phone,
    String signature,
    String timestamp,
  ) async {
    try {
      var data = FormData.fromMap({
        "phone": phone,
        "signature": signature,
        "timestamp": timestamp,
        "platformType": "app",
      });

      var response = await HttpUtils.post(Api.getPhoneVerifyCode, data: data);

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 手机号验证码登录
  static Future<ApiResponse<Account?>> loginByPhone(
    String phone,
    String code,
  ) async {
    try {
      var data = FormData.fromMap({
        "phone": phone,
        "code": code,
      });

      var response = await HttpUtils.post(Api.loginByPhone, data: data);
      debugPrint("loginByPhone: $response");

      BaseResponse<Account> result = BaseResponse.fromJson(
        response,
        (json) => Account.fromJson(response),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("loginByPhone-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 霸王餐免登获取 token
  static Future<ApiResponse<BwcToken?>> fetchBwcToken(
    String phone, {
    String channelCode = 'msmds',
  }) async {
    try {
      var data = FormData.fromMap({
        "channelCode": channelCode,
        "phone": phone,
      });

      final response = await HttpUtils.post(FreeLunch.phoneLoginByNoVerify, data: data);

      BaseResponse<BwcToken?> result = BaseResponse.fromJson(
        response,
        (json) {
          if (json == null) {
            return null;
          }
          if (json is Map<String, dynamic>) {
            return BwcToken.fromJson(json);
          }
          if (json is Map) {
            return BwcToken.fromJson(Map<String, dynamic>.from(json));
          }
          return null;
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, e.toString(), -1));
    }
  }

  /// 获取并缓存霸王餐 token
  static Future<BwcToken?> fetchAndPersistBwcToken(
    String phone, {
    String channelCode = 'msmds',
  }) async {
    final response =
        await fetchBwcToken(phone, channelCode: channelCode);
    if (response.status == Status.completed && response.data != null) {
      final token = response.data!;
      await saveBwcToken(token);
      return token;
    }
    if (response.status == Status.error) {
      debugPrint('fetchAndPersistBwcToken error: ${response.exception}');
    }
    return null;
  }

  /// 读取本地缓存的霸王餐 token
  static BwcToken? getLocalBwcToken() {
    final bwcTokenJson = PrefsUtil().getJSON(PrefsKeys.bwcTokenKey);
    if (bwcTokenJson == null) {
      return null;
    }
    try {
      if (bwcTokenJson is Map<String, dynamic>) {
        return BwcToken.fromJson(bwcTokenJson);
      }
      if (bwcTokenJson is Map) {
        return BwcToken.fromJson(
          Map<String, dynamic>.from(bwcTokenJson),
        );
      }
    } catch (_) {
      return null;
    }
    return null;
  }

  /// 持久化霸王餐 token
  static Future<void> saveBwcToken(BwcToken? token) async {
    if (token == null || token.authorization == null) {
      final removeFuture = PrefsUtil().remove(PrefsKeys.bwcTokenKey);
      if (removeFuture != null) {
        await removeFuture;
      }
      GlobalConfig.bwcToken = null;
      GlobalConfig.bwcUserId = null;
      if (GlobalConfig.account != null) {
        GlobalConfig.account!.bwcToken = null;
        GlobalConfig.account!.bwcUserId = null;
        await GlobalConfig.saveProfile();
      }
      return;
    }

    final saveFuture = PrefsUtil().setJSON(PrefsKeys.bwcTokenKey, token.toJson());
    if (saveFuture != null) {
      await saveFuture;
    }
    GlobalConfig.bwcToken = token.authorization;
    GlobalConfig.bwcUserId = token.userId;
    if (GlobalConfig.account != null) {
      GlobalConfig.account!.bwcToken = token.authorization;
      GlobalConfig.account!.bwcUserId = token.userId;
      await GlobalConfig.saveProfile();
    }
  }

  /// 清除缓存的霸王餐 token
  static Future<void> clearBwcToken() async {
    await saveBwcToken(null);
  }

  /// 获取用户钱包信息
  static Future<ApiResponse<Wallet?>> getUserWallet() async {
    try {
      var response = await HttpUtils.get(Api.getUserWallet);

      BaseResponse<Wallet> result = BaseResponse.fromJson(
        response,
        (json) => Wallet.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 最新订单显示
  static Future<ApiResponse<Order?>> getNewestOrder() async {
    try {
      var response = await HttpUtils.get(Api.newestOrder);

      BaseResponse<Order> result = BaseResponse.fromJson(
        response,
        (json) => Order.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 用户详细信息
  static Future<ApiResponse<AccountDetail?>> getAccountDetail() async {
    try {
      var response = await HttpUtils.post(Api.accountInfo);

      BaseResponse<AccountDetail> result = BaseResponse.fromJson(
        response,
        (json) => AccountDetail.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 用户注销
  static Future<ApiResponse<bool?>> cancelAccount() async {
    try {
      var response = await HttpUtils.post(Api.cancelAccount);

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 提现账户详细信息
  static Future<ApiResponse<WithdrawalAccount?>> getWithdrawalAccount() async {
    try {
      var response = await HttpUtils.get(BillApi.getWithdrawalAccount);

      BaseResponse<WithdrawalAccount> result = BaseResponse.fromJson(
        response,
        (json) => WithdrawalAccount.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 提现金额列表，获取可提现金额列表
  static Future<ApiResponse<WithdrawalAmount?>> getWithdrawAmountList() async {
    try {
      var response = await HttpUtils.get(BillApi.getWithdrawAmountList);

      BaseResponse<WithdrawalAmount> result = BaseResponse.fromJson(
        response,
        (json) => WithdrawalAmount.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 绑定提现账户
  static Future<ApiResponse<BindAlipay?>> bindWithdrawalAccount(
    String phone,
    String name,
  ) async {
    try {
      var data = FormData.fromMap({
        "phone": phone,
        "name": name,
      });

      var response = await HttpUtils.post(
        Api.bindWithdrawalAccount,
        data: data,
      );

      BaseResponse<BindAlipay> result = BaseResponse.fromJson(
        response,
        (json) => BindAlipay.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 用户发起提现
  static Future<ApiResponse<WithdrawalMessage?>> initiateWithdrawal(
    String withdrawalType,
    String amount,
  ) async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      var data = {
        "version": packageInfo.version,
        "withdrawalType": withdrawalType,
        "amount": amount,
      };

      var response = await HttpUtils.post(
        BillApi.withdrawByAliPayNew,
        params: data,
      );

      BaseResponse<WithdrawalMessage> result = BaseResponse.fromJson(
        response,
        (json) => WithdrawalMessage.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("initiateWithdrawal-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 用户是否授权拼多多
  static Future<ApiResponse<bool>> userIsAuthPdd() async {
    try {
      var response = await HttpUtils.get(Api.isAuthPdd);

      debugPrint('userIsAuthPdd: $response');

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 用户获取拼多多授权链接
  static Future<ApiResponse<PddChangeUrl>> userGetAuthPddUrl() async {
    try {
      var response = await HttpUtils.get(Api.getAuthUrl);

      debugPrint('userGetAuthPddUrl: $response');

      BaseResponse<PddChangeUrl> result = BaseResponse.fromJson(
        response,
        (json) => PddChangeUrl.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 淘宝TopNative授权
  static Future<ApiResponse<bool>> userTopNativeAuth(
    String accessToken,
  ) async {
    try {
      var data = {
        "accessToken": accessToken,
      };

      var response = await HttpUtils.post(Api.tbTopNativeAuth, data: data);

      debugPrint('userTopNativeAuth: $response');

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取绑定手机号验证码
  static Future<ApiResponse<bool>> getBindPhoneVerCode(String phone) async {
    try {
      var data = {
        "phone": phone,
      };

      var response = await HttpUtils.get(Api.sendBindPhoneCode, params: data);

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 绑定手机号
  static Future<ApiResponse<String>> bindPhone(
      String phone, String code) async {
    try {
      var data = {
        "code": code,
        "phone": phone,
      };

      var response = await HttpUtils.post(Api.bindPhone, data: data);

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取用户0元购资格
  static Future<ApiResponse<ValidFreeBuy>> getUserValidFreeBuy() async {
    try {
      var response = await HttpUtils.get(Api.userValidFreeBuy);

      debugPrint('getUserValidFreeBuy: $response');

      BaseResponse<ValidFreeBuy> result = BaseResponse.fromJson(
        response,
        (json) => ValidFreeBuy.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint('getUserValidFreeBuy: $e');
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取用户0元商品列表
  static Future<ApiResponse<FreeBuyGood>> getUserFreeBuyGoods(
    int pageNo,
    int pageSize,
  ) async {
    try {
      var data = {
        "pkgId": "ptjb6m",
        "pageNo": pageNo,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.post(Api.giftGoodsList, data: data);

      BaseResponse<FreeBuyGood> result = BaseResponse.fromJson(
        response.data,
        (json) => FreeBuyGood.fromJson(response.data),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint('getUserValidFreeBuy: $e');
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 收藏 足迹 红包 新手视频总数
  static Future<ApiResponse<MyNumber>> myNumber() async {
    try {
      var response = await HttpUtils.get(Api.myNumber);

      BaseResponse<MyNumber> result = BaseResponse.fromJson(
        response,
        (json) => MyNumber.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("myNumber: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("myNumber-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 收藏数显示
  static Future<ApiResponse<dynamic>> collectCount() async {
    try {
      var response = await HttpUtils.get(Api.collectCount);

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("collectCount: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("collectCount-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 浏览足迹数显示
  static Future<ApiResponse<dynamic>> browsingCount() async {
    try {
      var response = await HttpUtils.get(Api.browsingCount);

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("browsingCount: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("browsingCount-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 浏览足迹数显示
  static Future<ApiResponse<RedPacketList>> getMyGiftList(
    int status,
    int pageNum,
    int pageSize,
  ) async {
    try {
      var data = {
        "status": status,
        "pageNum": pageNum,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.post(Api.getMyGiftList, data: data);

      debugPrint("getMyGiftList-res: $response");

      BaseResponse<RedPacketList> result = BaseResponse.fromJson(
        response,
        (json) => RedPacketList.fromJson(response),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getMyGiftList: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getMyGiftList-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 获取粉丝收益列表
  static Future<ApiResponse<UserFansList>> getInvitationInfo(
    int pageNum,
    int pageSize,
  ) async {
    try {
      var data = {
        "pageNum": pageNum,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.get(
        Api.getInvitationInfo,
        params: data,
      );

      BaseResponse<UserFansList> result = BaseResponse.fromJson(
        response,
        (json) => UserFansList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getInvitationInfo: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getInvitationInfo-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }
}
