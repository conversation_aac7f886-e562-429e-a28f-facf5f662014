import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/account/account_detail.dart';

part 'account.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: account_response
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/6 11:20
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/6 11:20
/// @UpdateRemark: 更新说明
@JsonSerializable()
class Account {
  AccountDetail? data;
  String? token;
  String? bwcToken;
  int? bwcUserId;

  Account({
    this.data,
    this.token,
    this.bwcToken,
    this.bwcUserId,
  });

  Account copyWith({
    AccountDetail? data,
    String? token,
    String? bwcToken,
    int? bwcUserId,
  }) {
    return Account(
      data: data ?? this.data,
      token: token ?? this.token,
      bwcToken: bwcToken ?? this.bwcToken,
      bwcUserId: bwcUserId ?? this.bwcUserId,
    );
  }

  factory Account.fromJson(Map<String, dynamic> json) =>
      _$AccountFromJson(json);

  Map<String, dynamic> toJson() => _$AccountToJson(this);
}
