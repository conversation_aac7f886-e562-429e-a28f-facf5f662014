
import 'package:json_annotation/json_annotation.dart';

part 'bwc_token.g.dart';

@JsonSerializable()
class BwcToken {
  final String? tokenPrefix;
  final String? token;
  final int? userId;

  BwcToken({
    this.tokenPrefix,
    this.token,
    this.userId,
  });
  String? get authorization {
    if (tokenPrefix == null || token == null) {
      return null;
    }
    final prefix = tokenPrefix!.trim();
    final value = token!.trim();
    if (prefix.isEmpty || value.isEmpty) {
      return null;
    }
    return '$prefix $value';
  }
  factory BwcToken.fromJson(Map<String, dynamic> json) =>
      _$BwcTokenFromJson(json);

  Map<String, dynamic> toJson() => _$BwcTokenToJson(this);
  
}
