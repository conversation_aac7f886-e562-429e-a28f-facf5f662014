// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdrawal_record_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WithdrawalRecordList _$WithdrawalRecordListFromJson(
        Map<String, dynamic> json) =>
    WithdrawalRecordList()
      ..hasNextPage = json['hasNextPage'] as bool?
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => WithdrawalRecord.fromJson(e as Map<String, dynamic>))
          .toList()
      ..total = (json['total'] as num?)?.toInt();

Map<String, dynamic> _$WithdrawalRecordListToJson(
        WithdrawalRecordList instance) =>
    <String, dynamic>{
      'hasNextPage': instance.hasNextPage,
      'list': instance.list,
      'total': instance.total,
    };
