// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'taobao_auth.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaoBaoAuth _$TaoBao<PERSON>uth<PERSON>romJson(Map<String, dynamic> json) => TaoBaoAuth()
  ..accountName = json['accountName'] as String?
  ..appKey = json['appKey'] as String?
  ..createTime = json['createTime'] as String?
  ..id = (json['id'] as num?)?.toInt()
  ..relationId = (json['relationId'] as num?)?.toInt()
  ..specialId = (json['specialId'] as num?)?.toInt()
  ..updateTime = json['updateTime'] as String?
  ..userId = (json['userId'] as num?)?.toInt();

Map<String, dynamic> _$TaoBaoAuthToJson(TaoBaoAuth instance) =>
    <String, dynamic>{
      'accountName': instance.accountName,
      'appKey': instance.appKey,
      'createTime': instance.createTime,
      'id': instance.id,
      'relationId': instance.relationId,
      'specialId': instance.specialId,
      'updateTime': instance.updateTime,
      'userId': instance.userId,
    };
