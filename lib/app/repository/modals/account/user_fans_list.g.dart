// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_fans_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserFansList _$UserFansListFromJson(Map<String, dynamic> json) => UserFansList()
  ..contributionNum = (json['contributionNum'] as num?)?.toInt()
  ..fansInfoList = (json['fansInfoList'] as List<dynamic>?)
      ?.map((e) => UserFansItem.fromJson(e as Map<String, dynamic>))
      .toList()
  ..hasNextPage = json['hasNextPage'] as bool?
  ..invitationNum = (json['invitationNum'] as num?)?.toInt()
  ..totalIncome = json['totalIncome'] as num?;

Map<String, dynamic> _$UserFansListToJson(UserFansList instance) =>
    <String, dynamic>{
      'contributionNum': instance.contributionNum,
      'fansInfoList': instance.fansInfoList,
      'hasNextPage': instance.hasNextPage,
      'invitationNum': instance.invitationNum,
      'totalIncome': instance.totalIncome,
    };
