import 'package:json_annotation/json_annotation.dart';

part 'user_integral.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: user_integral
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/13 17:17
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/13 17:17
/// @UpdateRemark: 更新说明
@JsonSerializable()
class UserIntegral {
  dynamic todayIntegral;
  dynamic data;
  dynamic simpleIntegral;

  UserIntegral();

  factory UserIntegral.fromJson(Map<String, dynamic> json) =>
      _$UserIntegralFromJson(json);

  Map<String, dynamic> toJson() => _$UserIntegralToJson(this);
}

