// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_tab_pkg.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HomeTabPkg _$HomeTabPkgFromJson(Map<String, dynamic> json) => HomeTabPkg()
  ..pkgName = json['pkgName'] as String?
  ..pkgId = json['pkgId'] as String?
  ..platformType = (json['platformType'] as num?)?.toInt()
  ..pkgType = (json['pkgType'] as num?)?.toInt();

Map<String, dynamic> _$HomeTabPkgToJson(HomeTabPkg instance) =>
    <String, dynamic>{
      'pkgName': instance.pkgName,
      'pkgId': instance.pkgId,
      'platformType': instance.platformType,
      'pkgType': instance.pkgType,
    };
