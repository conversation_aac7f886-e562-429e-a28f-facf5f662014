// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'goods_detail.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GoodsDetail _$GoodsDetailFromJson(Map<String, dynamic> json) => GoodsDetail()
  ..goodsUniqueId = json['goodsUniqueId'] as String?
  ..goodsId = json['goodsId'] as String?
  ..goodsName = json['goodsName'] as String?
  ..goodsImg = json['goodsImg'] as String?
  ..smallPicList =
      (json['smallPicList'] as List<dynamic>?)?.map((e) => e as String).toList()
  ..goodsUrl = json['goodsUrl'] as String?
  ..couponUrl = json['couponUrl'] as String?
  ..originalPrice = json['originalPrice'] as num?
  ..price = json['price'] as num?
  ..priceAfterCoupon = json['priceAfterCoupon'] as num?
  ..priceAfterReceive = json['priceAfterReceive'] as num?
  ..subtractRate = json['subtractRate'] as num?
  ..saleVolume = json['saleVolume'] as String?
  ..redPacketAmount = json['redPacketAmount'] as num?
  ..couponInfo = json['couponInfo'] as String?
  ..couponAmount = json['couponAmount'] as num?
  ..couponAmountInfo = json['couponAmountInfo'] as String?
  ..vipCommission = json['vipCommission'] as num?
  ..noVipCommission = json['noVipCommission'] as num?
  ..shopName = json['shopName'] as String?
  ..platformTag = json['platformTag'] as String?
  ..platformType = (json['platformType'] as num?)?.toInt()
  ..appCouponConfigList = (json['appCouponConfigList'] as List<dynamic>?)
      ?.map((e) => ConfigCoupon.fromJson(e as Map<String, dynamic>))
      .toList()
  ..bizSceneId = json['bizSceneId'] as String?
  ..subtractAmount = json['subtractAmount'] as num?
  ..jdCanUseCoupons = (json['jdCanUseCoupons'] as List<dynamic>?)
      ?.map((e) => JdUseCoupon.fromJson(e as Map<String, dynamic>))
      .toList()
  ..jdCanUseLabelInfoList = (json['jdCanUseLabelInfoList'] as List<dynamic>?)
      ?.map((e) => JdUseLabel.fromJson(e as Map<String, dynamic>))
      .toList();

Map<String, dynamic> _$GoodsDetailToJson(GoodsDetail instance) =>
    <String, dynamic>{
      'goodsUniqueId': instance.goodsUniqueId,
      'goodsId': instance.goodsId,
      'goodsName': instance.goodsName,
      'goodsImg': instance.goodsImg,
      'smallPicList': instance.smallPicList,
      'goodsUrl': instance.goodsUrl,
      'couponUrl': instance.couponUrl,
      'originalPrice': instance.originalPrice,
      'price': instance.price,
      'priceAfterCoupon': instance.priceAfterCoupon,
      'priceAfterReceive': instance.priceAfterReceive,
      'subtractRate': instance.subtractRate,
      'saleVolume': instance.saleVolume,
      'redPacketAmount': instance.redPacketAmount,
      'couponInfo': instance.couponInfo,
      'couponAmount': instance.couponAmount,
      'couponAmountInfo': instance.couponAmountInfo,
      'vipCommission': instance.vipCommission,
      'noVipCommission': instance.noVipCommission,
      'shopName': instance.shopName,
      'platformTag': instance.platformTag,
      'platformType': instance.platformType,
      'appCouponConfigList': instance.appCouponConfigList,
      'bizSceneId': instance.bizSceneId,
      'subtractAmount': instance.subtractAmount,
      'jdCanUseCoupons': instance.jdCanUseCoupons,
      'jdCanUseLabelInfoList': instance.jdCanUseLabelInfoList,
    };
