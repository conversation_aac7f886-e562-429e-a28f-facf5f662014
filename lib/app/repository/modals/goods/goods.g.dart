// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'goods.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Goods _$GoodsFromJson(Map<String, dynamic> json) => Goods()
  ..comments = json['comments'] as String?
  ..commentsNum = (json['commentsNum'] as num?)?.toInt()
  ..commissionRate = json['commissionRate'] as num?
  ..completeGoodsId = json['completeGoodsId'] as String?
  ..couponPrice = json['couponPrice'] as num?
  ..cover = json['cover'] as String?
  ..createTime = json['createTime'] as String?
  ..discountRate = json['discountRate'] as num?
  ..extendInfo = json['extendInfo'] as String?
  ..goodsCoupon = (json['goodsCoupon'] as List<dynamic>?)
      ?.map((e) => Coupon.fromJson(e as Map<String, dynamic>))
      .toList()
  ..goodsId = json['goodsId'] as String?
  ..goodsName = json['goodsName'] as String?
  ..goodsType = (json['goodsType'] as num?)?.toInt()
  ..goodsUrl = json['goodsUrl'] as String?
  ..id = (json['id'] as num?)?.toInt()
  ..originalPrice = json['originalPrice'] as num?
  ..receivedPrice = json['receivedPrice'] as num?
  ..saleVolume = (json['saleVolume'] as num?)?.toInt()
  ..shopName = json['shopName'] as String?
  ..smallPicList =
      (json['smallPicList'] as List<dynamic>?)?.map((e) => e as String).toList()
  ..subGoodsType = (json['subGoodsType'] as num?)?.toInt()
  ..totalCommission = json['totalCommission'] as num?
  ..updateTime = json['updateTime'] as String?
  ..userCommission = json['userCommission'] as num?;

Map<String, dynamic> _$GoodsToJson(Goods instance) => <String, dynamic>{
      'comments': instance.comments,
      'commentsNum': instance.commentsNum,
      'commissionRate': instance.commissionRate,
      'completeGoodsId': instance.completeGoodsId,
      'couponPrice': instance.couponPrice,
      'cover': instance.cover,
      'createTime': instance.createTime,
      'discountRate': instance.discountRate,
      'extendInfo': instance.extendInfo,
      'goodsCoupon': instance.goodsCoupon,
      'goodsId': instance.goodsId,
      'goodsName': instance.goodsName,
      'goodsType': instance.goodsType,
      'goodsUrl': instance.goodsUrl,
      'id': instance.id,
      'originalPrice': instance.originalPrice,
      'receivedPrice': instance.receivedPrice,
      'saleVolume': instance.saleVolume,
      'shopName': instance.shopName,
      'smallPicList': instance.smallPicList,
      'subGoodsType': instance.subGoodsType,
      'totalCommission': instance.totalCommission,
      'updateTime': instance.updateTime,
      'userCommission': instance.userCommission,
    };
