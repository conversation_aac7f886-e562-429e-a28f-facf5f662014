// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'goods_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GoodsList _$GoodsListFromJson(Map<String, dynamic> json) => GoodsList()
  ..hasNextPage = json['hasNextPage'] as bool?
  ..list = (json['list'] as List<dynamic>?)
      ?.map((e) => Goods.fromJson(e as Map<String, dynamic>))
      .toList()
  ..total = (json['total'] as num?)?.toInt();

Map<String, dynamic> _$GoodsListToJson(GoodsList instance) => <String, dynamic>{
      'hasNextPage': instance.hasNextPage,
      'list': instance.list,
      'total': instance.total,
    };
