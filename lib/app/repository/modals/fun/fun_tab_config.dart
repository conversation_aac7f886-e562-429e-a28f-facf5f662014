import 'package:json_annotation/json_annotation.dart';

part 'fun_tab_config.g.dart';

@JsonSerializable(explicitToJson: true)
class FunTabConfig {
  final String? name;
  final String? code;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'icon_url')
  final String? iconUrl;
  final String? tabName;
  final String? title;
  final int? platform;
  @Json<PERSON>ey(name: 'listTopiId')
  final int? listTopiId;
  final int? bizLine;
  final int? val;
  final String? icon;
  final List<FunTabConfig>? category;
  final List<FunTabConfig>? children;
  final List<FunSortOption>? sort;

  const FunTabConfig({
    this.name,
    this.code,
    this.iconUrl,
    this.tabName,
    this.title,
    this.platform,
    this.listTopiId,
    this.bizLine,
    this.val,
    this.icon,
    this.category,
    this.children,
    this.sort,
  });

  String? get displayName => tabName ?? name ?? title;

  factory FunTabConfig.fromJson(Map<String, dynamic> json) =>
      _$FunTabConfigFromJson(json);

  Map<String, dynamic> toJson() => _$FunTabConfigToJson(this);
}

@JsonSerializable()
class FunSortOption {
  final String? sortName;
  final int? sortField;
  final int? ascDescOrder;
  final int? sortValue;

  const FunSortOption({
    this.sortName,
    this.sortField,
    this.ascDescOrder,
    this.sortValue,
  });

  factory FunSortOption.fromJson(Map<String, dynamic> json) =>
      _$FunSortOptionFromJson(json);

  Map<String, dynamic> toJson() => _$FunSortOptionToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FunSortOption &&
          runtimeType == other.runtimeType &&
          sortName == other.sortName &&
          sortField == other.sortField &&
          ascDescOrder == other.ascDescOrder &&
          sortValue == other.sortValue;

  @override
  int get hashCode =>
      sortName.hashCode ^
      sortField.hashCode ^
      ascDescOrder.hashCode ^
      sortValue.hashCode;
}

class FunTabResult {
  final List<FunTabConfig> tabs;

  const FunTabResult({
    this.tabs = const [],
  });
}
