import 'package:json_annotation/json_annotation.dart';

part 'bawangcan_registration_record.g.dart';

/// 霸王餐报名记录响应数据模型
@JsonSerializable(explicitToJson: true)
class BawangcanRegistrationRecordResponse {
  /// 记录列表
  final List<BawangcanRegistrationRecord>? list;

  /// 是否有下一页
  final bool? hasNextPage;

  /// 总数量
  final int? total;

  const BawangcanRegistrationRecordResponse({
    this.list,
    this.hasNextPage,
    this.total,
  });

  factory BawangcanRegistrationRecordResponse.fromJson(
          Map<String, dynamic> json) =>
      _$BawangcanRegistrationRecordResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$BawangcanRegistrationRecordResponseToJson(this);
}

/// 霸王餐报名记录数据模型
@JsonSerializable(explicitToJson: true)
class BawangcanRegistrationRecord {
  /// 记录ID
  final int? id;

  /// 渠道ID
  final int? channelId;

  /// 用户ID
  final int? userId;

  /// 报名手机号
  final String? mobile;

  /// 报名ID
  final String? applyId;

  /// 报名订单号
  final String? applyOrderNo;

  /// 订单号
  final String? orderNo;

  /// 过期时间
  final String? expireTime;

  /// 报名状态 (1:已过期, 2:已取消, 3:已报名, 4:已下单, 5:已评价, 6:订单无效)
  final int? state;

  /// 活动ID
  final String? activityId;

  /// 商铺ID
  final String? shopId;

  /// 创建时间
  final String? createTime;

  /// 更新时间
  final String? updateTime;

  /// 报名商铺信息
  final BawangcanApplyOrderShop? applyOrderShop;

  const BawangcanRegistrationRecord({
    this.id,
    this.channelId,
    this.userId,
    this.mobile,
    this.applyId,
    this.applyOrderNo,
    this.orderNo,
    this.expireTime,
    this.state,
    this.activityId,
    this.shopId,
    this.createTime,
    this.updateTime,
    this.applyOrderShop,
  });

  factory BawangcanRegistrationRecord.fromJson(Map<String, dynamic> json) =>
      _$BawangcanRegistrationRecordFromJson(json);

  Map<String, dynamic> toJson() => _$BawangcanRegistrationRecordToJson(this);
}

/// 霸王餐报名商铺信息数据模型
@JsonSerializable(explicitToJson: true)
class BawangcanApplyOrderShop {
  /// 记录ID
  final int? id;

  /// 报名订单ID
  final String? applyOrderId;

  /// 活动ID
  final String? activityId;

  /// 商铺ID
  final String? shopId;

  /// 商铺名称
  final String? shopName;

  /// 商铺图片
  final String? shopImg;

  /// 申请类型 (1:美团, 2:饿了么)
  final int? applyType;

  /// 佣金类型
  final int? commissionType;

  /// 活动类型 (1:需评价, 其他:无需评价)
  final int? activityType;

  /// 返利比例
  final double? commissionRate;

  /// 返利金额
  final double? commission;

  /// 返利门槛金额（分）
  final int? commissionThresholdCent;

  /// 最高返利金额
  final double? maxCommission;

  /// 官方返利比例
  final double? officialCommissionRate;

  /// 官方返利金额
  final double? officialCommission;

  /// 官方最高返利金额
  final double? officialMaxCommission;

  /// 活动链接信息
  final BawangcanActionUrl? actionUrl;

  /// 创建时间
  final String? createTime;

  /// 更新时间
  final String? updateTime;

  const BawangcanApplyOrderShop({
    this.id,
    this.applyOrderId,
    this.activityId,
    this.shopId,
    this.shopName,
    this.shopImg,
    this.applyType,
    this.commissionType,
    this.activityType,
    this.commissionRate,
    this.commission,
    this.commissionThresholdCent,
    this.maxCommission,
    this.officialCommissionRate,
    this.officialCommission,
    this.officialMaxCommission,
    this.actionUrl,
    this.createTime,
    this.updateTime,
  });

  factory BawangcanApplyOrderShop.fromJson(Map<String, dynamic> json) =>
      _$BawangcanApplyOrderShopFromJson(json);

  Map<String, dynamic> toJson() => _$BawangcanApplyOrderShopToJson(this);
}

/// 霸王餐活动链接信息数据模型
@JsonSerializable(explicitToJson: true)
class BawangcanActionUrl {
  /// 大众点评链接
  final String? dpUrl;

  /// H5链接
  final String? h5Url;

  /// 微信小程序路径
  final String? wxPath;

  /// 微信小程序AppId
  final String? wxAppId;

  /// 微信小程序原始ID
  final String? wxAppOrgId;

  const BawangcanActionUrl({
    this.dpUrl,
    this.h5Url,
    this.wxPath,
    this.wxAppId,
    this.wxAppOrgId,
  });

  factory BawangcanActionUrl.fromJson(Map<String, dynamic> json) =>
      _$BawangcanActionUrlFromJson(json);

  Map<String, dynamic> toJson() => _$BawangcanActionUrlToJson(this);
}

/// 霸王餐报名记录查询参数
@JsonSerializable()
class BawangcanRegistrationRecordParams {
  /// 页码
  final int pageNum;

  /// 每页大小
  final int pageSize;

  /// 状态筛选 (可选: 1:已过期, 2:已取消, 3:已报名, 4:已下单, 5:已评价, 6:订单无效)
  final int? state;

  const BawangcanRegistrationRecordParams({
    required this.pageNum,
    required this.pageSize,
    this.state,
  });

  factory BawangcanRegistrationRecordParams.fromJson(
          Map<String, dynamic> json) =>
      _$BawangcanRegistrationRecordParamsFromJson(json);

  Map<String, dynamic> toJson() =>
      _$BawangcanRegistrationRecordParamsToJson(this);
}

/// 霸王餐报名记录标签页数据模型
class BawangcanRegistrationRecordTab {
  /// 标签名称
  final String tabName;

  /// 对应的状态值 (可选)
  final int? state;

  const BawangcanRegistrationRecordTab({
    required this.tabName,
    this.state,
  });
}

/// 预定义的标签页数据
const List<BawangcanRegistrationRecordTab> bawangcanRegistrationRecordTabs = [
  BawangcanRegistrationRecordTab(tabName: '全部'),
  BawangcanRegistrationRecordTab(tabName: '已报名', state: 3),
  BawangcanRegistrationRecordTab(tabName: '已取消', state: 2),
  BawangcanRegistrationRecordTab(tabName: '已过期', state: 1),
  BawangcanRegistrationRecordTab(tabName: '已下单', state: 4),
];
