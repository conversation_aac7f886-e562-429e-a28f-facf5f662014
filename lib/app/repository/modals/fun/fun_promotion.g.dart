// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fun_promotion.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FunPromotionCollection _$FunPromotionCollectionFromJson(
        Map<String, dynamic> json) =>
    FunPromotionCollection()
      ..type = (json['type'] as num?)?.toInt()
      ..typeName = json['typeName'] as String?
      ..typeData =
          const _TypeDataListConverter().fromJson(json['typeData'] as List?);

Map<String, dynamic> _$FunPromotionCollectionToJson(
        FunPromotionCollection instance) =>
    <String, dynamic>{
      'type': instance.type,
      'typeName': instance.typeName,
      'typeData': const _TypeDataListConverter().toJson(instance.typeData),
    };

TypeData _$TypeDataFromJson(Map<String, dynamic> json) => TypeData(
      url: json['url'] as String?,
      jump: json['jump'] == null
          ? null
          : Jump.fromJson(json['jump'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TypeDataToJson(TypeData instance) => <String, dynamic>{
      'url': instance.url,
      'jump': instance.jump?.toJson(),
    };

Jump _$JumpFromJson(Map<String, dynamic> json) => Jump(
      type: (json['type'] as num?)?.toInt(),
      needLogin: json['needLogin'] as bool?,
      profile: json['profile'] as String?,
      title: json['title'] as String?,
      desc: json['desc'] as String?,
      promotionId: json['promotionId'] as String?,
      isMiniApp: json['isMiniApp'] as bool?,
      activityType: json['activityType'] as String?,
      jumpPlaform: json['jumpPlaform'] as String?,
      siteId: (json['siteId'] as num?)?.toInt(),
      rebateRuleUrl: json['rebateRuleUrl'] as String?,
    );

Map<String, dynamic> _$JumpToJson(Jump instance) => <String, dynamic>{
      'type': instance.type,
      'needLogin': instance.needLogin,
      'profile': instance.profile,
      'title': instance.title,
      'desc': instance.desc,
      'promotionId': instance.promotionId,
      'isMiniApp': instance.isMiniApp,
      'activityType': instance.activityType,
      'jumpPlaform': instance.jumpPlaform,
      'siteId': instance.siteId,
      'rebateRuleUrl': instance.rebateRuleUrl,
    };
