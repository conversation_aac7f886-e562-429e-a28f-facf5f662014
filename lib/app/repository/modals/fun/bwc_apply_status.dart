import 'package:json_annotation/json_annotation.dart';

part 'bwc_apply_status.g.dart';

/// 霸王餐报名状态数据模型
@JsonSerializable()
class   BwcApplyStatus {
  /// 报名手机号
  final String? mobile;

  /// 报名过期时间
  final String? expireTime;

  /// 报名ID
  final String? applyId;

  /// 订单ID
  final String? orderId;

  /// 活动ID
  final String? activityId;

  /// 商铺ID
  final String? shopId;

  BwcApplyStatus({
    this.mobile,
    this.expireTime,
    this.applyId,
    this.orderId,
    this.activityId,
    this.shopId,
  });

  factory BwcApplyStatus.fromJson(Map<String, dynamic> json) =>
      _$BwcApplyStatusFromJson(json);

  Map<String, dynamic> toJson() => _$BwcApplyStatusToJson(this);
}
