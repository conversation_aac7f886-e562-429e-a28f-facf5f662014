// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bawangcan_registration_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BawangcanRegistrationRecordResponse
    _$BawangcanRegistrationRecordResponseFromJson(Map<String, dynamic> json) =>
        BawangcanRegistrationRecordResponse(
          list: (json['list'] as List<dynamic>?)
              ?.map((e) => BawangcanRegistrationRecord.fromJson(
                  e as Map<String, dynamic>))
              .toList(),
          hasNextPage: json['hasNextPage'] as bool?,
          total: (json['total'] as num?)?.toInt(),
        );

Map<String, dynamic> _$BawangcanRegistrationRecordResponseToJson(
        BawangcanRegistrationRecordResponse instance) =>
    <String, dynamic>{
      'list': instance.list?.map((e) => e.toJson()).toList(),
      'hasNextPage': instance.hasNextPage,
      'total': instance.total,
    };

BawangcanRegistrationRecord _$BawangcanRegistrationRecordFromJson(
        Map<String, dynamic> json) =>
    BawangcanRegistrationRecord(
      id: (json['id'] as num?)?.toInt(),
      channelId: (json['channelId'] as num?)?.toInt(),
      userId: (json['userId'] as num?)?.toInt(),
      mobile: json['mobile'] as String?,
      applyId: json['applyId'] as String?,
      applyOrderNo: json['applyOrderNo'] as String?,
      orderNo: json['orderNo'] as String?,
      expireTime: json['expireTime'] as String?,
      state: (json['state'] as num?)?.toInt(),
      activityId: json['activityId'] as String?,
      shopId: json['shopId'] as String?,
      createTime: json['createTime'] as String?,
      updateTime: json['updateTime'] as String?,
      applyOrderShop: json['applyOrderShop'] == null
          ? null
          : BawangcanApplyOrderShop.fromJson(
              json['applyOrderShop'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BawangcanRegistrationRecordToJson(
        BawangcanRegistrationRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'channelId': instance.channelId,
      'userId': instance.userId,
      'mobile': instance.mobile,
      'applyId': instance.applyId,
      'applyOrderNo': instance.applyOrderNo,
      'orderNo': instance.orderNo,
      'expireTime': instance.expireTime,
      'state': instance.state,
      'activityId': instance.activityId,
      'shopId': instance.shopId,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
      'applyOrderShop': instance.applyOrderShop?.toJson(),
    };

BawangcanApplyOrderShop _$BawangcanApplyOrderShopFromJson(
        Map<String, dynamic> json) =>
    BawangcanApplyOrderShop(
      id: (json['id'] as num?)?.toInt(),
      applyOrderId: json['applyOrderId'] as String?,
      activityId: json['activityId'] as String?,
      shopId: json['shopId'] as String?,
      shopName: json['shopName'] as String?,
      shopImg: json['shopImg'] as String?,
      applyType: (json['applyType'] as num?)?.toInt(),
      commissionType: (json['commissionType'] as num?)?.toInt(),
      activityType: (json['activityType'] as num?)?.toInt(),
      commissionRate: (json['commissionRate'] as num?)?.toDouble(),
      commission: (json['commission'] as num?)?.toDouble(),
      commissionThresholdCent:
          (json['commissionThresholdCent'] as num?)?.toInt(),
      maxCommission: (json['maxCommission'] as num?)?.toDouble(),
      officialCommissionRate:
          (json['officialCommissionRate'] as num?)?.toDouble(),
      officialCommission: (json['officialCommission'] as num?)?.toDouble(),
      officialMaxCommission:
          (json['officialMaxCommission'] as num?)?.toDouble(),
      actionUrl: json['actionUrl'] == null
          ? null
          : BawangcanActionUrl.fromJson(
              json['actionUrl'] as Map<String, dynamic>),
      createTime: json['createTime'] as String?,
      updateTime: json['updateTime'] as String?,
    );

Map<String, dynamic> _$BawangcanApplyOrderShopToJson(
        BawangcanApplyOrderShop instance) =>
    <String, dynamic>{
      'id': instance.id,
      'applyOrderId': instance.applyOrderId,
      'activityId': instance.activityId,
      'shopId': instance.shopId,
      'shopName': instance.shopName,
      'shopImg': instance.shopImg,
      'applyType': instance.applyType,
      'commissionType': instance.commissionType,
      'activityType': instance.activityType,
      'commissionRate': instance.commissionRate,
      'commission': instance.commission,
      'commissionThresholdCent': instance.commissionThresholdCent,
      'maxCommission': instance.maxCommission,
      'officialCommissionRate': instance.officialCommissionRate,
      'officialCommission': instance.officialCommission,
      'officialMaxCommission': instance.officialMaxCommission,
      'actionUrl': instance.actionUrl?.toJson(),
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
    };

BawangcanActionUrl _$BawangcanActionUrlFromJson(Map<String, dynamic> json) =>
    BawangcanActionUrl(
      dpUrl: json['dpUrl'] as String?,
      h5Url: json['h5Url'] as String?,
      wxPath: json['wxPath'] as String?,
      wxAppId: json['wxAppId'] as String?,
      wxAppOrgId: json['wxAppOrgId'] as String?,
    );

Map<String, dynamic> _$BawangcanActionUrlToJson(BawangcanActionUrl instance) =>
    <String, dynamic>{
      'dpUrl': instance.dpUrl,
      'h5Url': instance.h5Url,
      'wxPath': instance.wxPath,
      'wxAppId': instance.wxAppId,
      'wxAppOrgId': instance.wxAppOrgId,
    };

BawangcanRegistrationRecordParams _$BawangcanRegistrationRecordParamsFromJson(
        Map<String, dynamic> json) =>
    BawangcanRegistrationRecordParams(
      pageNum: (json['pageNum'] as num).toInt(),
      pageSize: (json['pageSize'] as num).toInt(),
      state: (json['state'] as num?)?.toInt(),
    );

Map<String, dynamic> _$BawangcanRegistrationRecordParamsToJson(
        BawangcanRegistrationRecordParams instance) =>
    <String, dynamic>{
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'state': instance.state,
    };
