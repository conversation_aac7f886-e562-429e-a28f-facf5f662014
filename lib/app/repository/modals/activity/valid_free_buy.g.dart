// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'valid_free_buy.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ValidFreeBuy _$ValidFreeBuyFromJson(Map<String, dynamic> json) => ValidFreeBuy()
  ..activityType = (json['activityType'] as num?)?.toInt()
  ..createTime = json['createTime'] as String?
  ..description = json['description'] as String?
  ..expiryDate = json['expiryDate'] as String?
  ..goodsId = json['goodsId'] as String?
  ..id = (json['id'] as num?)?.toInt()
  ..state = (json['state'] as num?)?.toInt()
  ..subsidyAmount = json['subsidyAmount'] as num?
  ..subsidyType = (json['subsidyType'] as num?)?.toInt()
  ..updateTime = json['updateTime'] as String?
  ..useTime = json['useTime'] as String?
  ..userId = (json['userId'] as num?)?.toInt();

Map<String, dynamic> _$ValidFreeBuyToJson(ValidFreeBuy instance) =>
    <String, dynamic>{
      'activityType': instance.activityType,
      'createTime': instance.createTime,
      'description': instance.description,
      'expiryDate': instance.expiryDate,
      'goodsId': instance.goodsId,
      'id': instance.id,
      'state': instance.state,
      'subsidyAmount': instance.subsidyAmount,
      'subsidyType': instance.subsidyType,
      'updateTime': instance.updateTime,
      'useTime': instance.useTime,
      'userId': instance.userId,
    };
