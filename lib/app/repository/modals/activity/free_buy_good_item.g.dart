// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'free_buy_good_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FreeBuyGoodItem _$FreeBuyGoodItemFromJson(Map<String, dynamic> json) =>
    FreeBuyGoodItem()
      ..sku = json['sku'] as String?
      ..gift = json['gift'] as String?
      ..discountRate = json['discountRate'] as num?
      ..giftCount0 = json['giftCount0'] as num?
      ..giftCount1 = json['giftCount1'] as num?
      ..commissionRate = json['commissionRate'] as num?
      ..priceAfterCoupon = json['priceAfterCoupon'] as num?
      ..isPinGou = (json['isPinGou'] as num?)?.toInt()
      ..shopName = json['shopName'] as String?
      ..discount0 = json['discount0'] as num?
      ..cover = json['cover'] as String?
      ..swiperList = (json['swiperList'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList()
      ..detailPics = json['detailPics'] as String?
      ..couponAmount = json['couponAmount'] as num?
      ..couponLimit = json['couponLimit'] as num?
      ..priceUseGift = json['priceUseGift'] as num?
      ..noVipPriceUseGift = json['noVipPriceUseGift'] as num?
      ..hasCoupon = (json['hasCoupon'] as num?)?.toInt()
      ..pingouPrice = json['pingouPrice'] as num?
      ..wlPrice = json['wlPrice'] as num?
      ..lowestPrice = json['lowestPrice'] as num?
      ..discount1 = json['discount1'] as num?
      ..commission = json['commission'] as num?
      ..noVipCommission = json['noVipCommission'] as num?
      ..useGiftRateSort = json['useGiftRateSort'] as num?
      ..couponTips = json['couponTips'] as String?
      ..couponUrl = json['couponUrl'] as String?
      ..useGiftRate = json['useGiftRate'] as num?
      ..priceAfterReceive = json['priceAfterReceive'] as num?
      ..noVipPriceAfterReceive = json['noVipPriceAfterReceive'] as num?
      ..extraCommission = json['extraCommission'] as num?
      ..name = json['name'] as String?
      ..logoName = json['logoName'] as String?
      ..skuId = json['skuId'] as String?
      ..tbItemId = json['tbItemId'] as String?
      ..title = json['title'] as String?
      ..volume = json['volume'] as String?
      ..venderName = json['venderName'] as String?;

Map<String, dynamic> _$FreeBuyGoodItemToJson(FreeBuyGoodItem instance) =>
    <String, dynamic>{
      'sku': instance.sku,
      'gift': instance.gift,
      'discountRate': instance.discountRate,
      'giftCount0': instance.giftCount0,
      'giftCount1': instance.giftCount1,
      'commissionRate': instance.commissionRate,
      'priceAfterCoupon': instance.priceAfterCoupon,
      'isPinGou': instance.isPinGou,
      'shopName': instance.shopName,
      'discount0': instance.discount0,
      'cover': instance.cover,
      'swiperList': instance.swiperList,
      'detailPics': instance.detailPics,
      'couponAmount': instance.couponAmount,
      'couponLimit': instance.couponLimit,
      'priceUseGift': instance.priceUseGift,
      'noVipPriceUseGift': instance.noVipPriceUseGift,
      'hasCoupon': instance.hasCoupon,
      'pingouPrice': instance.pingouPrice,
      'wlPrice': instance.wlPrice,
      'lowestPrice': instance.lowestPrice,
      'discount1': instance.discount1,
      'commission': instance.commission,
      'noVipCommission': instance.noVipCommission,
      'useGiftRateSort': instance.useGiftRateSort,
      'couponTips': instance.couponTips,
      'couponUrl': instance.couponUrl,
      'useGiftRate': instance.useGiftRate,
      'priceAfterReceive': instance.priceAfterReceive,
      'noVipPriceAfterReceive': instance.noVipPriceAfterReceive,
      'extraCommission': instance.extraCommission,
      'name': instance.name,
      'logoName': instance.logoName,
      'skuId': instance.skuId,
      'tbItemId': instance.tbItemId,
      'title': instance.title,
      'volume': instance.volume,
      'venderName': instance.venderName,
    };
