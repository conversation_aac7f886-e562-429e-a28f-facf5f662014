// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jd_can_use_label.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JdCanUseLabel _$JdCanUseLabelFromJson(Map<String, dynamic> json) =>
    JdCanUseLabel()
      ..endTime = (json['endTime'] as num?)?.toInt()
      ..labelName = json['labelName'] as String?
      ..promotionLabel = json['promotionLabel'] as String?
      ..promotionLabelId = (json['promotionLabelId'] as num?)?.toInt()
      ..startTime = (json['startTime'] as num?)?.toInt();

Map<String, dynamic> _$JdCanUseLabelToJson(JdCanUseLabel instance) =>
    <String, dynamic>{
      'endTime': instance.endTime,
      'labelName': instance.labelName,
      'promotionLabel': instance.promotionLabel,
      'promotionLabelId': instance.promotionLabelId,
      'startTime': instance.startTime,
    };
