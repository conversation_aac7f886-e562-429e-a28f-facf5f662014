// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'convert_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConvertData _$ConvertDataFromJson(Map<String, dynamic> json) => ConvertData()
  ..aggregateSearchGoods = json['aggregateSearchGoods'] == null
      ? null
      : ConvertGoods.fromJson(
          json['aggregateSearchGoods'] as Map<String, dynamic>)
  ..cardText = json['cardText'] as String?
  ..comparePrice = json['comparePrice'] as bool?
  ..dialogTitle = json['dialogTitle'] as String?
  ..displaySearchWord = json['displaySearchWord'] as String?
  ..displayStandardSearchWord = json['displayStandardSearchWord'] as String?
  ..goods = json['goods'] == null
      ? null
      : ConvertGoods.fromJson(json['goods'] as Map<String, dynamic>)
  ..goodsCount = (json['goodsCount'] as num?)?.toInt()
  ..isShow = json['isShow'] as bool?
  ..isUrlStart = json['isUrlStart'] as bool?
  ..leftButtonText = json['leftButtonText'] as String?
  ..leftButtonTips = json['leftButtonTips'] as String?
  ..rightButtonText = json['rightButtonText'] as String?
  ..rightButtonTips = json['rightButtonTips'] as String?
  ..searchBtn = json['searchBtn'] as String?
  ..searchType = (json['searchType'] as num?)?.toInt()
  ..searchWord = json['searchWord'] as String?;

Map<String, dynamic> _$ConvertDataToJson(ConvertData instance) =>
    <String, dynamic>{
      'aggregateSearchGoods': instance.aggregateSearchGoods,
      'cardText': instance.cardText,
      'comparePrice': instance.comparePrice,
      'dialogTitle': instance.dialogTitle,
      'displaySearchWord': instance.displaySearchWord,
      'displayStandardSearchWord': instance.displayStandardSearchWord,
      'goods': instance.goods,
      'goodsCount': instance.goodsCount,
      'isShow': instance.isShow,
      'isUrlStart': instance.isUrlStart,
      'leftButtonText': instance.leftButtonText,
      'leftButtonTips': instance.leftButtonTips,
      'rightButtonText': instance.rightButtonText,
      'rightButtonTips': instance.rightButtonTips,
      'searchBtn': instance.searchBtn,
      'searchType': instance.searchType,
      'searchWord': instance.searchWord,
    };
