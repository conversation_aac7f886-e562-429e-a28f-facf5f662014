// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_gift_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderGiftItem _$OrderGiftItemFromJson(Map<String, dynamic> json) =>
    OrderGiftItem()
      ..activationTime = json['activationTime'] as String?
      ..createTime = json['createTime'] as String?
      ..duration = (json['duration'] as num?)?.toInt()
      ..endTime = json['endTime'] as String?
      ..forwardUserId = (json['forwardUserId'] as num?)?.toInt()
      ..fromOrderId = (json['fromOrderId'] as num?)?.toInt()
      ..giftId = (json['giftId'] as num?)?.toInt()
      ..invalidTime = json['invalidTime'] as String?
      ..isUse = json['isUse'] as bool?
      ..money = (json['money'] as num?)?.toInt()
      ..moneyLimit = (json['moneyLimit'] as num?)?.toInt()
      ..orderId = (json['orderId'] as num?)?.toInt()
      ..orderNo = json['orderNo'] as String?
      ..orderStatusDesc = json['orderStatusDesc'] as String?
      ..overDue = json['overDue'] as bool?
      ..qlFissionSubsidyRedPacket = json['qlFissionSubsidyRedPacket'] as bool?
      ..shopId = (json['shopId'] as num?)?.toInt()
      ..skuId = json['skuId'] as String?
      ..status = (json['status'] as num?)?.toInt()
      ..typeDesc = json['typeDesc'] as String?
      ..typeId = (json['typeId'] as num?)?.toInt()
      ..updateTime = json['updateTime'] as String?
      ..useTime = json['useTime'] as String?
      ..useType = (json['useType'] as num?)?.toInt()
      ..userId = (json['userId'] as num?)?.toInt();

Map<String, dynamic> _$OrderGiftItemToJson(OrderGiftItem instance) =>
    <String, dynamic>{
      'activationTime': instance.activationTime,
      'createTime': instance.createTime,
      'duration': instance.duration,
      'endTime': instance.endTime,
      'forwardUserId': instance.forwardUserId,
      'fromOrderId': instance.fromOrderId,
      'giftId': instance.giftId,
      'invalidTime': instance.invalidTime,
      'isUse': instance.isUse,
      'money': instance.money,
      'moneyLimit': instance.moneyLimit,
      'orderId': instance.orderId,
      'orderNo': instance.orderNo,
      'orderStatusDesc': instance.orderStatusDesc,
      'overDue': instance.overDue,
      'qlFissionSubsidyRedPacket': instance.qlFissionSubsidyRedPacket,
      'shopId': instance.shopId,
      'skuId': instance.skuId,
      'status': instance.status,
      'typeDesc': instance.typeDesc,
      'typeId': instance.typeId,
      'updateTime': instance.updateTime,
      'useTime': instance.useTime,
      'useType': instance.useType,
      'userId': instance.userId,
    };
