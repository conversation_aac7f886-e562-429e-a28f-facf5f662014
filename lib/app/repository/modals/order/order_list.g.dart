// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderList _$OrderListFromJson(Map<String, dynamic> json) => OrderList()
  ..hasNextPage = json['hasNextPage'] as bool?
  ..list = (json['list'] as List<dynamic>?)
      ?.map((e) => OrderDetail.fromJson(e as Map<String, dynamic>))
      .toList()
  ..total = (json['total'] as num?)?.toInt();

Map<String, dynamic> _$OrderListToJson(OrderList instance) => <String, dynamic>{
      'hasNextPage': instance.hasNextPage,
      'list': instance.list,
      'total': instance.total,
    };
