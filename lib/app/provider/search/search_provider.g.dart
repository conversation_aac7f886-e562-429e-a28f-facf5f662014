// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$searchKeywordHash() => r'5efa0a7e3b7a4a4fe55620b373c5e18ea592349b';

/// 搜索词
///
/// Copied from [SearchKeyword].
@ProviderFor(SearchKeyword)
final searchKeywordProvider =
    AutoDisposeNotifierProvider<SearchKeyword, String?>.internal(
  SearchKeyword.new,
  name: r'searchKeywordProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchKeywordHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SearchKeyword = AutoDisposeNotifier<String?>;
String _$searchHash() => r'359e578540ee372be0f223fa925d69232b2f688c';

/// See also [Search].
@ProviderFor(Search)
final searchProvider =
    AutoDisposeNotifierProvider<Search, SearchResult>.internal(
  Search.new,
  name: r'searchProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$searchHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Search = AutoDisposeNotifier<SearchResult>;
String _$searchFocusNodeHash() => r'5ece5e8a8e647d3a72bb0b8b40be9d910eb7a4fc';

/// search header focus
///
/// Copied from [SearchFocusNode].
@ProviderFor(SearchFocusNode)
final searchFocusNodeProvider =
    AutoDisposeNotifierProvider<SearchFocusNode, FocusNode>.internal(
  SearchFocusNode.new,
  name: r'searchFocusNodeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchFocusNodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SearchFocusNode = AutoDisposeNotifier<FocusNode>;
String _$searchAutoFocusHash() => r'e58e7a9637a1dce7e21c5b57adecbab3b80713fa';

/// 搜索页是否自动获取键盘焦点
///
/// Copied from [SearchAutoFocus].
@ProviderFor(SearchAutoFocus)
final searchAutoFocusProvider =
    AutoDisposeNotifierProvider<SearchAutoFocus, bool>.internal(
  SearchAutoFocus.new,
  name: r'searchAutoFocusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchAutoFocusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SearchAutoFocus = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
