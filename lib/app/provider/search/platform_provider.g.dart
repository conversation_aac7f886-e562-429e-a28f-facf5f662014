// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'platform_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$platformListHash() => r'7082c84e3f750e09b98d326e6bc0880eaa33536e';

/// See also [PlatformList].
@ProviderFor(PlatformList)
final platformListProvider =
    AutoDisposeNotifierProvider<PlatformList, List<PlatformData>>.internal(
  PlatformList.new,
  name: r'platformListProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$platformListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PlatformList = AutoDisposeNotifier<List<PlatformData>>;
String _$platformHash() => r'd088dbae5ff99331ea8019e21e8f09d61ae166da';

/// See also [Platform].
@ProviderFor(Platform)
final platformProvider =
    AutoDisposeNotifierProvider<Platform, PlatformData>.internal(
  Platform.new,
  name: r'platformProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$platformHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Platform = AutoDisposeNotifier<PlatformData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
