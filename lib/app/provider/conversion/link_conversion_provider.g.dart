// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'link_conversion_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$onGoodsItemTapHash() => r'7e3d983a1c0c79155bdf5dd142f1241b9769386c';

/// 商品统一点击入口
///
/// Copied from [OnGoodsItemTap].
@ProviderFor(OnGoodsItemTap)
final onGoodsItemTapProvider =
    AutoDisposeNotifierProvider<OnGoodsItemTap, void>.internal(
  OnGoodsItemTap.new,
  name: r'onGoodsItemTapProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$onGoodsItemTapHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OnGoodsItemTap = AutoDisposeNotifier<void>;
String _$tbConversionHash() => r'3f60c1fda3c2231ff39182e786c43c9a0cd09cb2';

/// See also [TbConversion].
@ProviderFor(TbConversion)
final tbConversionProvider =
    AutoDisposeNotifierProvider<TbConversion, void>.internal(
  TbConversion.new,
  name: r'tbConversionProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$tbConversionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TbConversion = AutoDisposeNotifier<void>;
String _$jdConversionHash() => r'a0b3a0241f49634ff4978105f095d88c0c17868f';

/// See also [JdConversion].
@ProviderFor(JdConversion)
final jdConversionProvider =
    AutoDisposeNotifierProvider<JdConversion, JdChangeUrl?>.internal(
  JdConversion.new,
  name: r'jdConversionProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$jdConversionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$JdConversion = AutoDisposeNotifier<JdChangeUrl?>;
String _$pddConversionHash() => r'faa4f8429fbd53bd1d2c46850f02b21890d6d025';

/// See also [PddConversion].
@ProviderFor(PddConversion)
final pddConversionProvider =
    AutoDisposeNotifierProvider<PddConversion, PddChangeUrl?>.internal(
  PddConversion.new,
  name: r'pddConversionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pddConversionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PddConversion = AutoDisposeNotifier<PddChangeUrl?>;
String _$eleConversionHash() => r'4660484859b7e500c9562589210cd5ac9cf79528';

/// See also [EleConversion].
@ProviderFor(EleConversion)
final eleConversionProvider =
    AutoDisposeNotifierProvider<EleConversion, EleActivity?>.internal(
  EleConversion.new,
  name: r'eleConversionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$eleConversionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EleConversion = AutoDisposeNotifier<EleActivity?>;
String _$mtConversionHash() => r'6f3f1eb93d06dc6bc24196702ea6d9c833dbe8e8';

/// See also [MtConversion].
@ProviderFor(MtConversion)
final mtConversionProvider =
    AutoDisposeNotifierProvider<MtConversion, String?>.internal(
  MtConversion.new,
  name: r'mtConversionProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$mtConversionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MtConversion = AutoDisposeNotifier<String?>;
String _$wphConversionHash() => r'b9d3ebfd092f06815ae94d46129db4d0807cbda9';

/// See also [WphConversion].
@ProviderFor(WphConversion)
final wphConversionProvider =
    AutoDisposeNotifierProvider<WphConversion, WphChangeGoods?>.internal(
  WphConversion.new,
  name: r'wphConversionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$wphConversionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WphConversion = AutoDisposeNotifier<WphChangeGoods?>;
String _$dyConversionHash() => r'6fcb59326ea101bff8f2d971b25146256bb496f3';

/// See also [DyConversion].
@ProviderFor(DyConversion)
final dyConversionProvider =
    AutoDisposeNotifierProvider<DyConversion, DyChangeGoods?>.internal(
  DyConversion.new,
  name: r'dyConversionProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$dyConversionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DyConversion = AutoDisposeNotifier<DyChangeGoods?>;
String _$launchAppHash() => r'a9a99892fafa2bdaa3230b4f88731fa469138de0';

/// 通过schemaUrl打开app或者打开h5链接
///
/// Copied from [LaunchApp].
@ProviderFor(LaunchApp)
final launchAppProvider = AutoDisposeNotifierProvider<LaunchApp, void>.internal(
  LaunchApp.new,
  name: r'launchAppProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$launchAppHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LaunchApp = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
