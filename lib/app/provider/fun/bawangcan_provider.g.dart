// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bawangcan_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$baWangCanListHash() => r'8a9d8fd580e8d519f16e7e590ba752dd31966477';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$BaWangCanList
    extends BuildlessAutoDisposeAsyncNotifier<List<FreeLunchDetail>> {
  late final FunTabConfig mainTabConfig;
  late final FunTabConfig childTabConfig;
  late final double? latitude;
  late final double? longitude;
  late final FunSortOption? sortOption;

  FutureOr<List<FreeLunchDetail>> build(
    FunTabConfig mainTabConfig,
    FunTabConfig childTabConfig, {
    double? latitude,
    double? longitude,
    FunSortOption? sortOption,
  });
}

/// See also [BaWangCanList].
@ProviderFor(BaWangCanList)
const baWangCanListProvider = BaWangCanListFamily();

/// See also [BaWangCanList].
class BaWangCanListFamily extends Family<AsyncValue<List<FreeLunchDetail>>> {
  /// See also [BaWangCanList].
  const BaWangCanListFamily();

  /// See also [BaWangCanList].
  BaWangCanListProvider call(
    FunTabConfig mainTabConfig,
    FunTabConfig childTabConfig, {
    double? latitude,
    double? longitude,
    FunSortOption? sortOption,
  }) {
    return BaWangCanListProvider(
      mainTabConfig,
      childTabConfig,
      latitude: latitude,
      longitude: longitude,
      sortOption: sortOption,
    );
  }

  @override
  BaWangCanListProvider getProviderOverride(
    covariant BaWangCanListProvider provider,
  ) {
    return call(
      provider.mainTabConfig,
      provider.childTabConfig,
      latitude: provider.latitude,
      longitude: provider.longitude,
      sortOption: provider.sortOption,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'baWangCanListProvider';
}

/// See also [BaWangCanList].
class BaWangCanListProvider extends AutoDisposeAsyncNotifierProviderImpl<
    BaWangCanList, List<FreeLunchDetail>> {
  /// See also [BaWangCanList].
  BaWangCanListProvider(
    FunTabConfig mainTabConfig,
    FunTabConfig childTabConfig, {
    double? latitude,
    double? longitude,
    FunSortOption? sortOption,
  }) : this._internal(
          () => BaWangCanList()
            ..mainTabConfig = mainTabConfig
            ..childTabConfig = childTabConfig
            ..latitude = latitude
            ..longitude = longitude
            ..sortOption = sortOption,
          from: baWangCanListProvider,
          name: r'baWangCanListProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$baWangCanListHash,
          dependencies: BaWangCanListFamily._dependencies,
          allTransitiveDependencies:
              BaWangCanListFamily._allTransitiveDependencies,
          mainTabConfig: mainTabConfig,
          childTabConfig: childTabConfig,
          latitude: latitude,
          longitude: longitude,
          sortOption: sortOption,
        );

  BaWangCanListProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.mainTabConfig,
    required this.childTabConfig,
    required this.latitude,
    required this.longitude,
    required this.sortOption,
  }) : super.internal();

  final FunTabConfig mainTabConfig;
  final FunTabConfig childTabConfig;
  final double? latitude;
  final double? longitude;
  final FunSortOption? sortOption;

  @override
  FutureOr<List<FreeLunchDetail>> runNotifierBuild(
    covariant BaWangCanList notifier,
  ) {
    return notifier.build(
      mainTabConfig,
      childTabConfig,
      latitude: latitude,
      longitude: longitude,
      sortOption: sortOption,
    );
  }

  @override
  Override overrideWith(BaWangCanList Function() create) {
    return ProviderOverride(
      origin: this,
      override: BaWangCanListProvider._internal(
        () => create()
          ..mainTabConfig = mainTabConfig
          ..childTabConfig = childTabConfig
          ..latitude = latitude
          ..longitude = longitude
          ..sortOption = sortOption,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        mainTabConfig: mainTabConfig,
        childTabConfig: childTabConfig,
        latitude: latitude,
        longitude: longitude,
        sortOption: sortOption,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<BaWangCanList, List<FreeLunchDetail>>
      createElement() {
    return _BaWangCanListProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BaWangCanListProvider &&
        other.mainTabConfig == mainTabConfig &&
        other.childTabConfig == childTabConfig &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.sortOption == sortOption;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mainTabConfig.hashCode);
    hash = _SystemHash.combine(hash, childTabConfig.hashCode);
    hash = _SystemHash.combine(hash, latitude.hashCode);
    hash = _SystemHash.combine(hash, longitude.hashCode);
    hash = _SystemHash.combine(hash, sortOption.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin BaWangCanListRef
    on AutoDisposeAsyncNotifierProviderRef<List<FreeLunchDetail>> {
  /// The parameter `mainTabConfig` of this provider.
  FunTabConfig get mainTabConfig;

  /// The parameter `childTabConfig` of this provider.
  FunTabConfig get childTabConfig;

  /// The parameter `latitude` of this provider.
  double? get latitude;

  /// The parameter `longitude` of this provider.
  double? get longitude;

  /// The parameter `sortOption` of this provider.
  FunSortOption? get sortOption;
}

class _BaWangCanListProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<BaWangCanList,
        List<FreeLunchDetail>> with BaWangCanListRef {
  _BaWangCanListProviderElement(super.provider);

  @override
  FunTabConfig get mainTabConfig =>
      (origin as BaWangCanListProvider).mainTabConfig;
  @override
  FunTabConfig get childTabConfig =>
      (origin as BaWangCanListProvider).childTabConfig;
  @override
  double? get latitude => (origin as BaWangCanListProvider).latitude;
  @override
  double? get longitude => (origin as BaWangCanListProvider).longitude;
  @override
  FunSortOption? get sortOption => (origin as BaWangCanListProvider).sortOption;
}

String _$bawangcanRulesExpandedHash() =>
    r'5f7d2d819e4a135219473442a429fbac64b9c83d';

/// 霸王餐规则展开状态管理
///
/// Copied from [BawangcanRulesExpanded].
@ProviderFor(BawangcanRulesExpanded)
final bawangcanRulesExpandedProvider =
    AutoDisposeNotifierProvider<BawangcanRulesExpanded, bool>.internal(
  BawangcanRulesExpanded.new,
  name: r'bawangcanRulesExpandedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bawangcanRulesExpandedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BawangcanRulesExpanded = AutoDisposeNotifier<bool>;
String _$bawangcanActivityManageHash() =>
    r'8e7abe941881ad333196636ff4e43a2184f736f8';

/// 霸王餐活动报名状态管理
///
/// Copied from [BawangcanActivityManage].
@ProviderFor(BawangcanActivityManage)
final bawangcanActivityManageProvider = AutoDisposeNotifierProvider<
    BawangcanActivityManage, BawangcanApplyState>.internal(
  BawangcanActivityManage.new,
  name: r'bawangcanActivityManageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bawangcanActivityManageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BawangcanActivityManage = AutoDisposeNotifier<BawangcanApplyState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
