// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mall_collection_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchListPromotionHash() =>
    r'023f48b74ec58f28c6a3ad8261d6caff6ca53c08';

/// 商城收藏数据 Provider
///
/// Copied from [fetchListPromotion].
@ProviderFor(fetchListPromotion)
final fetchListPromotionProvider =
    AutoDisposeFutureProvider<List<FunPromotionCollection>>.internal(
  fetchListPromotion,
  name: r'fetchListPromotionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchListPromotionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchListPromotionRef
    = AutoDisposeFutureProviderRef<List<FunPromotionCollection>>;
String _$mallCollectionTopMenuDataHash() =>
    r'86361dc287419019c8c227aec4a0ce907ae16685';

/// 顶部菜单数据
///
/// Copied from [mallCollectionTopMenuData].
@ProviderFor(mallCollectionTopMenuData)
final mallCollectionTopMenuDataProvider =
    AutoDisposeProvider<List<FunPromotionCollection>>.internal(
  mallCollectionTopMenuData,
  name: r'mallCollectionTopMenuDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mallCollectionTopMenuDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef MallCollectionTopMenuDataRef
    = AutoDisposeProviderRef<List<FunPromotionCollection>>;
String _$mallCollectionContentDataHash() =>
    r'1475f378fdfcf48cd3fdb90cb3299042144a57fc';

/// 当前选中分类的商品数据 Provider（派生状态）
/// 展开的商城数据 Provider（将所有商品数据展开为扁平列表）
///
/// Copied from [mallCollectionContentData].
@ProviderFor(mallCollectionContentData)
final mallCollectionContentDataProvider =
    AutoDisposeProvider<List<MallCollectionContentItem>>.internal(
  mallCollectionContentData,
  name: r'mallCollectionContentDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mallCollectionContentDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef MallCollectionContentDataRef
    = AutoDisposeProviderRef<List<MallCollectionContentItem>>;
String _$mallCollectionSelectedIndexHash() =>
    r'e9abc6482056a23310ee4a1cc5b9e4c6676770b5';

/// 当前选中的索引 Provider
///
/// Copied from [MallCollectionSelectedIndex].
@ProviderFor(MallCollectionSelectedIndex)
final mallCollectionSelectedIndexProvider =
    AutoDisposeNotifierProvider<MallCollectionSelectedIndex, int>.internal(
  MallCollectionSelectedIndex.new,
  name: r'mallCollectionSelectedIndexProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mallCollectionSelectedIndexHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MallCollectionSelectedIndex = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
