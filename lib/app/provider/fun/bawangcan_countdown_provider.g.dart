// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bawangcan_countdown_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bawangcanCountdownHash() =>
    r'faf365eb87e9cec1963f2bb3a7149626f04880a5';

/// 霸王餐倒计时状态管理
///
/// Copied from [BawangcanCountdown].
@ProviderFor(BawangcanCountdown)
final bawangcanCountdownProvider = AutoDisposeNotifierProvider<
    BawangcanCountdown, BawangcanCountdownState>.internal(
  BawangcanCountdown.new,
  name: r'bawangcanCountdownProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bawangcanCountdownHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BawangcanCountdown = AutoDisposeNotifier<BawangcanCountdownState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
