// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bawangcan_registration_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bawangcanRegistrationHash() =>
    r'3e3e2625ef327695395213784c8503f33c2eb080';

/// 霸王餐报名记录列表状态管理
///
/// Copied from [BawangcanRegistration].
@ProviderFor(BawangcanRegistration)
final bawangcanRegistrationProvider = AutoDisposeNotifierProvider<
    BawangcanRegistration, BawangcanRegistrationState>.internal(
  BawangcanRegistration.new,
  name: r'bawangcanRegistrationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bawangcanRegistrationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BawangcanRegistration
    = AutoDisposeNotifier<BawangcanRegistrationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
