import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:location/location.dart';
import 'package:msmds_platform/app/provider/conversion/link_conversion_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../common/http/api_response.dart';
import '../../repository/modals/fun/fun_meituan_response.dart';
import '../../repository/modals/fun/fun_tab_config.dart';
import '../../repository/service/fun_service.dart';

part 'fun_list_provider.g.dart';

@riverpod
Future<ApiResponse<MeituanCouponResponse?>> fetchMeituanCouponList(
  Ref ref,
  FunTabConfig mainTabConfig,
  FunTabConfig childTabConfig, {
  double? latitude,
  double? longitude,
  int pageNo = 1,
  int pageSize = 20,
  FunSortOption? sortOption,
}) async {
  try {
    final mainTabCode = mainTabConfig.code ?? '';
    if (mainTabCode != "meituan_groupBuying" &&
        mainTabCode != "meituan_sharpshooter") {
      return ApiResponse.completed(null);
    }

    final params = MeituanCouponParams(
      latitude: latitude ?? 0,
      longitude: longitude ?? 0,
      listTopiId: childTabConfig.listTopiId ?? 0,
      platform: childTabConfig.platform ?? 0,
      pageNo: pageNo,
      pageSize: pageSize,
      sortField: sortOption?.sortField,
      ascDescOrder: sortOption?.ascDescOrder,
      bizLine: childTabConfig.bizLine,
    );

    final response = await FunService.fetchMeituanCoupons(params);
    return response;
  } catch (e) {
    return ApiResponse.error(null);
  }
}

@riverpod
Future<FunTabResult?> fetchFunMainTabConfig(Ref ref, String code) async {
  final response = await FunService.fetchTabConfig(code);
  if (response.status == Status.completed) {
    return response.data;
  }
  return null;
}

@riverpod
Future<FunTabResult?> fetchFunChildrenTabConfig(Ref ref, String code) async {
  final response = await FunService.fetchTabConfig(code);
  if (response.status == Status.completed) {
    return response.data;
  }
  return null;
}

@riverpod
class MeituanShareLink extends _$MeituanShareLink {
  @override
  Future<String> build(MeituanCouponItem item) async {
    SmartDialog.showLoading(msg: "加载中...");
    final response = await FunService.fetchMeituanShareLink(item);
    SmartDialog.dismiss();

    if (response.status == Status.completed) {
      return response.data;
    }
    return "";
  }
}

@riverpod
class FunItemClick extends _$FunItemClick {
  @override
  void build() {
    return;
  }

  void meituanGetCoupon(MeituanCouponItem item) async {
    SmartDialog.showLoading(msg: "跳转中...");

    /// 判断能否打开美团
    bool canLaunchMt = await canLaunchUrl(Uri.parse("imeituan://"));
    try {
      var result =
          await FunService.fetchMeituanGetCoupon(item, canLaunchMt ? 3 : 2);
      SmartDialog.dismiss();

      if (result.status == Status.completed && result.data != null) {
        debugPrint("changeUrlWithMtActivity: $result");

        /// 跳转美团或者h5
        if (result.data != null) {
          String schemaUrl = canLaunchMt ? result.data! : "";
          String h5Url = canLaunchMt ? "" : result.data!;
          ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
        }
      } else {
        // 如果请求失败，显示错误提示
        SmartDialog.showToast('获取优惠券失败，请稍后重试');
      }
    } catch (e) {
      SmartDialog.dismiss();
      SmartDialog.showToast('跳转失败：${e.toString()}');
      debugPrint('美团优惠券跳转失败: $e');
    }
  }
}

@riverpod
class ManageFunTabConfig extends _$ManageFunTabConfig {
  late TabStateConfig? _currentTabConfig;

  @override
  Future<TabStateConfig?> build() async {
    final mainTab = await FunService.fetchTabConfig("free_lunch_meituan_tab");
    if (mainTab.status == Status.error) {
      return null;
    }
    final childrenTab =
        await FunService.fetchTabConfig(mainTab.data!.tabs.first.code ?? "");

    _currentTabConfig = TabStateConfig(
        mainTab: mainTab.data!,
        childrenTab: childrenTab.data!,
        currentSort: childrenTab.data?.tabs.first.sort?.first);
    return _currentTabConfig;
  }

  Future<bool> getChildrenTab(String code) async {
    final childrenTab = await FunService.fetchTabConfig(code);
    if (childrenTab.status == Status.completed) {
      if (_currentTabConfig != null && childrenTab.data != null) {
        _currentTabConfig!.childrenTab = childrenTab.data!;
        _currentTabConfig!.currentSort =
            childrenTab.data?.tabs.first.sort?.first;

        state = AsyncData(_currentTabConfig!);
        return true; // 成功
      }
    }
    return false; // 失败
  }

  // Function to handle click event on the main tab
  void onMainTabClick(String mainTabCode) async {
    final mainTab = await FunService.fetchTabConfig(mainTabCode);
    if (mainTab.status == Status.error) {
      return;
    }

    // Update the main tab and fetch the children tab
    final childrenTab =
        await FunService.fetchTabConfig(mainTab.data!.tabs.first.code ?? "");
    _currentTabConfig =
        TabStateConfig(mainTab: mainTab.data!, childrenTab: childrenTab.data!);

    // Update the state with the new TabConfig
    state = AsyncData(_currentTabConfig!);
  }

  setCurrentSort(FunSortOption sortOption) {
    if (_currentTabConfig != null) {
      _currentTabConfig!.currentSort = sortOption;
      state = AsyncData(_currentTabConfig!);
    }
  }
}

@riverpod
class TabSelectionState extends _$TabSelectionState {
  @override
  TabSelection build() {
    return const TabSelection();
  }

  void setMainTabIndex(int index) {
    state = state.copyWith(
      mainTabIndex: index,
      childTabIndex: 0,
      childTabChildIndex: -1,
    );
  }

  void setChildTabIndex(int index) {
    state = state.copyWith(
      childTabIndex: index,
      childTabChildIndex: -1,
    );
  }

  void setChildTabChildIndex(int index) {
    state = state.copyWith(childTabChildIndex: index);
  }
}

class TabSelection {
  final int mainTabIndex;
  final int childTabIndex;
  final int childTabChildIndex;

  const TabSelection({
    this.mainTabIndex = 0,
    this.childTabIndex = 0,
    this.childTabChildIndex = -1,
  });

  TabSelection copyWith({
    int? mainTabIndex,
    int? childTabIndex,
    int? childTabChildIndex,
  }) {
    return TabSelection(
      mainTabIndex: mainTabIndex ?? this.mainTabIndex,
      childTabIndex: childTabIndex ?? this.childTabIndex,
      childTabChildIndex: childTabChildIndex ?? this.childTabChildIndex,
    );
  }
}

class TabStateConfig {
  FunTabResult mainTab;
  FunTabResult childrenTab;
  FunSortOption? currentSort;

  TabStateConfig({
    required this.mainTab,
    required this.childrenTab,
    this.currentSort = const FunSortOption(),
  });
}

/// 获取当前选中的主标签页代码
@riverpod
String? currentMainTabCode(Ref ref) {
  final tabState = ref.watch(manageFunTabConfigProvider);
  final tabSelection = ref.watch(tabSelectionStateProvider);

  return tabState.when(
    data: (data) {
      if (data != null &&
          tabSelection.mainTabIndex < data.mainTab.tabs.length) {
        return data.mainTab.tabs[tabSelection.mainTabIndex].code;
      }
      return null;
    },
    loading: () => null,
    error: (_, __) => null,
  );
}

/// 位置状态数据类
class LocationState {
  final LocationData? locationData;
  final String locationName;
  final bool isLoading;
  final String? errorMessage;

  const LocationState({
    this.locationData,
    this.locationName = '附近',
    this.isLoading = false,
    this.errorMessage,
  });

  LocationState copyWith({
    LocationData? locationData,
    String? locationName,
    bool? isLoading,
    String? errorMessage,
  }) {
    return LocationState(
      locationData: locationData ?? this.locationData,
      locationName: locationName ?? this.locationName,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// 获取纬度
  double? get latitude => locationData?.latitude;

  /// 获取经度
  double? get longitude => locationData?.longitude;

  /// 是否有位置数据
  bool get hasLocation => locationData != null;

  /// 是否有错误
  bool get hasError => errorMessage != null;

  /// 是否是权限错误
  bool get isPermissionError =>
      errorMessage != null &&
      (errorMessage!.contains('权限') || errorMessage!.contains('未开启'));
}

/// 位置管理 Provider
@riverpod
class LocationManager extends _$LocationManager {
  final Location _location = Location();

  @override
  LocationState build() {
    // 初始化时自动获取位置
    Future.microtask(() => getCurrentLocation());
    return const LocationState();
  }

  /// 获取当前位置
  Future<void> getCurrentLocation({bool showLoading = false}) async {
    try {
      if (showLoading) {
        state = state.copyWith(isLoading: true, errorMessage: null);
      }

      // 检查位置服务是否启用
      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          state = state.copyWith(
            isLoading: false,
            errorMessage: '位置服务未开启，请在设置中开启',
          );
          return;
        }
      }
      debugPrint("开始检查位置权限");

      // 检查位置权限
      PermissionStatus permissionGranted = await _location.hasPermission();
      if (permissionGranted == PermissionStatus.denied) {
        permissionGranted = await _location.requestPermission();
        if (permissionGranted != PermissionStatus.granted) {
          state = state.copyWith(
            isLoading: false,
            errorMessage: '位置权限未授予',
            locationName: '获取位置',
          );
          return;
        }
      }
      debugPrint("开始获取位置");

      // 获取当前位置
      final locationData = await _location.getLocation();

      debugPrint("获取位置成功${locationData.latitude} ${locationData.longitude}");

      state = state.copyWith(
        locationData: locationData,
        locationName: '附近', // 可以根据经纬度调用逆地理编码获取具体地址
        isLoading: false,
        errorMessage: null,
      );

      // 打印位置信息
    } catch (e) {
      debugPrint('获取位置失败: $e');
      state = state.copyWith(
        isLoading: false,
        errorMessage: '获取位置失败: $e',
      );
    }
  }

  /// 刷新位置
  Future<void> refreshLocation() async {
    await getCurrentLocation(showLoading: true);
  }
}
