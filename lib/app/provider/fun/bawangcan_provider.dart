import 'package:flutter/cupertino.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../common/http/api_response.dart';
import '../../repository/modals/fun/fun_free_lunch_response.dart';
import '../../repository/modals/fun/fun_tab_config.dart';
import '../../repository/modals/fun/bwc_apply_status.dart';
import '../../repository/service/fun_service.dart';
import '../../../utils/toast_util.dart';

part 'bawangcan_provider.g.dart';

@riverpod
class BaWangCanList extends _$BaWangCanList {
  String? _mtPageId;
  String? _pageId;
  String? _meituanPvId;
  bool _hasMore = true;
  bool _isLoadingMore = false;

  @override
  Future<List<FreeLunchDetail>> build(
    FunTabConfig mainTabConfig,
    FunTabConfig childTabConfig, {
    double? latitude,
    double? longitude,
    FunSortOption? sortOption,
  }) async {
    final mainTabCode = mainTabConfig.code ?? '';
    if (mainTabCode != "bawangcan_sort") {
      return [];
    }

    // 重置分页参数
    _mtPageId = null;
    _pageId = null;
    _meituanPvId = null;
    _hasMore = true;
    _isLoadingMore = false;

    return await _fetchData(
      mainTabConfig: mainTabConfig,
      childTabConfig: childTabConfig,
      latitude: latitude,
      longitude: longitude,
      sortOption: sortOption,
      isLoadMore: false,
    );
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();
    _mtPageId = null;
    _pageId = null;
    _hasMore = true;
    state = await AsyncValue.guard(() => build(
          mainTabConfig,
          childTabConfig,
          latitude: latitude,
          longitude: longitude,
          sortOption: sortOption,
        ));
  }

  Future<void> loadMore() async {
    if (_isLoadingMore || !_hasMore) return;
    _isLoadingMore = true;
    state = AsyncValue.data(state.value ?? []);
    try {
      final currentData = state.value ?? [];
      final newItems = await _fetchData(
        mainTabConfig: mainTabConfig,
        childTabConfig: childTabConfig,
        latitude: latitude,
        longitude: longitude,
        sortOption: sortOption,
        isLoadMore: true,
      );

      if (newItems.isNotEmpty) {
        state = AsyncValue.data([...currentData, ...newItems]);
      }
    } catch (e) {
      debugPrint('Load more error: $e');
      ToastUtil.showErrorToast('加载更多失败');
    } finally {
      _isLoadingMore = false;
      state = AsyncValue.data(state.value ?? []);
    }
  }

  bool get hasMore => _hasMore;
  bool get isLoadingMore => _isLoadingMore;
  String? get meituanPvId => _meituanPvId;

  Future<List<FreeLunchDetail>> _fetchData({
    required FunTabConfig mainTabConfig,
    required FunTabConfig childTabConfig,
    double? latitude,
    double? longitude,
    FunSortOption? sortOption,
    required bool isLoadMore,
  }) async {
    try {
      final params = FreeLunchParams(
        lat: latitude ?? 0,
        lng: longitude ?? 0,
        sort: sortOption?.sortField ?? 0,
        mtPageId: isLoadMore ? _mtPageId : null,
        pageId: isLoadMore ? _pageId : null,
      );

      debugPrint("BaWangCan params: $params");

      ApiResponse<FreeLunchResponse> response;
      final childTabName = childTabConfig.tabName ?? '';

      if (childTabName == '美团专版') {
        response = await FunService.fetchMtActivityList(params);
      } else if (childTabName == '饿了么专版') {
        response = await FunService.fetchElmActivityList(params);
      } else {
        response = await FunService.fetchFreeLunchList(params);
      }

      if (response.status == Status.completed) {
        final data = response.data;
        if (data != null) {
          // 更新分页信息
          _meituanPvId = data.meituanPvId ?? _meituanPvId;
          if (childTabName == '美团专版' || childTabName == '饿了么专版') {
            _pageId = data.pageInfo?.elmPageId ?? data.pageId;
          } else {
            _mtPageId = data.pageInfo?.mtPageId;
          }

          final details = data.details ?? [];
          _hasMore =
              details.isNotEmpty && (_mtPageId != null || _pageId != null);

          return details;
        }
      } else if (response.status == Status.error) {
        throw response.exception ?? Exception('请求失败');
      }

      _hasMore = false;
      return [];
    } catch (e) {
      debugPrint('BaWangCan fetch error: $e');
      _hasMore = false;
      rethrow;
    }
  }
}


/// 霸王餐规则展开状态管理
@riverpod
class BawangcanRulesExpanded extends _$BawangcanRulesExpanded {
  @override
  bool build() {
    return false; // 默认折叠状态
  }

  /// 切换展开/折叠状态
  void toggle() {
    state = !state;
  }

  /// 设置展开状态
  void setExpanded(bool expanded) {
    state = expanded;
  }
}

/// 霸王餐活动报名状态管理
@riverpod
class BawangcanActivityManage extends _$BawangcanActivityManage {
  /// 当前报名状态数据
  BwcApplyStatus? _applyStatus;
  
  /// 当前商铺数据
  FreeLunchDetail? _currentGoodsData;
  
  /// 当前活动列表
  List<FreeLunchActivity>? _activityList;

  @override
  BawangcanApplyState build() {
    return const BawangcanApplyState(); // 默认选中第一个活动
  }

  /// 获取当前报名状态
  BwcApplyStatus? get applyStatus => _applyStatus;

  /// 获取当前商铺数据
  FreeLunchDetail? get currentGoodsData => _currentGoodsData;

  /// 获取当前活动列表
  List<FreeLunchActivity>? get activityList => _activityList;

  /// 设置选中的活动索引
  void setIndex(int index) {
    state = state.copyWith(selectedActivityIndex: index);
  }

  /// 重置到第一个活动
  void reset() {
    state = state.copyWith(selectedActivityIndex: 0);
    _applyStatus = null;
    _currentGoodsData = null;
    _activityList = null;
  }

  /// 初始化数据
  void initializeData(FreeLunchDetail goodsData) {
    _currentGoodsData = goodsData;
    _activityList = goodsData.activityList;
  }

  /// 查询所有活动的报名状态
  Future<void> checkAllActivitiesApplyStatus() async {
    final activityList = _activityList;
    if (activityList == null || activityList.isEmpty) {
      return;
    }

    // 遍历所有活动,查询报名状态
    for (int i = 0; i < activityList.length; i++) {
      final activity = activityList[i];
      if (activity.activityId != null) {
        await checkApplyStatus(
          activityId: activity.activityId!,
          activityIndex: i,
        );

        // 如果找到已报名的活动,停止查询
        if (_applyStatus != null) {
          break;
        }
      }
    }
  }

  /// 查询店铺是否有报名
  /// 参数:
  /// - activityId: 活动ID
  /// - activityIndex: 活动索引
  /// - updateState: 是否更新状态(默认 false)
  Future<void> checkApplyStatus({
    required String activityId,
    required int activityIndex,
    bool updateState = false,
  }) async {
    final shopId = _currentGoodsData?.shopId;
    if (shopId == null) {
      return;
    }

    try {
      final response = await FunService.checkApplyStatus(
        shopId: shopId,
        activityId: activityId,
      );

      if (response.status == Status.completed) {
        final applyStatus = response.data;

        if (updateState) {
          _applyStatus = applyStatus;

          state = state.copyWith(selectedActivityIndex: activityIndex);
        } else if (applyStatus != null) {
          // 首次查询时,如果找到报名记录就设置状态
          _applyStatus = applyStatus;
          state = state.copyWith(selectedActivityIndex: activityIndex);
        }
      }
    } catch (e) {
      debugPrint('查询报名状态失败: $e');
    }
  }

  /// 倒计时结束时的回调
  void handleCountdownFinish() {
    _applyStatus = null;

    final activityList = _activityList ?? [];
    final selectedIndex = state.selectedActivityIndex;
    if (selectedIndex >= activityList.length) {
      return;
    }

    final activityId = activityList[selectedIndex].activityId;
    if (activityId == null || activityId.isEmpty) {
      return;
    }

    checkApplyStatus(
      activityId: activityId,
      activityIndex: selectedIndex,
      updateState: true,
    );
  }

  /// 美团活动报名
  Future<bool> mtApply({
    required String phone,
    required FreeLunchDetail goodsData,
    required FreeLunchActivity selectedActivity,
    required double latitude,
    required double longitude,
    String? meituanPvId,
  }) async {
    try {
      // 构建 detail 参数
      final detail = {
        'actionUrl': goodsData.actionUrl?.toJson(),
        'activity': selectedActivity.toJson(),
        'name': goodsData.name,
        'picture': goodsData.picture,
        'shopId': goodsData.shopId,
      };

      final response = await FunService.mtApply(
        activityId: selectedActivity.activityId ?? '',
        lat: latitude,
        lng: longitude,
        mobile: phone,
        detail: detail,
        meituanPvId: meituanPvId,
      );

      if (response.status == Status.completed && response.data == true) {
        // 报名成功，延迟300ms后刷新报名状态
        await Future.delayed(const Duration(milliseconds: 300));
        await checkApplyStatus(
          activityId: selectedActivity.activityId ?? '',
          activityIndex: state.selectedActivityIndex,
          updateState: true,
        );
        return true;
      } else {
        throw Exception(response.exception?.getMessage() ?? '报名失败');
      }
    } catch (e) {
      debugPrint('美团报名失败: $e');
      rethrow;
    }
  }

  /// 饿了么活动报名
  Future<bool> elmApply({
    required String phone,
    required FreeLunchDetail goodsData,
    required FreeLunchActivity selectedActivity,
  }) async {
    try {
      // 1. 先获取活动转链
      final actionUrlResponse = await FunService.getActionUrl(
        activityId: selectedActivity.activityId ?? '',
        shopId: goodsData.shopId ?? '',
      );

      if (actionUrlResponse.status != Status.completed ||
          actionUrlResponse.data == null) {
        throw Exception('获取活动链接失败');
      }

      // 2. 调用饿了么报名接口
      final detail = {
        'actionUrl': actionUrlResponse.data,
        'activity': selectedActivity.toJson(),
        'name': goodsData.name,
        'picture': goodsData.picture,
        'shopId': goodsData.shopId,
      };

      final response = await FunService.elmActivityApply(
        activityId: selectedActivity.activityId ?? '',
        mobile: phone,
        detail: detail,
      );

      if (response.status == Status.completed && response.data == true) {
        // 报名成功，延迟300ms后刷新报名状态
        await Future.delayed(const Duration(milliseconds: 300));
        await checkApplyStatus(
          activityId: selectedActivity.activityId ?? '',
          activityIndex: state.selectedActivityIndex,
          updateState: true,
        );
        return true;
      } else {
        throw Exception(response.exception?.getMessage() ?? '报名失败');
      }
    } catch (e) {
      debugPrint('饿了么报名失败: $e');
      rethrow;
    }
  }

  /// 通用报名接口调用
  Future<bool> applyActivity({
    required String phone,
    required FreeLunchDetail goodsData,
    required FreeLunchActivity selectedActivity,
    required double latitude,
    required double longitude,
    String? meituanPvId,
  }) async {
    // 根据平台类型调用不同的报名接口
    if (goodsData.type == 1) {
      // 美团报名
      return await mtApply(
        phone: phone,
        goodsData: goodsData,
        selectedActivity: selectedActivity,
        latitude: latitude,
        longitude: longitude,
        meituanPvId: meituanPvId,
      );
    } else {
      // 饿了么报名
      return await elmApply(
        phone: phone,
        goodsData: goodsData,
        selectedActivity: selectedActivity,
      );
    }
  }

  /// 清除报名状态
  void clearApplyStatus() {
    _applyStatus = null;
  }
}

/// 霸王餐报名状态数据模型
class BawangcanApplyState {
  /// 当前报名状态数据
  final BwcApplyStatus? applyStatus;

  /// 当前选中的活动索引
  final int selectedActivityIndex;

  const BawangcanApplyState({
    this.applyStatus,
    this.selectedActivityIndex = 0,
  });

  /// 复制并更新状态
  BawangcanApplyState copyWith({
    BwcApplyStatus? applyStatus,
    int? selectedActivityIndex,
    bool clearApplyStatus = false,
  }) {
    return BawangcanApplyState(
      applyStatus: clearApplyStatus ? null : (applyStatus ?? this.applyStatus),
      selectedActivityIndex: selectedActivityIndex ?? this.selectedActivityIndex,
    );
  }
}