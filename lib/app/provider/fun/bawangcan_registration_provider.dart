import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/fun/bawangcan_countdown_provider.dart';
import 'package:msmds_platform/app/repository/modals/fun/bawangcan_registration_record.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_promotion.dart';
import 'package:msmds_platform/app/repository/service/fun_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/config/global_config.dart';
import 'package:msmds_platform/utils/smart_jump_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'bawangcan_registration_provider.g.dart';

/// 霸王餐报名记录列表状态
class BawangcanRegistrationState {
  /// 当前选中的标签页索引
  final int selectedTabIndex;

  /// 报名记录列表
  final List<BawangcanRegistrationRecord> records;

  /// 是否正在加载
  final bool isLoading;

  /// 是否正在刷新
  final bool isRefreshing;

  /// 是否正在加载更多
  final bool isLoadingMore;

  /// 是否有更多数据
  final bool hasMore;

  /// 当前页码
  final int currentPage;

  /// 错误信息
  final String? errorMessage;

  const BawangcanRegistrationState({
    this.selectedTabIndex = 0,
    this.records = const [],
    this.isLoading = false,
    this.isRefreshing = false,
    this.isLoadingMore = false,
    this.hasMore = true,
    this.currentPage = 1,
    this.errorMessage,
  });

  BawangcanRegistrationState copyWith({
    int? selectedTabIndex,
    List<BawangcanRegistrationRecord>? records,
    bool? isLoading,
    bool? isRefreshing,
    bool? isLoadingMore,
    bool? hasMore,
    int? currentPage,
    String? errorMessage,
  }) {
    return BawangcanRegistrationState(
      selectedTabIndex: selectedTabIndex ?? this.selectedTabIndex,
      records: records ?? this.records,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      errorMessage: errorMessage,
    );
  }
}

/// 霸王餐报名记录列表状态管理
@riverpod
class BawangcanRegistration extends _$BawangcanRegistration {
  static const int _pageSize = 10;

  @override
  BawangcanRegistrationState build() {
    return const BawangcanRegistrationState();
  }

  /// 切换标签页
  void selectTab(int index) {
    if (state.selectedTabIndex == index) return;

    state = state.copyWith(
      selectedTabIndex: index,
      records: [],
      currentPage: 1,
      hasMore: true,
      errorMessage: null,
    );

    // 加载新标签页的数据
    loadRecords();
  }

  /// 加载报名记录列表
  Future<void> loadRecords() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final records = await _fetchRecords(1);
      state = state.copyWith(
        records: records.list,
        hasMore: records.hasMore,
        currentPage: 1,
        isLoading: false,
      );
    } catch (e) {
      debugPrint('loadRecords error: $e');
      final errorMsg = '加载失败: ${e.toString()}';
      state = state.copyWith(
        isLoading: false,
        errorMessage: errorMsg,
      );
      SmartDialog.showToast(errorMsg);
    }
  }

  /// 刷新数据
  Future<void> refreshRecords() async {
    if (state.isRefreshing) return;

    state = state.copyWith(isRefreshing: true, errorMessage: null);

    try {
      final records = await _fetchRecords(1);
      state = state.copyWith(
        records: records.list,
        hasMore: records.hasMore,
        currentPage: 1,
        isRefreshing: false,
      );
    } catch (e) {
      debugPrint('refreshRecords error: $e');
      final errorMsg = '刷新失败: ${e.toString()}';
      state = state.copyWith(
        isRefreshing: false,
        errorMessage: errorMsg,
      );
      SmartDialog.showToast(errorMsg);
    }
  }

  /// 加载更多数据
  Future<void> loadMoreRecords() async {
    if (state.isLoadingMore || !state.hasMore) return;

    state = state.copyWith(isLoadingMore: true, errorMessage: null);

    try {
      final nextPage = state.currentPage + 1;
      final records = await _fetchRecords(nextPage);
      final newRecords = [...state.records, ...records.list];

      state = state.copyWith(
        records: newRecords,
        hasMore: records.hasMore,
        currentPage: nextPage,
        isLoadingMore: false,
      );
    } catch (e) {
      debugPrint('loadMoreRecords error: $e');
      final errorMsg = '加载更多失败: ${e.toString()}';
      state = state.copyWith(
        isLoadingMore: false,
        errorMessage: errorMsg,
      );
      SmartDialog.showToast(errorMsg);
    }
  }

  /// 获取饿了么活动转链（用于"前往下单"）
  ///
  /// 参数:
  /// - activityId: 活动ID
  /// - shopId: 商铺ID
  ///
  /// 返回: 包含 wxPath 等字段的 Map，失败时返回 null
  Future<Map<String, dynamic>?> getElmActionUrl({
    required String activityId,
    required String shopId,
  }) async {
    try {
      final response = await FunService.getActionUrl(
        activityId: activityId,
        shopId: shopId,
      );

      if (response.status == Status.completed && response.data != null) {
        return response.data;
      } else {
        final errorMsg = response.exception?.getMessage() ?? '获取下单链接失败';
        debugPrint('getElmActionUrl failed: $errorMsg');
        return null;
      }
    } catch (e) {
      debugPrint('getElmActionUrl error: $e');
      return null;
    }
  }

  /// 取消报名
  Future<bool> cancelRegistration(BawangcanRegistrationRecord record) async {
    if (record.applyId == null ||
        record.applyOrderShop?.applyType == null ||
        record.mobile == null) {
      SmartDialog.showToast('报名信息不完整');
      return false;
    }

    try {
      final applyType = record.applyOrderShop!.applyType!;
      SmartDialog.showLoading(msg: '取消中...');

      final ApiResponse<bool> response;
      if (applyType == 1) {
        // 美团
        response = await FunService.cancelMtActivity(
          applyId: record.applyId!,
          mobile: record.mobile!,
        );
      } else {
        // 饿了么
        response = await FunService.cancelElmActivity(
          applyId: record.applyId!,
          mobile: record.mobile!,
        );
      }

      SmartDialog.dismiss();

      if (response.status == Status.completed && response.data == true) {
        SmartDialog.showToast('取消成功');
        // 刷新列表
        await refreshRecords();
        // 取消成功，刷新报名提醒倒计时（可能切换到下一条记录）
        await ref.read(bawangcanCountdownProvider.notifier).refreshCountdown();
        return true;
      } else {
        final errorMsg = response.exception?.getMessage() ?? '取消失败';
        SmartDialog.showToast(errorMsg);
        debugPrint('cancelRegistration failed: $errorMsg');
        return false;
      }
    } catch (e) {
      SmartDialog.dismiss();
      debugPrint('cancelRegistration error: $e');
      SmartDialog.showToast('取消失败: ${e.toString()}');
      return false;
    }
  }

  /// 获取记录列表的通用方法
  Future<_RecordsResult> _fetchRecords(int pageNum) async {
    final selectedTab = bawangcanRegistrationRecordTabs[state.selectedTabIndex];
    final params = BawangcanRegistrationRecordParams(
      pageNum: pageNum,
      pageSize: _pageSize,
      state: selectedTab.state,
    );

    final response = await FunService.getApplyOrderList(params);

    if (response.status == Status.completed && response.data != null) {
      final data = response.data!;
      return _RecordsResult(
        list: data.list ?? [],
        hasMore: data.hasNextPage ?? false,
      );
    } else {
      throw Exception(response.exception?.getMessage() ?? '请求失败');
    }
  }

  /// 前往下单
  ///
  /// 参数：
  /// - context: BuildContext，用于跳转
  /// - ref: WidgetRef，用于跳转
  /// - applyType: 平台类型（1=美团，2=饿了么）
  /// - wxPath: 小程序路径（美团必需）
  /// - activityId: 活动ID（饿了么必需）
  /// - shopId: 商铺ID（饿了么必需）
  Future<void> goToPlaceOrder({
    required BuildContext context,
    required WidgetRef ref,
    required int applyType,
    String? wxPath,
    String? activityId,
    String? shopId,
  }) async {

    // 美团（applyType == 1）：直接使用已有的 wxPath
    if (applyType == 1) {
      if (wxPath == null || wxPath.isEmpty) {
        SmartDialog.showToast('暂无下单链接');
        return;
      }

      // 跳转到美团小程序
      if (context.mounted) {
        await SmartJumpUtil.smartJump(
          ref: ref,
          context: context,
          jumpData: Jump(type: 6), // type 6 = 跳小程序
          fallbackUrl: wxPath,
          miniId: 'gh_72a4eb2d4324', // 美团小程序 AppID
        );
      }
      return;
    }

    // 饿了么（applyType != 1）：需要先调用 getActionUrl 获取 wxPath
    if (activityId == null || shopId == null) {
      SmartDialog.showToast('活动信息不完整');
      return;
    }

    try {
      // 调用 API 获取饿了么的下单链接
      final response = await getElmActionUrl(
        activityId: activityId,
        shopId: shopId,
      );

      if (response == null || response['wxPath'] == null) {
        SmartDialog.showToast('获取下单链接失败');
        return;
      }

      // 跳转到饿了么小程序
      if (context.mounted) {
        await SmartJumpUtil.smartJump(
          ref: ref,
          context: context,
          jumpData: Jump(type: 6), // type 6 = 跳小程序
          fallbackUrl: response['wxPath'] as String,
          miniId: 'gh_6506303a12bb', // 饿了么小程序 AppID
        );
      }
    } catch (e) {
      debugPrint('获取饿了么下单链接失败: $e');
      SmartDialog.showToast('获取下单链接失败，请稍后重试');
    }
  }

  /// 前往评论
  ///
  /// 参数：
  /// - context: BuildContext，用于跳转
  /// - ref: WidgetRef，用于跳转
  /// - applyType: 平台类型（1=美团，2=饿了么）
  Future<void> goToComment({
    required BuildContext context,
    required WidgetRef ref,
    required int applyType,
  }) async {

    // 根据平台类型确定评论页面路径
    // 美团评论页面路径: pages/orders/orders
    // 饿了么评论页面路径: pages/orderlist/pages/index/index
    String wxPath;
    if (applyType == 1) {
      // 美团
      wxPath = 'pages/orders/orders';
    } else {
      // 饿了么
      wxPath = 'pages/orderlist/pages/index/index';
    }

    // 跳转到对应平台的订单/评论页面
    if (context.mounted) {
      await SmartJumpUtil.smartJump(
        ref: ref,
        context: context,
        jumpData: Jump(type: 6), // type 6 = 跳小程序
        fallbackUrl: wxPath,
        miniId: applyType == 1 ? 'gh_72a4eb2d4324' : 'gh_6506303a12bb',
      );
    }
  }

  /// 领取红包 - 跳转到对应外卖平台小程序领取红包
  ///
  /// 参数：
  /// - context: BuildContext，用于跳转
  /// - ref: WidgetRef，用于跳转
  /// - applyType: 平台类型（1=美团，2=饿了么）
  Future<void> receiveRedPacket({
    required BuildContext context,
    required WidgetRef ref,
    required int applyType,
  }) async {
    // 获取用户ID
    final userId = GlobalConfig.bwcUserId;
    if (userId == null) {
      SmartDialog.showToast('请先登录');
      return;
    }
    
    final userIdStr = userId.toString();

    if (applyType == 1) {
      // 美团：调用 getMtRedPacketLink 获取推广链接
      final response = await FunService.getMtRedPacketLink(userId: userIdStr);

      if (response.status == Status.completed && response.data != null) {
        final wxPath = response.data!;
        
        if (!context.mounted) return;
        
        // 跳转到美团外卖小程序（gh_870576f3c6f9）
        await SmartJumpUtil.smartJump(
          ref: ref,
          context: context,
          jumpData: Jump(type: 6), // type 6 = 跳小程序
          fallbackUrl: wxPath,
          miniId: 'gh_870576f3c6f9', // 美团外卖小程序 AppID
        );
      } else {
        SmartDialog.showToast('获取红包链接失败');
      }
    } else {
      // 饿了么：调用 getElmRedPacketLink 获取推广链接
      final response = await FunService.getElmRedPacketLink(userId: userIdStr);

      if (response.status == Status.completed && response.data != null) {
        final wxPath = response.data!['wxPath'] as String?;
        final miniId = response.data!['wxOrgAppId'] as String?;
        
        if (wxPath == null || wxPath.isEmpty || miniId == null || miniId.isEmpty) {
          SmartDialog.showToast('获取红包链接失败');
          return;
        }
        
        if (!context.mounted) return;
        
        // 跳转到饿了么小程序
        await SmartJumpUtil.smartJump(
          ref: ref,
          context: context,
          jumpData: Jump(type: 6), // type 6 = 跳小程序
          fallbackUrl: wxPath,
          miniId: miniId, // 使用返回的 wxOrgAppId
        );
      } else {
        SmartDialog.showToast('获取红包链接失败');
      }
    }
  }
}

/// 记录列表结果
class _RecordsResult {
  final List<BawangcanRegistrationRecord> list;
  final bool hasMore;

  _RecordsResult({required this.list, required this.hasMore});
}
