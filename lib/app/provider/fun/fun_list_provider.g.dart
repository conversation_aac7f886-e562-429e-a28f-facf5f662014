// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fun_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchMeituanCouponListHash() =>
    r'56d2dbe576195ac694621ec49e525b551d8831f8';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchMeituanCouponList].
@ProviderFor(fetchMeituanCouponList)
const fetchMeituanCouponListProvider = FetchMeituanCouponListFamily();

/// See also [fetchMeituanCouponList].
class FetchMeituanCouponListFamily
    extends Family<AsyncValue<ApiResponse<MeituanCouponResponse?>>> {
  /// See also [fetchMeituanCouponList].
  const FetchMeituanCouponListFamily();

  /// See also [fetchMeituanCouponList].
  FetchMeituanCouponListProvider call(
    FunTabConfig mainTabConfig,
    FunTabConfig childTabConfig, {
    double? latitude,
    double? longitude,
    int pageNo = 1,
    int pageSize = 20,
    FunSortOption? sortOption,
  }) {
    return FetchMeituanCouponListProvider(
      mainTabConfig,
      childTabConfig,
      latitude: latitude,
      longitude: longitude,
      pageNo: pageNo,
      pageSize: pageSize,
      sortOption: sortOption,
    );
  }

  @override
  FetchMeituanCouponListProvider getProviderOverride(
    covariant FetchMeituanCouponListProvider provider,
  ) {
    return call(
      provider.mainTabConfig,
      provider.childTabConfig,
      latitude: provider.latitude,
      longitude: provider.longitude,
      pageNo: provider.pageNo,
      pageSize: provider.pageSize,
      sortOption: provider.sortOption,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchMeituanCouponListProvider';
}

/// See also [fetchMeituanCouponList].
class FetchMeituanCouponListProvider
    extends AutoDisposeFutureProvider<ApiResponse<MeituanCouponResponse?>> {
  /// See also [fetchMeituanCouponList].
  FetchMeituanCouponListProvider(
    FunTabConfig mainTabConfig,
    FunTabConfig childTabConfig, {
    double? latitude,
    double? longitude,
    int pageNo = 1,
    int pageSize = 20,
    FunSortOption? sortOption,
  }) : this._internal(
          (ref) => fetchMeituanCouponList(
            ref as FetchMeituanCouponListRef,
            mainTabConfig,
            childTabConfig,
            latitude: latitude,
            longitude: longitude,
            pageNo: pageNo,
            pageSize: pageSize,
            sortOption: sortOption,
          ),
          from: fetchMeituanCouponListProvider,
          name: r'fetchMeituanCouponListProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchMeituanCouponListHash,
          dependencies: FetchMeituanCouponListFamily._dependencies,
          allTransitiveDependencies:
              FetchMeituanCouponListFamily._allTransitiveDependencies,
          mainTabConfig: mainTabConfig,
          childTabConfig: childTabConfig,
          latitude: latitude,
          longitude: longitude,
          pageNo: pageNo,
          pageSize: pageSize,
          sortOption: sortOption,
        );

  FetchMeituanCouponListProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.mainTabConfig,
    required this.childTabConfig,
    required this.latitude,
    required this.longitude,
    required this.pageNo,
    required this.pageSize,
    required this.sortOption,
  }) : super.internal();

  final FunTabConfig mainTabConfig;
  final FunTabConfig childTabConfig;
  final double? latitude;
  final double? longitude;
  final int pageNo;
  final int pageSize;
  final FunSortOption? sortOption;

  @override
  Override overrideWith(
    FutureOr<ApiResponse<MeituanCouponResponse?>> Function(
            FetchMeituanCouponListRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchMeituanCouponListProvider._internal(
        (ref) => create(ref as FetchMeituanCouponListRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        mainTabConfig: mainTabConfig,
        childTabConfig: childTabConfig,
        latitude: latitude,
        longitude: longitude,
        pageNo: pageNo,
        pageSize: pageSize,
        sortOption: sortOption,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<ApiResponse<MeituanCouponResponse?>>
      createElement() {
    return _FetchMeituanCouponListProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchMeituanCouponListProvider &&
        other.mainTabConfig == mainTabConfig &&
        other.childTabConfig == childTabConfig &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.pageNo == pageNo &&
        other.pageSize == pageSize &&
        other.sortOption == sortOption;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mainTabConfig.hashCode);
    hash = _SystemHash.combine(hash, childTabConfig.hashCode);
    hash = _SystemHash.combine(hash, latitude.hashCode);
    hash = _SystemHash.combine(hash, longitude.hashCode);
    hash = _SystemHash.combine(hash, pageNo.hashCode);
    hash = _SystemHash.combine(hash, pageSize.hashCode);
    hash = _SystemHash.combine(hash, sortOption.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FetchMeituanCouponListRef
    on AutoDisposeFutureProviderRef<ApiResponse<MeituanCouponResponse?>> {
  /// The parameter `mainTabConfig` of this provider.
  FunTabConfig get mainTabConfig;

  /// The parameter `childTabConfig` of this provider.
  FunTabConfig get childTabConfig;

  /// The parameter `latitude` of this provider.
  double? get latitude;

  /// The parameter `longitude` of this provider.
  double? get longitude;

  /// The parameter `pageNo` of this provider.
  int get pageNo;

  /// The parameter `pageSize` of this provider.
  int get pageSize;

  /// The parameter `sortOption` of this provider.
  FunSortOption? get sortOption;
}

class _FetchMeituanCouponListProviderElement
    extends AutoDisposeFutureProviderElement<
        ApiResponse<MeituanCouponResponse?>> with FetchMeituanCouponListRef {
  _FetchMeituanCouponListProviderElement(super.provider);

  @override
  FunTabConfig get mainTabConfig =>
      (origin as FetchMeituanCouponListProvider).mainTabConfig;
  @override
  FunTabConfig get childTabConfig =>
      (origin as FetchMeituanCouponListProvider).childTabConfig;
  @override
  double? get latitude => (origin as FetchMeituanCouponListProvider).latitude;
  @override
  double? get longitude => (origin as FetchMeituanCouponListProvider).longitude;
  @override
  int get pageNo => (origin as FetchMeituanCouponListProvider).pageNo;
  @override
  int get pageSize => (origin as FetchMeituanCouponListProvider).pageSize;
  @override
  FunSortOption? get sortOption =>
      (origin as FetchMeituanCouponListProvider).sortOption;
}

String _$fetchFunMainTabConfigHash() =>
    r'b632e4e45480305049fb899c75739f58311060c1';

/// See also [fetchFunMainTabConfig].
@ProviderFor(fetchFunMainTabConfig)
const fetchFunMainTabConfigProvider = FetchFunMainTabConfigFamily();

/// See also [fetchFunMainTabConfig].
class FetchFunMainTabConfigFamily extends Family<AsyncValue<FunTabResult?>> {
  /// See also [fetchFunMainTabConfig].
  const FetchFunMainTabConfigFamily();

  /// See also [fetchFunMainTabConfig].
  FetchFunMainTabConfigProvider call(
    String code,
  ) {
    return FetchFunMainTabConfigProvider(
      code,
    );
  }

  @override
  FetchFunMainTabConfigProvider getProviderOverride(
    covariant FetchFunMainTabConfigProvider provider,
  ) {
    return call(
      provider.code,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchFunMainTabConfigProvider';
}

/// See also [fetchFunMainTabConfig].
class FetchFunMainTabConfigProvider
    extends AutoDisposeFutureProvider<FunTabResult?> {
  /// See also [fetchFunMainTabConfig].
  FetchFunMainTabConfigProvider(
    String code,
  ) : this._internal(
          (ref) => fetchFunMainTabConfig(
            ref as FetchFunMainTabConfigRef,
            code,
          ),
          from: fetchFunMainTabConfigProvider,
          name: r'fetchFunMainTabConfigProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchFunMainTabConfigHash,
          dependencies: FetchFunMainTabConfigFamily._dependencies,
          allTransitiveDependencies:
              FetchFunMainTabConfigFamily._allTransitiveDependencies,
          code: code,
        );

  FetchFunMainTabConfigProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String code;

  @override
  Override overrideWith(
    FutureOr<FunTabResult?> Function(FetchFunMainTabConfigRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchFunMainTabConfigProvider._internal(
        (ref) => create(ref as FetchFunMainTabConfigRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<FunTabResult?> createElement() {
    return _FetchFunMainTabConfigProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchFunMainTabConfigProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FetchFunMainTabConfigRef on AutoDisposeFutureProviderRef<FunTabResult?> {
  /// The parameter `code` of this provider.
  String get code;
}

class _FetchFunMainTabConfigProviderElement
    extends AutoDisposeFutureProviderElement<FunTabResult?>
    with FetchFunMainTabConfigRef {
  _FetchFunMainTabConfigProviderElement(super.provider);

  @override
  String get code => (origin as FetchFunMainTabConfigProvider).code;
}

String _$fetchFunChildrenTabConfigHash() =>
    r'e4c4b7a81c79f28599ad1416316642f5d8ff0bc7';

/// See also [fetchFunChildrenTabConfig].
@ProviderFor(fetchFunChildrenTabConfig)
const fetchFunChildrenTabConfigProvider = FetchFunChildrenTabConfigFamily();

/// See also [fetchFunChildrenTabConfig].
class FetchFunChildrenTabConfigFamily
    extends Family<AsyncValue<FunTabResult?>> {
  /// See also [fetchFunChildrenTabConfig].
  const FetchFunChildrenTabConfigFamily();

  /// See also [fetchFunChildrenTabConfig].
  FetchFunChildrenTabConfigProvider call(
    String code,
  ) {
    return FetchFunChildrenTabConfigProvider(
      code,
    );
  }

  @override
  FetchFunChildrenTabConfigProvider getProviderOverride(
    covariant FetchFunChildrenTabConfigProvider provider,
  ) {
    return call(
      provider.code,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchFunChildrenTabConfigProvider';
}

/// See also [fetchFunChildrenTabConfig].
class FetchFunChildrenTabConfigProvider
    extends AutoDisposeFutureProvider<FunTabResult?> {
  /// See also [fetchFunChildrenTabConfig].
  FetchFunChildrenTabConfigProvider(
    String code,
  ) : this._internal(
          (ref) => fetchFunChildrenTabConfig(
            ref as FetchFunChildrenTabConfigRef,
            code,
          ),
          from: fetchFunChildrenTabConfigProvider,
          name: r'fetchFunChildrenTabConfigProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchFunChildrenTabConfigHash,
          dependencies: FetchFunChildrenTabConfigFamily._dependencies,
          allTransitiveDependencies:
              FetchFunChildrenTabConfigFamily._allTransitiveDependencies,
          code: code,
        );

  FetchFunChildrenTabConfigProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String code;

  @override
  Override overrideWith(
    FutureOr<FunTabResult?> Function(FetchFunChildrenTabConfigRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchFunChildrenTabConfigProvider._internal(
        (ref) => create(ref as FetchFunChildrenTabConfigRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<FunTabResult?> createElement() {
    return _FetchFunChildrenTabConfigProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchFunChildrenTabConfigProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin FetchFunChildrenTabConfigRef
    on AutoDisposeFutureProviderRef<FunTabResult?> {
  /// The parameter `code` of this provider.
  String get code;
}

class _FetchFunChildrenTabConfigProviderElement
    extends AutoDisposeFutureProviderElement<FunTabResult?>
    with FetchFunChildrenTabConfigRef {
  _FetchFunChildrenTabConfigProviderElement(super.provider);

  @override
  String get code => (origin as FetchFunChildrenTabConfigProvider).code;
}

String _$currentMainTabCodeHash() =>
    r'cc68129cb18ef3fd99fa2779342d9a4db0239299';

/// 获取当前选中的主标签页代码
///
/// Copied from [currentMainTabCode].
@ProviderFor(currentMainTabCode)
final currentMainTabCodeProvider = AutoDisposeProvider<String?>.internal(
  currentMainTabCode,
  name: r'currentMainTabCodeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentMainTabCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentMainTabCodeRef = AutoDisposeProviderRef<String?>;
String _$meituanShareLinkHash() => r'f78920bcdb768ba6be2f226cdc7951ba2dedfcd0';

abstract class _$MeituanShareLink
    extends BuildlessAutoDisposeAsyncNotifier<String> {
  late final MeituanCouponItem item;

  FutureOr<String> build(
    MeituanCouponItem item,
  );
}

/// See also [MeituanShareLink].
@ProviderFor(MeituanShareLink)
const meituanShareLinkProvider = MeituanShareLinkFamily();

/// See also [MeituanShareLink].
class MeituanShareLinkFamily extends Family<AsyncValue<String>> {
  /// See also [MeituanShareLink].
  const MeituanShareLinkFamily();

  /// See also [MeituanShareLink].
  MeituanShareLinkProvider call(
    MeituanCouponItem item,
  ) {
    return MeituanShareLinkProvider(
      item,
    );
  }

  @override
  MeituanShareLinkProvider getProviderOverride(
    covariant MeituanShareLinkProvider provider,
  ) {
    return call(
      provider.item,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'meituanShareLinkProvider';
}

/// See also [MeituanShareLink].
class MeituanShareLinkProvider
    extends AutoDisposeAsyncNotifierProviderImpl<MeituanShareLink, String> {
  /// See also [MeituanShareLink].
  MeituanShareLinkProvider(
    MeituanCouponItem item,
  ) : this._internal(
          () => MeituanShareLink()..item = item,
          from: meituanShareLinkProvider,
          name: r'meituanShareLinkProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$meituanShareLinkHash,
          dependencies: MeituanShareLinkFamily._dependencies,
          allTransitiveDependencies:
              MeituanShareLinkFamily._allTransitiveDependencies,
          item: item,
        );

  MeituanShareLinkProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.item,
  }) : super.internal();

  final MeituanCouponItem item;

  @override
  FutureOr<String> runNotifierBuild(
    covariant MeituanShareLink notifier,
  ) {
    return notifier.build(
      item,
    );
  }

  @override
  Override overrideWith(MeituanShareLink Function() create) {
    return ProviderOverride(
      origin: this,
      override: MeituanShareLinkProvider._internal(
        () => create()..item = item,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        item: item,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<MeituanShareLink, String>
      createElement() {
    return _MeituanShareLinkProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MeituanShareLinkProvider && other.item == item;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, item.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin MeituanShareLinkRef on AutoDisposeAsyncNotifierProviderRef<String> {
  /// The parameter `item` of this provider.
  MeituanCouponItem get item;
}

class _MeituanShareLinkProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<MeituanShareLink, String>
    with MeituanShareLinkRef {
  _MeituanShareLinkProviderElement(super.provider);

  @override
  MeituanCouponItem get item => (origin as MeituanShareLinkProvider).item;
}

String _$funItemClickHash() => r'e364e0e1312f17ac71fbe12f6e8e4b729d28f517';

/// See also [FunItemClick].
@ProviderFor(FunItemClick)
final funItemClickProvider =
    AutoDisposeNotifierProvider<FunItemClick, void>.internal(
  FunItemClick.new,
  name: r'funItemClickProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$funItemClickHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FunItemClick = AutoDisposeNotifier<void>;
String _$manageFunTabConfigHash() =>
    r'c92dc29ee96d7527c08a5eb70ce5a7356fe94ab4';

/// See also [ManageFunTabConfig].
@ProviderFor(ManageFunTabConfig)
final manageFunTabConfigProvider = AutoDisposeAsyncNotifierProvider<
    ManageFunTabConfig, TabStateConfig?>.internal(
  ManageFunTabConfig.new,
  name: r'manageFunTabConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$manageFunTabConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ManageFunTabConfig = AutoDisposeAsyncNotifier<TabStateConfig?>;
String _$tabSelectionStateHash() => r'8a250e033dc370bb01f94f0018ea007fe490271e';

/// See also [TabSelectionState].
@ProviderFor(TabSelectionState)
final tabSelectionStateProvider =
    AutoDisposeNotifierProvider<TabSelectionState, TabSelection>.internal(
  TabSelectionState.new,
  name: r'tabSelectionStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$tabSelectionStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TabSelectionState = AutoDisposeNotifier<TabSelection>;
String _$locationManagerHash() => r'945df1648f06d8f0ed6d51110b97f5b93e900d94';

/// 位置管理 Provider
///
/// Copied from [LocationManager].
@ProviderFor(LocationManager)
final locationManagerProvider =
    AutoDisposeNotifierProvider<LocationManager, LocationState>.internal(
  LocationManager.new,
  name: r'locationManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$locationManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LocationManager = AutoDisposeNotifier<LocationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
