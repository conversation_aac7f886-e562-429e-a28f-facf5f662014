import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:msmds_platform/app/repository/modals/fun/bawangcan_registration_record.dart';
import 'package:msmds_platform/app/repository/service/fun_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'bawangcan_countdown_provider.g.dart';

/// 霸王餐倒计时状态
class BawangcanCountdownState {
  /// 需要提醒的报名记录
  final BawangcanRegistrationRecord? reminderRecord;

  /// 过期时间
  final DateTime? expireTime;

  /// 是否正在加载
  final bool isLoading;

  /// 最后更新时间（用于强制触发状态变化）
  final DateTime lastUpdateTime;

  BawangcanCountdownState({
    this.reminderRecord,
    this.expireTime,
    this.isLoading = false,
    DateTime? lastUpdateTime,
  }) : lastUpdateTime = lastUpdateTime ?? DateTime.fromMillisecondsSinceEpoch(0);

  BawangcanCountdownState copyWith({
    BawangcanRegistrationRecord? reminderRecord,
    DateTime? expireTime,
    bool? isLoading,
    bool clearReminder = false,
    bool forceUpdate = false,
  }) {
    return BawangcanCountdownState(
      reminderRecord: clearReminder ? null : (reminderRecord ?? this.reminderRecord),
      expireTime: clearReminder ? null : (expireTime ?? this.expireTime),
      isLoading: isLoading ?? this.isLoading,
      lastUpdateTime: forceUpdate ? DateTime.now() : lastUpdateTime,
    );
  }

  /// 计算剩余时间
  Duration get remainingTime {
    if (expireTime == null) return Duration.zero;
    final remaining = expireTime!.difference(DateTime.now());
    return remaining.isNegative ? Duration.zero : remaining;
  }

  /// 是否有活跃的倒计时
  bool get hasActiveCountdown {
    return reminderRecord != null && expireTime != null && remainingTime > Duration.zero;
  }

  /// 格式化倒计时显示（HH:MM:SS）
  String get formattedCountdown {
    final totalSeconds = remainingTime.inSeconds;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;
    final seconds = totalSeconds % 60;

    return '${hours.toString().padLeft(2, '0')}:'
        '${minutes.toString().padLeft(2, '0')}:'
        '${seconds.toString().padLeft(2, '0')}';
  }
}

/// 霸王餐倒计时状态管理
@riverpod
class BawangcanCountdown extends _$BawangcanCountdown {
  /// 提醒状态（已报名）
  static const int _reminderState = 3;

  /// 倒计时 Timer
  Timer? _countdownTimer;

  @override
  BawangcanCountdownState build() {
    // 注册清理回调，确保 Provider 销毁时清理 Timer
    ref.onDispose(() {
      _stopTimer();
    });
    
    return BawangcanCountdownState();
  }

  /// 加载第一条已报名记录（用于倒计时提醒）
  Future<void> loadFirstRegisteredRecord() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true);

    try {
      const params = BawangcanRegistrationRecordParams(
        pageNum: 1,
        pageSize: 10,
        state: _reminderState,
      );

      final response = await FunService.getApplyOrderList(params);

      if (response.status == Status.completed && response.data != null) {
        final records = response.data!.list ?? [];
        if (records.isNotEmpty) {
          final record = records.first;
          final expireTime = _parseExpireTime(record.expireTime);
          state = state.copyWith(
            reminderRecord: record,
            expireTime: expireTime,
            isLoading: false,
          );
        } else {
          // 没有记录
          state = state.copyWith(
            isLoading: false,
            clearReminder: true,
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          clearReminder: true,
        );
      }
    } catch (e) {
      debugPrint('loadFirstRegisteredRecord error: $e');
      state = state.copyWith(
        isLoading: false,
        clearReminder: true,
      );
    }
  }

  /// 启动倒计时（供 Widget 调用）
  void startCountdown() {
    _stopTimer();

    // 每秒触发一次状态更新
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      try {
        if (!state.hasActiveCountdown) {
          timer.cancel();
          _countdownTimer = null;
          // 倒计时结束，重新加载数据
          loadFirstRegisteredRecord().then((_) {
            // 加载完成后，如果有新数据，重新启动倒计时
            if (state.hasActiveCountdown) {
              startCountdown();
            }
          });
        } else {
          // 强制触发状态更新（通过 forceUpdate 参数）
          state = state.copyWith(forceUpdate: true);
        }
      } catch (e) {
        debugPrint('Error in countdown timer: $e');
        timer.cancel();
        _countdownTimer = null;
      }
    });
  }

  /// 刷新报名提醒倒计时（供外部调用，如取消报名后）
  Future<void> refreshCountdown() async {
    // 停止当前倒计时
    _stopTimer();
    
    // 重新加载数据
    await loadFirstRegisteredRecord();
    
    // 如果有新数据，重新启动倒计时
    if (state.hasActiveCountdown) {
      startCountdown();
    }
  }

  /// 内部方法：停止 Timer
  void _stopTimer() {
    _countdownTimer?.cancel();
    _countdownTimer = null;
  }

  /// 解析过期时间
  static DateTime? _parseExpireTime(String? raw) {
    if (raw == null || raw.isEmpty) {
      return null;
    }
    final normalised = raw.contains('T') ? raw : raw.replaceFirst(' ', 'T');
    try {
      return DateTime.parse(normalised);
    } catch (_) {
      return null;
    }
  }
}
