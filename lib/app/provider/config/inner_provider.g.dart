// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inner_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchVipShopSwiperConfigHash() =>
    r'81367cdcce8c807e927536d1915eabaa63089191';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package: app.provider.config
/// @ClassName: inner_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/21 15:41
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/21 15:41
/// @UpdateRemark: 更新说明
/// 获取内部唯品会页面的Banner
///
/// Copied from [fetchVipShopSwiperConfig].
@ProviderFor(fetchVipShopSwiperConfig)
final fetchVipShopSwiperConfigProvider =
    AutoDisposeFutureProvider<List<IconConfig>?>.internal(
  fetchVipShopSwiperConfig,
  name: r'fetchVipShopSwiperConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchVipShopSwiperConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchVipShopSwiperConfigRef
    = AutoDisposeFutureProviderRef<List<IconConfig>?>;
String _$suNingSavingHash() => r'd131b56715a61c535672276f78fed1eeb3d9657f';

/// See also [SuNingSaving].
@ProviderFor(SuNingSaving)
final suNingSavingProvider =
    AutoDisposeNotifierProvider<SuNingSaving, void>.internal(
  SuNingSaving.new,
  name: r'suNingSavingProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$suNingSavingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SuNingSaving = AutoDisposeNotifier<void>;
String _$redPacketsListHash() => r'9f5051630f7844ffe0cedf88af7ebfdf670ea994';

/// See also [RedPacketsList].
@ProviderFor(RedPacketsList)
final redPacketsListProvider =
    AutoDisposeNotifierProvider<RedPacketsList, RedPacketResult>.internal(
  RedPacketsList.new,
  name: r'redPacketsListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$redPacketsListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RedPacketsList = AutoDisposeNotifier<RedPacketResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
