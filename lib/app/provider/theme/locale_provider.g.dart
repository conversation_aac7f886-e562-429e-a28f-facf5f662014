// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'locale_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$localeSettingHash() => r'd8dd11d036ffa62eb59a16e1f3cc1850b02ef892';

/// See also [LocaleSetting].
@ProviderFor(LocaleSetting)
final localeSettingProvider =
    AutoDisposeNotifierProvider<LocaleSetting, Locale?>.internal(
  LocaleSetting.new,
  name: r'localeSettingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$localeSettingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LocaleSetting = AutoDisposeNotifier<Locale?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
