// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'goods_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$jdGoodsByPkgHash() => r'4f0ebb88d4b938e948e7efab02246348b33e091e';

/// ================首页京东商品包======================
///
/// Copied from [JdGoodsByPkg].
@ProviderFor(JdGoodsByPkg)
final jdGoodsByPkgProvider =
    NotifierProvider<JdGoodsByPkg, GoodsResult>.internal(
  JdGoodsByPkg.new,
  name: r'jdGoodsByPkgProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$jdGoodsByPkgHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$JdGoodsByPkg = Notifier<GoodsResult>;
String _$tbGoodsByPkgHash() => r'8250e0efecbdecdd87413affaa23fa55f335b886';

/// ================首页京东商品包======================
/// ================首页淘宝商品包======================
///
/// Copied from [TbGoodsByPkg].
@ProviderFor(TbGoodsByPkg)
final tbGoodsByPkgProvider =
    NotifierProvider<TbGoodsByPkg, GoodsResult>.internal(
  TbGoodsByPkg.new,
  name: r'tbGoodsByPkgProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$tbGoodsByPkgHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TbGoodsByPkg = Notifier<GoodsResult>;
String _$pddGoodsByPkgHash() => r'9f614e1980296600d653ced35c6a86b3d4558041';

/// ================首页淘宝商品包======================
/// ================首页拼多多商品包======================
///
/// Copied from [PddGoodsByPkg].
@ProviderFor(PddGoodsByPkg)
final pddGoodsByPkgProvider =
    NotifierProvider<PddGoodsByPkg, GoodsResult>.internal(
  PddGoodsByPkg.new,
  name: r'pddGoodsByPkgProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pddGoodsByPkgHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PddGoodsByPkg = Notifier<GoodsResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
