// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'history_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$historyInfoHash() => r'ca0cdbf0b6bd557aa95785fd038d8cf1a660555a';

/// See also [HistoryInfo].
@ProviderFor(HistoryInfo)
final historyInfoProvider =
    AutoDisposeNotifierProvider<HistoryInfo, HistoryGoodsResult>.internal(
  HistoryInfo.new,
  name: r'historyInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$historyInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$HistoryInfo = AutoDisposeNotifier<HistoryGoodsResult>;
String _$historyManageStateHash() =>
    r'ef547b60b76539065293926c86f140c48ad80d0b';

/// See also [HistoryManageState].
@ProviderFor(HistoryManageState)
final historyManageStateProvider =
    AutoDisposeNotifierProvider<HistoryManageState, bool>.internal(
  HistoryManageState.new,
  name: r'historyManageStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$historyManageStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$HistoryManageState = AutoDisposeNotifier<bool>;
String _$historyManageAllSelectHash() =>
    r'f2d7793f15244da493258dec24b4ad01b650cda8';

/// See also [HistoryManageAllSelect].
@ProviderFor(HistoryManageAllSelect)
final historyManageAllSelectProvider =
    AutoDisposeNotifierProvider<HistoryManageAllSelect, bool>.internal(
  HistoryManageAllSelect.new,
  name: r'historyManageAllSelectProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$historyManageAllSelectHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$HistoryManageAllSelect = AutoDisposeNotifier<bool>;
String _$historyManageDeleteHash() =>
    r'64f9acdd137b32a65d170414571b5e365c03a7ef';

/// See also [HistoryManageDelete].
@ProviderFor(HistoryManageDelete)
final historyManageDeleteProvider = AutoDisposeNotifierProvider<
    HistoryManageDelete, List<FavoriteGoods?>>.internal(
  HistoryManageDelete.new,
  name: r'historyManageDeleteProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$historyManageDeleteHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$HistoryManageDelete = AutoDisposeNotifier<List<FavoriteGoods?>>;
String _$saveGoodsBrowsingHistoryHash() =>
    r'4dfd8397ab5571b1c7918dc4e247d5450e4ea8a8';

/// See also [SaveGoodsBrowsingHistory].
@ProviderFor(SaveGoodsBrowsingHistory)
final saveGoodsBrowsingHistoryProvider =
    AutoDisposeNotifierProvider<SaveGoodsBrowsingHistory, void>.internal(
  SaveGoodsBrowsingHistory.new,
  name: r'saveGoodsBrowsingHistoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$saveGoodsBrowsingHistoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SaveGoodsBrowsingHistory = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
