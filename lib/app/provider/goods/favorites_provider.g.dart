// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favorites_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$favoriteInfoHash() => r'622637f1835951d374bccffcf70345fc66d0a15e';

/// See also [FavoriteInfo].
@ProviderFor(FavoriteInfo)
final favoriteInfoProvider =
    AutoDisposeNotifierProvider<FavoriteInfo, FavoriteGoodsResult>.internal(
  FavoriteInfo.new,
  name: r'favoriteInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$favoriteInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FavoriteInfo = AutoDisposeNotifier<FavoriteGoodsResult>;
String _$favoriteManageStateHash() =>
    r'597523ae17c51c15f3241625030c4debdf44d0c3';

/// See also [FavoriteManageState].
@ProviderFor(FavoriteManageState)
final favoriteManageStateProvider =
    AutoDisposeNotifierProvider<FavoriteManageState, bool>.internal(
  FavoriteManageState.new,
  name: r'favoriteManageStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$favoriteManageStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FavoriteManageState = AutoDisposeNotifier<bool>;
String _$favoriteManageAllSelectHash() =>
    r'026db346f5d68fab7fe49f7a73f01bcb18915d07';

/// See also [FavoriteManageAllSelect].
@ProviderFor(FavoriteManageAllSelect)
final favoriteManageAllSelectProvider =
    AutoDisposeNotifierProvider<FavoriteManageAllSelect, bool>.internal(
  FavoriteManageAllSelect.new,
  name: r'favoriteManageAllSelectProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$favoriteManageAllSelectHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FavoriteManageAllSelect = AutoDisposeNotifier<bool>;
String _$favoriteManageDeleteHash() =>
    r'892203afc22d43cabbf550bd7718ae160f6bf2df';

/// See also [FavoriteManageDelete].
@ProviderFor(FavoriteManageDelete)
final favoriteManageDeleteProvider = AutoDisposeNotifierProvider<
    FavoriteManageDelete, List<FavoriteGoods?>>.internal(
  FavoriteManageDelete.new,
  name: r'favoriteManageDeleteProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$favoriteManageDeleteHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FavoriteManageDelete = AutoDisposeNotifier<List<FavoriteGoods?>>;
String _$checkGoodsFavoriteHash() =>
    r'a5956d59208087eb4eff2a15a84cb3328dde83b2';

/// See also [CheckGoodsFavorite].
@ProviderFor(CheckGoodsFavorite)
final checkGoodsFavoriteProvider =
    AutoDisposeNotifierProvider<CheckGoodsFavorite, bool>.internal(
  CheckGoodsFavorite.new,
  name: r'checkGoodsFavoriteProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$checkGoodsFavoriteHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckGoodsFavorite = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
