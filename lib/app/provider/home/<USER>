import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:msmds_platform/app/dialog/convert_dialog.dart';
import 'package:msmds_platform/app/dialog/convert_fail_dialog.dart';
import 'package:msmds_platform/app/navigation/coosea.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/provider/order/order_detail_provider.dart';
import 'package:msmds_platform/app/provider/search/platform_provider.dart';
import 'package:msmds_platform/app/provider/search/search_provider.dart';
import 'package:msmds_platform/app/provider/setting/info_collection_provider.dart';
import 'package:msmds_platform/app/repository/modals/convert/convert_goods.dart';
import 'package:msmds_platform/app/repository/service/goods_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/config/global_config.dart';
import 'package:msmds_platform/utils/router_util.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../widgets/dialog_manager/notification_task_manager.dart';
import '../../../widgets/dialog_manager/dialog_task_manager.dart';
import '../../../widgets/dialog_manager/widget/intelligent_widget.dart';
import '../../repository/modals/config/icon_config.dart';
import '../../repository/service/account_service.dart';
import '../../repository/service/config_service.dart';
import '../config/config_provider.dart';

part 'home_provider.g.dart';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: home_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/29 11:12
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/29 11:12
/// @UpdateRemark: 更新说明
class HomeController {
  int index;
  PageController pageController;

  HomeController(this.index, this.pageController);
}

@riverpod
class Home extends _$Home {
  PageController pageController = PageController();

  @override
  HomeController build() {
    ref.onDispose(() {
      pageController.dispose();
    });
    return HomeController(0, pageController);
  }

  void jumpToPage(int index, {bool init = false}) {
    if (init) {
      pageController = PageController(initialPage: index);
      state = HomeController(index, pageController);
    } else {
      if (index == 1) {
        // 切换到签到需要先登录
        if (navigatorKey.currentContext != null) {
          RouterUtil.checkLogin(
            navigatorKey.currentContext!,
            call: () {
              state = HomeController(index, pageController);
              pageController.jumpToPage(index);
            },
            execute: true,
          );
        }
      } else {
        state = HomeController(index, pageController);
        pageController.jumpToPage(index);
      }
    }
  }

  /// deep link 跳转
  void openAppLink(BuildContext context, Uri uri) {
    var params = uri.queryParameters["params"];
    if (params != null && params.isNotEmpty) {
      var map = jsonDecode(params);
      var category = map["category"];
      if (category == "jump") {
        /// 获取跳转页面名称
        var routerName = map["url"];
        switch (routerName) {
          case CsRouter.searchPre:

            /// 搜索
            searchPage(map);
            break;
          case CsRouter.withdrawalRecord:

            /// 提现记录
            RouterUtil.checkLogin(
              context,
              call: () {
                navigatorKey.currentState?.pushNamed(CsRouter.withdrawalRecord);
              },
              execute: true,
            );
            break;
          case CsRouter.orderDetail:

            /// 订单详情
            RouterUtil.checkLogin(
              context,
              call: () {
                int orderId = map["orderId"];
                ref
                    .watch(currentOrderProvider.notifier)
                    .setCurrentOrder(orderId.toString());
                navigatorKey.currentState?.pushNamed(CsRouter.orderDetail);
              },
              execute: true,
            );
            break;
          default:
            if (routerName != null &&
                routerName != CsRouter.home &&
                routerName != '/') {
              bool? needLogin = map["needLogin"];
              if (needLogin == true && GlobalConfig.account == null) {
                RouterUtil.checkLogin(
                  context,
                  call: () {
                    if (GlobalConfig.currentRouter == routerName) {
                      navigatorKey.currentState
                          ?.pushReplacementNamed(routerName);
                    } else {
                      navigatorKey.currentState?.pushNamed(routerName);
                    }
                  },
                  execute: true,
                );
              } else {
                if (GlobalConfig.currentRouter == routerName) {
                  navigatorKey.currentState?.pushReplacementNamed(routerName);
                } else {
                  navigatorKey.currentState?.pushNamed(routerName);
                }
              }
            }
            break;
        }
      } else if (category == "switch") {
        /// 首页切换tab
        var index = map["index"];
        ref.watch(homeProvider.notifier).jumpToPage(index);
      }
    }
  }

  /// 搜索页处理
  void searchPage(Map map) {
    /// 搜索平台，默认淘宝
    int platformType = map["platformType"] ?? 1;
    String keyword = map["keyword"];

    /// 设置搜索平台
    ref.watch(platformProvider.notifier).setCurrentPlatform(
          platformType,
          isSearch: false,
        );

    /// 设置搜索词
    ref.watch(searchKeywordProvider.notifier).setKeyword(keyword);

    /// 搜索跳转
    if (GlobalConfig.currentRouter == CsRouter.search) {
      navigatorKey.currentState?.pushReplacementNamed(CsRouter.search);
    } else if (GlobalConfig.currentRouter == CsRouter.searchPre) {
      navigatorKey.currentState?.pushNamed(CsRouter.search);
    } else {
      ref.watch(searchAutoFocusProvider.notifier).setAutoFocus(false);
      navigatorKey.currentState?.pushNamed(CsRouter.searchPre);
      navigatorKey.currentState?.pushNamed(CsRouter.search);
    }
  }
}

/// 识别弹窗
@riverpod
class ConvertContent extends _$ConvertContent {
  @override
  ConvertGoods? build() {
    return null;
  }

  /// 识别接口
  Future<bool> convert() async {
    try {
      var content = await Clipboard.getData(Clipboard.kTextPlain);
      debugPrint("convert: $content");
      ref.watch(collectionClipboardProvider.notifier).collection(content?.text);
      if (content != null && content.text != null && content.text!.isNotEmpty) {
        PackageInfo packageInfo = await PackageInfo.fromPlatform();
        var result = await GoodsService.clipboardConvert(
          packageInfo.version,
          content.text!,
        );
        debugPrint("convert-eeee: $result");
        if (result.status == Status.completed) {
          Clipboard.setData(const ClipboardData(text: ""));
          // 先下掉拼多多
          if (result.data?.goods != null &&
              result.data?.goods?.platformType != 10) {
            /// 识别成功
            state = result.data?.goods;
            ConvertDialog.showConvertDialog(result.data?.goods);
          } else {
            /// 识别失败
            ConvertFailDialog.showConvertFailDialog(
              result.data?.displayStandardSearchWord,
            );
          }
          return false;
        }
      }
    } catch (e) {
      debugPrint("convert-eeee: $e");
    }
    return true;
  }
}

/// 获取最新用户信息
@riverpod
class NewestUserInfo extends _$NewestUserInfo {
  @override
  void build() {
    return;
  }

  /// 获取最新数据并更新到本地
  void getNewestUserInfo() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var userInfoById = await AccountService.getAccountDetail();
      if (userInfoById.status == Status.completed) {
        ref.read(authProvider.notifier).refreshUserInfo(userInfoById.data);
      }
    }
  }
}

/// 首页智能弹窗、飘窗
@riverpod
class ConfigSmartDialog extends _$ConfigSmartDialog {
  @override
  void build() {
    return;
  }

  // 首页智能弹窗
  void showMainSmartDialog() async {
    var result = await ConfigService.getIntelligentPopup(1, 1);
    if (result.status == Status.completed) {
      try {
        var data = result.data as List?;
        if (data != null && data.isNotEmpty) {
          _addDialogQueue(data);
          return;
        }
      } catch (e) {
        debugPrint("showMainDialog-e: $e");
      }
    }

    // 弹窗没有，尝试显示飘窗
    showMainSmartNotification();
  }

  // 首页智能飘窗
  void showMainSmartNotification() async {
    var result = await ConfigService.getIntelligentPopup(1, 4);
    if (result.status == Status.completed) {
      try {
        var data = result.data as List?;
        if (data != null && data.isNotEmpty) {
          _addNotificationQueue(data);
        }
      } catch (e) {
        debugPrint("showMainSmartNotification-e: $e");
      }
    }
  }

  // 添加弹窗到弹窗队列中
  void _addDialogQueue(List<dynamic> iconConfigs) {
    DialogTaskManager().showMultipleTask(
      iconConfigs.map((e) => _buildDialogTask(e)).toList(),
    );
  }

  DialogTask _buildDialogTask(dynamic e) {
    return DialogTask(
      priority: DialogPriority.normal,
      onDismiss: () {
        var baseId = e["id"];
        var popUpType = e["popUpType"];
        var popUpScene = e["popUpScene"];
        debugPrint("DialogTask=onDismiss: $baseId");
        recordUserClosePopUpIds(baseId, popUpScene, popUpType);
      },
      builder: (_) => SmartDialogWidget(
        iconConfig: e,
        recordExposure: recordExposure,
        recordPopupNum: recordPopupNum,
        recordHits: recordHits,
        recordCloseHits: recordCloseHits,
      ),
    );
  }

  void _addNotificationQueue(List<dynamic> iconConfigs) {
    NotifyTaskManager.showMultiple(
      iconConfigs.map((e) => _buildNotificationRequest(e)).toList(),
    );
  }

  NotifyRequest? _buildNotificationRequest(dynamic e) {
    try {
      var baseId = e["id"];
      var popUpType = e["popUpType"];
      var popUpScene = e["popUpScene"];
      var popUpStyle = e["popUpStyle"];
      var popUpStyleJso = jsonDecode(popUpStyle);
      var picData = popUpStyleJso["picData"] as List?;
      // 点击参数
      var typeData = picData?.first["jumpUrl"];
      var jumpUrl = typeData["url"] ?? typeData["jumpUrl"];
      return NotifyRequest(
        context: navigatorKey.currentContext!,
        onTap: () {
          recordHits(baseId);
          // 配置跳转
          ref.read(configItemClickProvider.notifier).configItemClick(
                IconConfig()
                  ..jumpUrl = jumpUrl
                  ..typeData = jsonEncode(typeData),
              );
        },
        onDismiss: () {
          debugPrint(
              "NotificationTask=onDismiss: $baseId, $popUpScene, $popUpType");
          // recordUserClosePopUpIds(baseId, popUpScene, popUpType);
        },
        builder: (context) {
          return SmartNotificationWidget(iconConfig: e);
        },
      );
    } catch (e) {
      return null;
    }
  }

  // 记录弹窗曝光量
  void recordExposure(int baseId) {
    ConfigService.recordExposure(baseId);
  }

  // 记录弹窗次数
  void recordPopupNum(int popUpScene, int popUpType) {
    ConfigService.recordPopupNum(popUpScene, popUpType);
  }

  // 记录用户主动关闭的弹窗
  void recordUserClosePopUpIds(int baseId, int popUpScene, int popUpType) {
    ConfigService.recordUserClosePopUpIds(baseId, popUpScene, popUpType);
  }

  // 记录弹窗点击量
  void recordHits(int baseId) {
    ConfigService.recordHits(baseId);
  }

  // 记录弹窗关闭点击量
  void recordCloseHits(int baseId) {
    ConfigService.recordCloseHits(baseId);
  }
}
