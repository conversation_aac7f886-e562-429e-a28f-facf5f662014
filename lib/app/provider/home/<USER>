// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'free_buy_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchValidFreeBuyHash() => r'9c821df9a0a471e9c515087852bf589218580199';

/// ==================新人引导显示===============
/// 获取用户0元购资格
///
/// Copied from [fetchValidFreeBuy].
@ProviderFor(fetchValidFreeBuy)
final fetchValidFreeBuyProvider =
    AutoDisposeFutureProvider<ValidFreeBuy?>.internal(
  fetchValidFreeBuy,
  name: r'fetchValidFreeBuyProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchValidFreeBuyHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchValidFreeBuyRef = AutoDisposeFutureProviderRef<ValidFreeBuy?>;
String _$freeBuyTutorialHash() => r'3aa427519101ea75f943839630673b08abac98b1';

/// Copyright (C), 2021-2024, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: free_buy_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/3/28 14:30
/// @UpdateUser: frankylee
/// @UpdateData: 2024/3/28 14:30
/// @UpdateRemark: 更新说明
/// ==================新人引导显示===============
///
/// Copied from [FreeBuyTutorial].
@ProviderFor(FreeBuyTutorial)
final freeBuyTutorialProvider =
    AutoDisposeNotifierProvider<FreeBuyTutorial, bool>.internal(
  FreeBuyTutorial.new,
  name: r'freeBuyTutorialProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$freeBuyTutorialHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreeBuyTutorial = AutoDisposeNotifier<bool>;
String _$freeBuyEntranceTutorialHash() =>
    r'26833e639b8a68c505dec99f0395b5c04f7a32b9';

/// See also [FreeBuyEntranceTutorial].
@ProviderFor(FreeBuyEntranceTutorial)
final freeBuyEntranceTutorialProvider =
    AutoDisposeNotifierProvider<FreeBuyEntranceTutorial, bool>.internal(
  FreeBuyEntranceTutorial.new,
  name: r'freeBuyEntranceTutorialProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$freeBuyEntranceTutorialHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreeBuyEntranceTutorial = AutoDisposeNotifier<bool>;
String _$validFreeBuyTimerHash() => r'348e8dfdf90a985684eb3e50c96e18105c77cf96';

/// 资格倒计时
///
/// Copied from [ValidFreeBuyTimer].
@ProviderFor(ValidFreeBuyTimer)
final validFreeBuyTimerProvider =
    AutoDisposeNotifierProvider<ValidFreeBuyTimer, Duration?>.internal(
  ValidFreeBuyTimer.new,
  name: r'validFreeBuyTimerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$validFreeBuyTimerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ValidFreeBuyTimer = AutoDisposeNotifier<Duration?>;
String _$freeBuyEntranceHash() => r'2287d5359fc4edae9a3263aa2dc0f30e1bc8573b';

/// 0元购入口商品显示
///
/// Copied from [FreeBuyEntrance].
@ProviderFor(FreeBuyEntrance)
final freeBuyEntranceProvider = AutoDisposeNotifierProvider<FreeBuyEntrance,
    List<FreeBuyGoodItem?>?>.internal(
  FreeBuyEntrance.new,
  name: r'freeBuyEntranceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$freeBuyEntranceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreeBuyEntrance = AutoDisposeNotifier<List<FreeBuyGoodItem?>?>;
String _$freeBuyGoodsListHash() => r'9ec452fe922397a7231c6f77dbf7c010fdd17e79';

/// See also [FreeBuyGoodsList].
@ProviderFor(FreeBuyGoodsList)
final freeBuyGoodsListProvider =
    AutoDisposeNotifierProvider<FreeBuyGoodsList, FreeBuyGoodsResult>.internal(
  FreeBuyGoodsList.new,
  name: r'freeBuyGoodsListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$freeBuyGoodsListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FreeBuyGoodsList = AutoDisposeNotifier<FreeBuyGoodsResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
