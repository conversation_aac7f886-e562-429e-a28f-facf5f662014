// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscribe_reminder_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$reminderGoodsListHash() => r'cb9f89662145efeac311f096385051a6cafdec1f';

/// See also [ReminderGoodsList].
@ProviderFor(ReminderGoodsList)
final reminderGoodsListProvider =
    AutoDisposeNotifierProvider<ReminderGoodsList, ReminderResult>.internal(
  ReminderGoodsList.new,
  name: r'reminderGoodsListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$reminderGoodsListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ReminderGoodsList = AutoDisposeNotifier<ReminderResult>;
String _$saveReminderHash() => r'edf8cffb4ecacdb397807d58bcb80ee7261c889f';

///==================保存提醒============
///
/// Copied from [SaveReminder].
@ProviderFor(SaveReminder)
final saveReminderProvider =
    AutoDisposeNotifierProvider<SaveReminder, void>.internal(
  SaveReminder.new,
  name: r'saveReminderProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$saveReminderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SaveReminder = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
