// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchChipIdHash() => r'54b48fc594978cacefbc374d0f3868fc0d748ffc';

/// 获取酷赛唯一ID
///
/// Copied from [fetchChipId].
@ProviderFor(fetchChipId)
final fetchChipIdProvider = AutoDisposeFutureProvider<String?>.internal(
  fetchChipId,
  name: r'fetchChipIdProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$fetchChipIdHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchChipIdRef = AutoDisposeFutureProviderRef<String?>;
String _$authHash() => r'e954f6118c40f4e87b6226d5410cdc2ab3a49ed5';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: auth_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/5 17:02
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/5 17:02
/// @UpdateRemark: 更新说明
///
/// Copied from [Auth].
@ProviderFor(Auth)
final authProvider = AutoDisposeNotifierProvider<Auth, Account?>.internal(
  Auth.new,
  name: r'authProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$authHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Auth = AutoDisposeNotifier<Account?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
