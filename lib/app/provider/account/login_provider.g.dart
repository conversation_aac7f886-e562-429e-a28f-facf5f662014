// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$verifyCodeHash() => r'8433870e912c10596fa0554ec5e6d68cea515f60';

/// Copyright (C), 2021-2023, <PERSON><PERSON> Lee
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: login_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/25 16:26
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/25 16:26
/// @UpdateRemark: 更新说明
/// 获取验证码
///
/// Copied from [VerifyCode].
@ProviderFor(VerifyCode)
final verifyCodeProvider =
    AutoDisposeNotifierProvider<VerifyCode, String?>.internal(
  VerifyCode.new,
  name: r'verifyCodeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$verifyCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VerifyCode = AutoDisposeNotifier<String?>;
String _$loginHash() => r'e1b2c00986137d55e8617e25d679e3059e83deab';

/// See also [Login].
@ProviderFor(Login)
final loginProvider = AutoDisposeNotifierProvider<Login, String?>.internal(
  Login.new,
  name: r'loginProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$loginHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Login = AutoDisposeNotifier<String?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
