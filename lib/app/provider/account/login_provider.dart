import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/repository/service/account_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/utils/toast_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:crypto/crypto.dart';

part 'login_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: login_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/25 16:26
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/25 16:26
/// @UpdateRemark: 更新说明

/// 获取验证码
@riverpod
class VerifyCode extends _$VerifyCode {
  @override
  String? build() {
    return null;
  }

  // 获取服务器时间戳
  Future<int?> getServiceTime() async {
    var result = await AccountService.getServiceTime();
    if (result.status == Status.completed) {
      return result.data;
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
    return null;
  }

  // 获取验证码接口
  Future<bool> getVerifyCode(BuildContext context) async {
    var phone = ref.read(loginProvider);
    if (phone != null && phone.isNotEmpty) {
      SmartDialog.showLoading(msg: "加载中...");
      var timestamp = await getServiceTime();
      if (timestamp != null) {
        // 签名,算法：a.时间戳 b.手机号 c.时间戳加手机号的值,拼接 cba MD5加密32位小写
        var timeAndPhone = timestamp + int.parse(phone);
        var content =
            const Utf8Encoder().convert("$timeAndPhone$phone$timestamp");
        var signature = md5.convert(content);
        debugPrint("signature: $signature");
        var result = await AccountService.getVerCode(
          phone,
          signature.toString(),
          timestamp.toString(),
        );
        SmartDialog.dismiss();
        if (result.status == Status.completed) {
          return true;
        } else {
          return true;
          ToastUtil.showToast(result.exception!.getMessage());
        }
      }
      SmartDialog.dismiss();
    }
    return false;
  }

  /// 设置验证码
  void setVerifyCode(String code) {
    state = code;
  }
}

@riverpod
class Login extends _$Login {
  @override
  String? build() {
    return null;
  }

  /// 设置手机号
  void setVerifyPhone(String phone) {
    state = phone;
  }
}
