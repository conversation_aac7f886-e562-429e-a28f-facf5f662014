// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invite_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userFansInfoListHash() => r'0cbe08a1fac22b2e2b88da84430eb73080a2adad';

/// See also [UserFansInfoList].
@ProviderFor(UserFansInfoList)
final userFansInfoListProvider =
    AutoDisposeNotifierProvider<UserFansInfoList, UserFansResult>.internal(
  UserFansInfoList.new,
  name: r'userFansInfoListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userFansInfoListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserFansInfoList = AutoDisposeNotifier<UserFansResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
