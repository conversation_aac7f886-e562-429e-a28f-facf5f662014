// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderTabListHash() => r'fa1a3c68c02571be0a53be4a1f655e25cef0a461';

/// See also [OrderTabList].
@ProviderFor(OrderTabList)
final orderTabListProvider =
    AutoDisposeNotifierProvider<OrderTabList, List<OrderTab>?>.internal(
  OrderTabList.new,
  name: r'orderTabListProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$orderTabListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrderTabList = AutoDisposeNotifier<List<OrderTab>?>;
String _$orderListHash() => r'4f7a5984a552981fc0d70f820a5d6570c489de6c';

/// See also [OrderList].
@ProviderFor(OrderList)
final orderListProvider =
    AutoDisposeNotifierProvider<OrderList, OrderResult>.internal(
  OrderList.new,
  name: r'orderListProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$orderListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrderList = AutoDisposeNotifier<OrderResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
