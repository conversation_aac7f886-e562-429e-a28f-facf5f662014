// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'setting_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchVersionHash() => r'cc40f7445e4cd6d7d9f525244bf9693ebf2db7e0';

/// Copyright (C), 2021-2023, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: setting_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/30 15:06
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/30 15:06
/// @UpdateRemark: 更新说明
/// 版本号
///
/// Copied from [fetchVersion].
@ProviderFor(fetchVersion)
final fetchVersionProvider = AutoDisposeFutureProvider<String?>.internal(
  fetchVersion,
  name: r'fetchVersionProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$fetchVersionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchVersionRef = AutoDisposeFutureProviderRef<String?>;
String _$settingRecommendHash() => r'de995b352ff253d0641b72b3f27b4d33fbbb83ab';

/// 个性化消息推荐
///
/// Copied from [SettingRecommend].
@ProviderFor(SettingRecommend)
final settingRecommendProvider =
    AutoDisposeNotifierProvider<SettingRecommend, bool>.internal(
  SettingRecommend.new,
  name: r'settingRecommendProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$settingRecommendHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SettingRecommend = AutoDisposeNotifier<bool>;
String _$settingCleanCacheHash() => r'ebcfedb4d7fff8a028756f9539ef1d5f01385de6';

/// 清除缓存
///
/// Copied from [SettingCleanCache].
@ProviderFor(SettingCleanCache)
final settingCleanCacheProvider =
    AutoDisposeNotifierProvider<SettingCleanCache, String>.internal(
  SettingCleanCache.new,
  name: r'settingCleanCacheProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$settingCleanCacheHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SettingCleanCache = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
