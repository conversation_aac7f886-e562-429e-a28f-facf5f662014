// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sign_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchSignBackImgHash() => r'75aabe65ed9fec50a61c3b8b6d8a0b5aed537b99';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package: app.provider.sign
/// @ClassName: sign_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/11 10:51
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/11 10:51
/// @UpdateRemark: 更新说明
///
/// Copied from [fetchSignBackImg].
@ProviderFor(fetchSignBackImg)
final fetchSignBackImgProvider =
    AutoDisposeFutureProvider<BackConfig?>.internal(
  fetchSignBackImg,
  name: r'fetchSignBackImgProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchSignBackImgHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchSignBackImgRef = AutoDisposeFutureProviderRef<BackConfig?>;
String _$userSignIntegralHash() => r'e691d011907b17bc474dd253aabe266b3c21a82f';

/// See also [UserSignIntegral].
@ProviderFor(UserSignIntegral)
final userSignIntegralProvider =
    AutoDisposeNotifierProvider<UserSignIntegral, UserIntegral?>.internal(
  UserSignIntegral.new,
  name: r'userSignIntegralProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userSignIntegralHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserSignIntegral = AutoDisposeNotifier<UserIntegral?>;
String _$signPageRecordHash() => r'caf17b2b793bc48b03d06f49ef0222ce6ededd37';

/// See also [SignPageRecord].
@ProviderFor(SignPageRecord)
final signPageRecordProvider =
    AutoDisposeNotifierProvider<SignPageRecord, SignRecord?>.internal(
  SignPageRecord.new,
  name: r'signPageRecordProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$signPageRecordHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SignPageRecord = AutoDisposeNotifier<SignRecord?>;
String _$signLayoutConfigHash() => r'20f70bd9c627b7ef1611ca3bb6dc261e1202bb75';

/// See also [SignLayoutConfig].
@ProviderFor(SignLayoutConfig)
final signLayoutConfigProvider =
    AutoDisposeNotifierProvider<SignLayoutConfig, SignLayout?>.internal(
  SignLayoutConfig.new,
  name: r'signLayoutConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$signLayoutConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SignLayoutConfig = AutoDisposeNotifier<SignLayout?>;
String _$signLayoutBallHash() => r'8bf2cc6178f06a119c82f9035d2fdc70ec440a41';

/// See also [SignLayoutBall].
@ProviderFor(SignLayoutBall)
final signLayoutBallProvider = AutoDisposeNotifierProvider<SignLayoutBall,
    List<SignLayoutBallItem>?>.internal(
  SignLayoutBall.new,
  name: r'signLayoutBallProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$signLayoutBallHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SignLayoutBall = AutoDisposeNotifier<List<SignLayoutBallItem>?>;
String _$integralExchangeHash() => r'7dfb2e986f6ee3177262e769ee87ff1b17806f5a';

/// See also [IntegralExchange].
@ProviderFor(IntegralExchange)
final integralExchangeProvider =
    AutoDisposeNotifierProvider<IntegralExchange, List<ExchangeItem>?>.internal(
  IntegralExchange.new,
  name: r'integralExchangeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$integralExchangeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$IntegralExchange = AutoDisposeNotifier<List<ExchangeItem>?>;
String _$integralDetailListHash() =>
    r'8711aaadcc5c3ebb68cc7a7924a52d8e85dadab3';

/// See also [IntegralDetailList].
@ProviderFor(IntegralDetailList)
final integralDetailListProvider = AutoDisposeNotifierProvider<
    IntegralDetailList, IntegralDetailResult>.internal(
  IntegralDetailList.new,
  name: r'integralDetailListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$integralDetailListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$IntegralDetailList = AutoDisposeNotifier<IntegralDetailResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
