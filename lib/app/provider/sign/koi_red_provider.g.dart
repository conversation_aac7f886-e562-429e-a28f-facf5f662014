// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'koi_red_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchLuckGiftInfoHash() => r'f9a031e9c6f0a8cb72d23217ba8c7abf06114b09';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package: app.provider.sign
/// @ClassName: koi_red_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/14 16:58
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 16:58
/// @UpdateRemark: 更新说明
///
/// Copied from [FetchLuckGiftInfo].
@ProviderFor(FetchLuckGiftInfo)
final fetchLuckGiftInfoProvider =
    AutoDisposeNotifierProvider<FetchLuckGiftInfo, LuckGiftInfo?>.internal(
  FetchLuckGiftInfo.new,
  name: r'fetchLuckGiftInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchLuckGiftInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FetchLuckGiftInfo = AutoDisposeNotifier<LuckGiftInfo?>;
String _$receiveKoiRedPacketHash() =>
    r'9edbfee5c413b82d6e5b6dd061a723922f0ca0b5';

/// See also [ReceiveKoiRedPacket].
@ProviderFor(ReceiveKoiRedPacket)
final receiveKoiRedPacketProvider =
    AutoDisposeNotifierProvider<ReceiveKoiRedPacket, LuckGiftItem?>.internal(
  ReceiveKoiRedPacket.new,
  name: r'receiveKoiRedPacketProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$receiveKoiRedPacketHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ReceiveKoiRedPacket = AutoDisposeNotifier<LuckGiftItem?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
