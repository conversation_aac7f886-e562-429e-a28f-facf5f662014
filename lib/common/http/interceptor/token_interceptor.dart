import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import '../../../app/navigation/coosea.dart';
import '../../../app/navigation/router.dart';
import '../../../config/global_config.dart';
import '../base/base_response.dart';
import 'utils.dart';

/// Lock and UnLock Will delete in v5.0. Use `QueuedInterceptor` instead, more detail see'
/// ' https://github.com/flutterchina/dio/issues/1308
/// 如果同时发起多个请求会重复刷新token，所以使用QueuedInterceptors
class TokenInterceptor extends QueuedInterceptorsWrapper {
  /// token过期后台返回5003
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    BaseResponse br = BaseResponse.fromJson(response.data, (json) => null);

    /// token 过期
    debugPrint("TokenInterceptor-onResponse: ${br.errorCode}");
    if (InterceptorUtil.isTokenExpired(br)) {
      if (GlobalConfig.currentRouter != CsRouter.login) {
        navigatorKey.currentState?.pushNamed(CsRouter.login);
      }
    }

    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    try {
      if (err.response != null &&
          err.response!.statusCode != null &&
          err.response!.data != null &&
          err.response!.statusCode != 404) {
        BaseResponse br =
            BaseResponse.fromJson(err.response!.data, (json) => null);
        debugPrint("TokenInterceptor-onError: ${br.errorCode}");

        /// token 过期,跳转到登录页
        if (InterceptorUtil.isTokenExpired(br)) {
          if (GlobalConfig.currentRouter != CsRouter.login) {
            navigatorKey.currentState?.pushNamed(CsRouter.login);
          }
        }
      }
      super.onError(err, handler);
    } catch (e) {
      super.onError(err, handler);
    }
  }
}
