import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../../config/global_config.dart';

/// 利用请求拦截器在options的headers中加入token
class RequestInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final info = <String, dynamic>{};

    // Preserve existing content-type when explicitly provided.
    if (!options.headers.containsKey('Content-Type')) {
      info['Content-Type'] = 'application/json';
    }

    final isFreeLunchHost = options.uri.host.contains('freelunchapi');
    if (isFreeLunchHost) {
      final token = GlobalConfig.bwcToken;
      if (token != null && token.isNotEmpty) {
        info['Authorization'] = token;
      }
    } else {
      final accessToken = GlobalConfig.account?.token;
          // String? accessToken =
          //     "eyJhbGciOiJIUzUxMiJ9.***********************************************************************************.MU6MEvoyGGdEShHfFofMol8wsaUa3SqO2h3P-BNDPkdm-y5Ccd5W4ajTz6uNR9i9HWUosDtFqR8DF6whmtK8FQ";
      debugPrint('RequestInterceptor: $accessToken');
      if (accessToken != null && accessToken.isNotEmpty) {
        info['Authorization'] = accessToken;
      }
    }

    final locale = GlobalConfig.localeInfo;
    if (locale != null) {
      if (locale.scriptCode != null) {
        info['Locale'] = '${locale.languageCode}_${locale.scriptCode}';
      } else if (locale.languageCode == 'zh') {
        info['Locale'] = '${locale.languageCode}_CN';
      } else if (locale.languageCode == 'en') {
        info['Locale'] = '${locale.languageCode}_US';
      } else {
        info['Locale'] = locale.languageCode;
      }
    }

    options.headers.addAll(info);
    super.onRequest(options, handler);
  }
}
