import '../base/base_response.dart';

class InterceptorUtil {

  // 是否是token过期
  static bool isTokenExpired(BaseResponse br) {
    return br.code == 1001 ||
        br.code == "1001" ||
        br.code == 1002 ||
        br.code == "1002" ||
        br.errorCode == 1001 ||
        br.errorCode == "1001" ||
        br.errorCode == 1002 ||
        br.errorCode == "1002" ||
        br.msg == "用户不存在" ||
        br.msg == "user not exists";
  }
}
