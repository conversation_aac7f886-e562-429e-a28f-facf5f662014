/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: tab_icon_addres
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/17 15:52
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/17 15:52
/// @UpdateRemark: 更新说明

const assetsBase = "https://ecocdn.szprize.cn/images";

const signIng = "$assetsBase/sign/sign_ing.png";
const signRaw = "$assetsBase/sign/sign_raw.png";
const signDay = "$assetsBase/sign/sign_day.png";
const signBag = "$assetsBase/sign/sign_bag.png";
const signIngBg = "$assetsBase/sign/sign_ing_bg.png";

const delete = "$assetsBase/base/delete.png";

const searchEmpty = "$assetsBase/search/search_empty.png";

const billAmountBg = "$assetsBase/bill/bill_amount_bg.png";

/// 识别弹窗
const convertFailBg = "$assetsBase/main/convert_fail_bg.png";

const orderDetailBg = "$assetsBase/bill/order_detail_bg.png";

const serviceDialogBg = "$assetsBase/base/service_title_bg.png";

const alipayIcon = "$assetsBase/bill/alipay_icon.png";

const withdrawalSuccess = "$assetsBase/bill/withdrawal_success.png";
const withdrawalFail = "$assetsBase/bill/withdrawal_fail.png";
const withdrawalFailTap = "$assetsBase/bill/withdrawal_fail_tap.png";

const withdrawalEmpty = "$assetsBase/bill/withdrawal_empty.png";
const billEmpty = "$assetsBase/bill/bill_empty.png";
const orderEmpty = "$assetsBase/bill/order_empty.png";

/// ===assets img========

///我的页
const String rightArrowImg = "$assetsBase/personal/right_arrow_img.png";
const String orderIconImg = "$assetsBase/personal/order_icon_img.png";
const String earnMoneyIconImg = "$assetsBase/personal/earn_money_icon_img.png";
const String consumerPhoneIcon = "$assetsBase/personal/consumer_phone.png";

/// 拼多多二级页面
const pddActivityBg = "$assetsBase/base/pdd_activity_bg.png";
const pddDescBanner = "$assetsBase/base/pdd_desc_banner.png";
const mySavingIcon = "$assetsBase/base/my_saving_icon.png";
const pddActivityMid = "$assetsBase/base/pdd_activity_mid.png";
const subsidy = "$assetsBase/base/subsidy.png";
const coupon = "$assetsBase/base/coupon.png";
const limited = "$assetsBase/base/limited.png";
const brand = "$assetsBase/base/brand.png";
const shipping = "$assetsBase/base/shipping.png";
const ticket = "$assetsBase/base/ticket.png";
const hotSelling = "$assetsBase/base/hot_selling.png";
const eleAnimal = "$assetsBase/base/ele_animal.png";
const mtAnimal = "$assetsBase/base/mt_animal.png";
const jdAnimal = "$assetsBase/base/jd_animal.png";
const tbAnimal = "$assetsBase/base/tb_animal.png";
const pddAnimal = "$assetsBase/base/pdd_animal.png";
const recharge = "$assetsBase/pdd_activity/jiayongdianqi.png";

/// 京东二级页面
const jdActivityBg = "$assetsBase/jd_activity/jd_activity_bg.png";
const jdDescBanner = "$assetsBase/jd_activity/jd_desc_banner.png";
const jdActivityMid = "$assetsBase/jd_activity/jd_activity_mid.png";
const jdAppliances = "$assetsBase/jd_activity/appliances.png";
const jdFresh = "$assetsBase/jd_activity/fresh.png";
const jdGift = "$assetsBase/jd_activity/gift.png";
const jdSubsidy = "$assetsBase/jd_activity/subsidy.png";

/// 淘宝二级页面
const tbActivityBg = "$assetsBase/tb_activity/tb_activity_bg.png";
const tbDescBanner = "$assetsBase/tb_activity/tb_desc_banner.png";
const tbActivityMid = "$assetsBase/tb_activity/tb_activity_mid.png";
const tbReduction = "$assetsBase/tb_activity/reduction.png";
const tbPreferred = "$assetsBase/tb_activity/preferred.png";
const tbFreeShipping = "$assetsBase/tb_activity/free_shipping.png";
const tbSubsidy = "$assetsBase/tb_activity/subsidy.png";

/// 0元购
const homeZeroTitle = "$assetsBase/zero_purchase/home_zero_title.png";
const homeZeroBg = "$assetsBase/zero_purchase/home_zero_bg.png";
const homeZeroBtn = "$assetsBase/zero_purchase/home_zero_btn.png";
const freeBuyTitleBg = "$assetsBase/zero_purchase/free_buy_title_bg.png";
const freeBuyTag = "$assetsBase/zero_purchase/free_buy_tag.png";

/// =================================assets====================================
const titleTutorial = "assets/images/title_tutorial.png";
const guideOne = "assets/images/guide_one.png";
const guideTwo = "assets/images/guide_two.png";
const reminderTitleBg = "assets/images/reminder_title_bg.png";
const cleanReminder = "assets/images/clean_reminder.png";
const ordinaryClose = "assets/images/ordinary_close.png";
const sidebarClose = "assets/images/close_sidebar.png";
const cancel = "assets/images/cancel.png";
const myTutorial = "assets/images/my_tutorial.png";
const myFansIncome = "assets/images/my_fans_income.png";
const historyManage = "assets/images/history_manage.png";
const collection = "assets/images/collection.png";
const uncollection = "assets/images/uncollection.png";

/// tabbar
const tabBarMain = "assets/images/tabbar/tab_bar_main.png";
const tabBarMainActive = "assets/images/tabbar/tab_bar_main_active.png";
const tabBarSignActive = "assets/images/tabbar/tab_bar_sign_active.png";
const tabBarSign = "assets/images/tabbar/tab_bar_sign_tab.png";
const tabBarFun = "assets/images/tabbar/icon_bar_fun2.png";
const tabBarFunActive = "assets/images/tabbar/icon_bar_fun.png";
const tabBarMe = "assets/images/tabbar/tab_bar_me.png";
const tabBarMeActive = "assets/images/tabbar/tab_bar_me_active.png";

const noLoginAvatar = "assets/images/head.png";
const cleanSearchHistory = "assets/images/clean_history.png";
const vipHeaderIcon = "assets/images/vip_header_icon.png";
const closeBlack = "assets/images/close_black.png";
const signCoin = "assets/images/sign_coin.png";
const jfBc = "assets/images/sign/jfBc.png";
const jfBcLeft = "assets/images/sign/jfBcLeft.png";
const jfBcRight = "assets/images/sign/jfBcRight.png";

const searchIcon = "assets/images/searchIcon.png";
const back = "assets/images/back.png";
const myOrderIcon = "assets/images/my_dd.png";

const splashBottomLogo = "assets/images/splash_bottom_logo.png";

const close = "assets/images/close.png";

const progressIconImg = "assets/images/progress_icon_img.png";
const hbpzIconImg = "assets/images/hbpz_icon_img.png";
const checkIconImg = "assets/images/check_icon_img.png";
const bwcIcon = "assets/images/bwc_icon_img.png";
const cjwtIcon = "assets/images/cjwt_icon_img.png";
const funActivityLoading = "assets/images/fun_activity_loading.gif";