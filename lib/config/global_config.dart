import 'package:flutter/material.dart';

import '../app/repository/modals/account/account.dart';
import '../app/repository/modals/account/bwc_token.dart';
import '../common/http/http_utils.dart';
import '../utils/prefs_util.dart';
import 'config.dart';
import 'constant.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: global_config
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/17 15:16
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/17 15:16
/// @UpdateRemark: 更新说明
class GlobalConfig {
  /// 是否是release版
  static bool get isRelease => const bool.fromEnvironment("dart.vm.product");

  /// 用户信息
  static Account? account;

  /// 当前语言环境
  static Locale? localeInfo;

  /// 用户协议
  static bool? agreePrivacy;

  /// 当前路由
  static String? currentRouter;

  /// 是否开启代理(打包时选择不同配置关闭或者开启代理)
  static bool enableProxy = Config.vm;

  /// 霸王餐toke
  static String? bwcToken;
  static int? bwcUserId;

  static late String baseUrl;

  /// 霸王餐域名
  static late String freeLunchBaseUrl;

  /// 初始化全局信息，会在APP启动时执行
  static Future init() async {
    /// 初始化本地存储
    await PrefsUtil().init();

    final bool useReleaseBase;
    if (enableProxy) {
      final isTest = PrefsUtil().getBool(PrefsKeys.serverWitchKey);
      useReleaseBase = isTest == false;
    } else {
      useReleaseBase = true;
    }

    baseUrl = useReleaseBase ? Constant.releaseBaseUrl : Constant.devBaseUrl;
    freeLunchBaseUrl = useReleaseBase
        ? Constant.releaseFreeLunchBaseUrl
        : Constant.devFreeLunchBaseUrl;

    HttpUtils.init(baseUrl: baseUrl);

    /// 获取用户信息
    var userInfo = PrefsUtil().getJSON("account");
    debugPrint("userInfo----: $userInfo");
    if (userInfo != null) {
      account = Account.fromJson(userInfo);
    }

    final bwcTokenJson = PrefsUtil().getJSON(PrefsKeys.bwcTokenKey);
    if (bwcTokenJson != null) {
      try {
        final map = Map<String, dynamic>.from(bwcTokenJson);
        final stored = BwcToken.fromJson(map);
        bwcToken = stored.authorization;
        bwcUserId = stored.userId;
        if (account != null) {
          account!.bwcToken = bwcToken;
          account!.bwcUserId = bwcUserId;
        }
      } catch (_) {
        bwcToken = null;
        bwcUserId = null;
      }
    }

    agreePrivacy = PrefsUtil().getBool(PrefsKeys.agreePrivacy);
  }

  /// 持久化用户信息
  static saveProfile() => PrefsUtil().setJSON("account", account?.toJson());
}
