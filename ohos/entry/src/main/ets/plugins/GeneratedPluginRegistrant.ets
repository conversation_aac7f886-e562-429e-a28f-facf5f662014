import { FlutterEngine, Log } from '@ohos/flutter_ohos';
import InAppWebViewFlutterPlugin from 'flutter_inappwebview_ohos';
import ImageGallerySaverPlugin from 'image_gallery_saver';
import LocationPlugin from 'location';
import PackageInfoPlugin from 'package_info_plus';
import SharedPreferencesPlugin from 'shared_preferences_ohos';
import UrlLauncherPlugin from 'url_launcher_ohos';
import VideoPlayerPlugin from 'video_player_ohos';

/**
 * Generated file. Do not edit.
 * This file is generated by the Flutter tool based on the
 * plugins that support the Ohos platform.
 */

const TAG = "GeneratedPluginRegistrant";

export class GeneratedPluginRegistrant {

  static registerWith(flutterEngine: FlutterEngine) {
    try {
      flutterEngine.getPlugins()?.add(new InAppWebViewFlutterPlugin());
      flutterEngine.getPlugins()?.add(new ImageGallerySaverPlugin());
      flutterEngine.getPlugins()?.add(new LocationPlugin());
      flutterEngine.getPlugins()?.add(new PackageInfoPlugin());
      flutterEngine.getPlugins()?.add(new SharedPreferencesPlugin());
      flutterEngine.getPlugins()?.add(new UrlLauncherPlugin());
      flutterEngine.getPlugins()?.add(new VideoPlayerPlugin());
    } catch (e) {
      Log.e(
        TAG,
        "Tried to register plugins with FlutterEngine ("
          + flutterEngine
          + ") failed.");
      Log.e(TAG, "Received exception while registering", e);
    }
  }
}
