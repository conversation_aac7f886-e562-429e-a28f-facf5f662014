{"app": {"signingConfigs": [{"name": "default1", "type": "HarmonyOS", "material": {"certpath": "/Users/<USER>/.ohos/config/default1_ohos_Zv66goCswmU88etCh_VFEEfnV7TPm3n0FYXJXHTXXfE=.cer", "storePassword": "0000001B816AA9C3E47D1A6D4AFA63E30FD226A7FFD5D181AD1508408A163B3A0F73F26A5BC13CB7D5F4F6", "keyAlias": "debugKey", "keyPassword": "0000001BC1AE26A163017C9EEC405B02B0DDF0EA2DDD6DEF5E60530535C7786C1C67ED261B369366B8C78B", "profile": "/Users/<USER>/.ohos/config/default1_ohos_Zv66goCswmU88etCh_VFEEfnV7TPm3n0FYXJXHTXXfE=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "/Users/<USER>/.ohos/config/default1_ohos_Zv66goCswmU88etCh_VFEEfnV7TPm3n0FYXJXHTXXfE=.p12"}}], "products": [{"name": "default", "signingConfig": "default1", "compatibleSdkVersion": "5.0.0(12)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"useNormalizedOHMUrl": true}}}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}]}