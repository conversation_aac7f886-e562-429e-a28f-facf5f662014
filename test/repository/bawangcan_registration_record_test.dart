import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:msmds_platform/app/repository/modals/fun/bawangcan_registration_record.dart';

void main() {
  group('BawangcanRegistrationRecord Tests', () {
    test('应该正确解析完整的 JSON 数据', () {
      const jsonString = '''
      {
        "id": 25484,
        "channelId": 1,
        "userId": 549168,
        "mobile": "13377776666",
        "applyId": "1029363214300839936",
        "applyOrderNo": "20251011120154561429229",
        "orderNo": null,
        "expireTime": "2025-10-11 12:31:55",
        "state": 3,
        "activityId": "917827229379174401",
        "shopId": "R5bPu6i%2FNUjMW6KsYC27xg%3D%3D",
        "createTime": "2025-10-11 12:01:55",
        "updateTime": "2025-10-11 12:01:55",
        "applyOrderShop": {
          "id": 25405,
          "applyOrderId": null,
          "activityId": "917827229379174401",
          "shopId": "R5bPu6i%2FNUjMW6KsYC27xg%3D%3D",
          "shopName": "LINLEE林里·手打柠檬茶（王府井喜悦店）",
          "shopImg": "https://p1.meituan.net/waimaipoi.jpg",
          "applyType": 1,
          "commissionType": 1,
          "activityType": 1,
          "commissionRate": 21.6,
          "commission": null,
          "commissionThresholdCent": null,
          "maxCommission": 14.4,
          "officialCommissionRate": null,
          "officialCommission": null,
          "officialMaxCommission": null,
          "actionUrl": {
            "dpUrl": "",
            "h5Url": "https://h5.waimai.meituan.com/waimai/mindex/menu",
            "wxPath": "",
            "wxAppId": "",
            "wxAppOrgId": "gh_72a4eb2d4324"
          },
          "createTime": "2025-10-11 12:01:55",
          "updateTime": null
        }
      }
      ''';

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final record = BawangcanRegistrationRecord.fromJson(json);

      // 验证基本字段
      expect(record.id, 25484);
      expect(record.channelId, 1);
      expect(record.userId, 549168);
      expect(record.mobile, "13377776666");
      expect(record.applyId, "1029363214300839936");
      expect(record.applyOrderNo, "20251011120154561429229");
      expect(record.orderNo, null);
      expect(record.expireTime, "2025-10-11 12:31:55");
      expect(record.state, 3);
      expect(record.activityId, "917827229379174401");
      expect(record.shopId, "R5bPu6i%2FNUjMW6KsYC27xg%3D%3D");
      expect(record.createTime, "2025-10-11 12:01:55");
      expect(record.updateTime, "2025-10-11 12:01:55");

      // 验证嵌套对象
      expect(record.applyOrderShop, isNotNull);
      final shop = record.applyOrderShop!;
      expect(shop.id, 25405);
      expect(shop.shopName, "LINLEE林里·手打柠檬茶（王府井喜悦店）");
      expect(shop.shopImg, "https://p1.meituan.net/waimaipoi.jpg");
      expect(shop.applyType, 1);
      expect(shop.commissionType, 1);
      expect(shop.activityType, 1);
      expect(shop.commissionRate, 21.6);
      expect(shop.commission, null);
      expect(shop.maxCommission, 14.4);

      // 验证 ActionUrl
      expect(shop.actionUrl, isNotNull);
      final actionUrl = shop.actionUrl!;
      expect(actionUrl.dpUrl, "");
      expect(actionUrl.h5Url, "https://h5.waimai.meituan.com/waimai/mindex/menu");
      expect(actionUrl.wxPath, "");
      expect(actionUrl.wxAppId, "");
      expect(actionUrl.wxAppOrgId, "gh_72a4eb2d4324");
    });

    test('应该正确解析响应数据', () {
      const jsonString = '''
      {
        "total": 1,
        "list": [
          {
            "id": 25484,
            "channelId": 1,
            "userId": 549168,
            "mobile": "13377776666",
            "applyId": "1029363214300839936",
            "applyOrderNo": "20251011120154561429229",
            "orderNo": null,
            "expireTime": "2025-10-11 12:31:55",
            "state": 3,
            "activityId": "917827229379174401",
            "shopId": "R5bPu6i%2FNUjMW6KsYC27xg%3D%3D",
            "createTime": "2025-10-11 12:01:55",
            "updateTime": "2025-10-11 12:01:55",
            "applyOrderShop": {
              "id": 25405,
              "shopName": "LINLEE林里·手打柠檬茶（王府井喜悦店）",
              "shopImg": "https://p1.meituan.net/waimaipoi.jpg",
              "applyType": 1,
              "commissionType": 1,
              "activityType": 1,
              "commissionRate": 21.6,
              "commission": null,
              "maxCommission": 14.4,
              "actionUrl": {
                "dpUrl": "",
                "h5Url": "https://h5.waimai.meituan.com/waimai/mindex/menu",
                "wxPath": "",
                "wxAppId": "",
                "wxAppOrgId": "gh_72a4eb2d4324"
              }
            }
          }
        ],
        "pageNum": 1,
        "pageSize": 10,
        "size": 1,
        "hasNextPage": false
      }
      ''';

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final response = BawangcanRegistrationRecordResponse.fromJson(json);

      expect(response.total, 1);
      expect(response.hasNextPage, false);
      expect(response.list, isNotNull);
      expect(response.list!.length, 1);

      final record = response.list!.first;
      expect(record.id, 25484);
      expect(record.applyOrderNo, "20251011120154561429229");
      expect(record.state, 3);
    });

    test('应该正确处理空值', () {
      const jsonString = '''
      {
        "id": null,
        "channelId": null,
        "userId": null,
        "mobile": null,
        "applyId": null,
        "applyOrderNo": null,
        "orderNo": null,
        "expireTime": null,
        "state": null,
        "activityId": null,
        "shopId": null,
        "createTime": null,
        "updateTime": null,
        "applyOrderShop": null
      }
      ''';

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final record = BawangcanRegistrationRecord.fromJson(json);

      expect(record.id, null);
      expect(record.channelId, null);
      expect(record.userId, null);
      expect(record.mobile, null);
      expect(record.applyId, null);
      expect(record.applyOrderNo, null);
      expect(record.orderNo, null);
      expect(record.expireTime, null);
      expect(record.state, null);
      expect(record.activityId, null);
      expect(record.shopId, null);
      expect(record.createTime, null);
      expect(record.updateTime, null);
      expect(record.applyOrderShop, null);
    });

    test('JSON 序列化应该正常工作', () {
      const record = BawangcanRegistrationRecord(
        id: 123,
        channelId: 1,
        userId: 456,
        mobile: "13800138000",
        applyId: "test_apply_id",
        applyOrderNo: "test_order_no",
        state: 3,
        activityId: "test_activity_id",
        shopId: "test_shop_id",
        createTime: "2025-01-01 00:00:00",
        updateTime: "2025-01-01 00:00:00",
      );

      final json = record.toJson();
      expect(json['id'], 123);
      expect(json['channelId'], 1);
      expect(json['userId'], 456);
      expect(json['mobile'], "13800138000");
      expect(json['applyId'], "test_apply_id");
      expect(json['applyOrderNo'], "test_order_no");
      expect(json['state'], 3);

      final fromJson = BawangcanRegistrationRecord.fromJson(json);
      expect(fromJson.id, record.id);
      expect(fromJson.channelId, record.channelId);
      expect(fromJson.userId, record.userId);
      expect(fromJson.mobile, record.mobile);
      expect(fromJson.applyId, record.applyId);
      expect(fromJson.applyOrderNo, record.applyOrderNo);
      expect(fromJson.state, record.state);
    });
  });

  group('BawangcanActionUrl Tests', () {
    test('应该正确解析所有字段', () {
      const jsonString = '''
      {
        "dpUrl": "https://example.com/dp",
        "h5Url": "https://example.com/h5",
        "wxPath": "/pages/index",
        "wxAppId": "wx123456789",
        "wxAppOrgId": "gh_abcdefg"
      }
      ''';

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final actionUrl = BawangcanActionUrl.fromJson(json);

      expect(actionUrl.dpUrl, "https://example.com/dp");
      expect(actionUrl.h5Url, "https://example.com/h5");
      expect(actionUrl.wxPath, "/pages/index");
      expect(actionUrl.wxAppId, "wx123456789");
      expect(actionUrl.wxAppOrgId, "gh_abcdefg");
    });
  });
}
