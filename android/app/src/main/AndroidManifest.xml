<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.msmds_platform">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>

    <uses-permission android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.BLUETOOTH"
        tools:node="merge"/>
    <uses-permission android:name="android.permission.READ_SETTINGS"
        tools:node="merge"/>
    <uses-permission android:name="android.permission.GET_TASKS"
        tools:node="merge"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
    <queries>
        <!-- 使app可以发现京东app -->
        <package android:name="com.jingdong.app.mall" />
        <!-- 使app可以发现拼多多app -->
        <package android:name="com.xunmeng.pinduoduo" />
        <!-- 使app可以发现饿了么app -->
        <package android:name="me.ele" />
        <!-- 使app可以发现美团app&美团外卖 -->
        <package android:name="com.sankuai.meituan" />
        <package android:name="com.sankuai.meituan.takeoutnew" />
        <!-- 使app可以发现微信app -->
        <package android:name="com.tencent.mm" />
        <!-- 使app可以发现淘宝app和天猫 -->
        <package android:name="com.taobao.taobao" />
        <package android:name="com.tmall.wireless" />

        <!-- 酷赛浏览器 -->
        <package android:name="com.prize.browser" />

        <!-- If your app checks for call support -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="tel" />
        </intent>
    </queries>

    <application
        android:label="@string/app_name"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:networkSecurityConfig="@xml/network_security_config">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Accepts URIs that begin with "flkc.msmds://virtual” -->
                <data android:scheme="flkc.msmds"
                    android:host="virtual" />
            </intent-filter>

        </activity>

        <activity
            android:name="com.alibaba.alibclinkpartner.smartlink.ALPEntranceActivity"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="true">
            <intent-filter>
                <action android:name="com.alibaba.intent.action.GETWAY" />

                <category android:name="android.intent.category.DEFAULT" />

                <data
                    android:host="linkpartner"
                    android:pathPrefix="/entrance"
                    android:scheme="tbopen" />
            </intent-filter>
        </activity>

        <activity
            android:exported="true"
            android:name="com.taobao.avplayer.playercontrol.hiv.DialogActivity"
            android:screenOrientation="landscape"
            android:theme="@android:style/Theme.Black.NoTitleBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="hiv.taobao.com"
                    android:path="/dialog"
                    android:scheme="http" />
                <data
                    android:host="hiv.taobao.com"
                    android:path="/dialog"
                    android:scheme="https" />
            </intent-filter>
        </activity>

        <receiver
            android:name="com.alibaba.alibclogin.listener.LoginBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="NOTIFY_LOGIN_SUCCESS" />
                <action android:name="NOTIFY_LOGIN_FAILED" />
                <action android:name="NOTIFY_LOGIN_CANCEL" />
                <action android:name="NOTIFY_LOGOUT" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.alibaba.alibcprotocol.NetworkChangeReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="android.net.wifi.WIFI_STATE_CHANGED" />
                <action android:name="android.net.wifi.STATE_CHANGE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.alibaba.triver.TRACCSService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.taobao.accs.intent.action.RECEIVE" />
            </intent-filter>
        </service>

        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>
